<script setup lang="ts">
import type { VxeGridProps } from "#/adapter/vxe-table";
import TableTemplate from "#/components/table/index.vue";
import {
  getLastNDaysRange,
  transformTimeFields,
  formatTimestampToDate,
} from "#/utils/time";
import { mapToOptions } from "#/utils/transform";
import { getSearchResetHomeUserLog } from "#/api/operateLog";
import {
  ID_TYPE_MAP,
  RESET_TYPE_MAP,
  KEYWORD_LEVEL_MAP,
} from "#/constants/maps/operate-log";
import { findLabelByValue } from "#/utils/data";
import useForbidStore from "#/store/forbid";
import { NO_LIMIT_NUM } from "#/constants/num";

import { storeToRefs } from "pinia";
const forbidStore = useForbidStore();
const { allForbidTags } = storeToRefs(forbidStore);
const searchOptions = {
  collapsed: false,
  schemas: {
    "1": [
      {
        component: "Select",
        fieldName: "home_user_id_type",
        label: "id类型",
        componentProps: {
          options: mapToOptions(ID_TYPE_MAP),
        },
      },
      {
        component: "Input",
        fieldName: "home_user_id",
        label: "id",
      },
      {
        component: "Select",
        fieldName: "scope",
        label: "重置类型",
        componentProps: {
          options: mapToOptions(RESET_TYPE_MAP),
        },
      },
      {
        component: "Select",
        fieldName: "first_type",
        label: "重置原因",
        componentProps: {
          options: mapToOptions(KEYWORD_LEVEL_MAP),
        },
      },
      {
        component: "Input",
        fieldName: "reviewer",
        label: "操作人",
      },
      {
        component: "RangePicker",
        fieldName: "dateRange",
        label: "时间",
        class: "col-span-2",
        defaultValue: getLastNDaysRange(14, 0, "X"),
        componentProps: {
          valueFormat: "X",
          showTime: true,
        },
      },
    ],
  },
  showCollapseButton: true,
  submitOnChange: false,
  wrapperClass: "grid-cols-5",
};

const columns: VxeGridProps["columns"] = [
  {
    field: "user_id_type",
    title: "id类型",
    formatter: ({ cellValue }) => ID_TYPE_MAP[cellValue],
  },
  {
    field: "user_id",
    title: "id",
  },
  {
    field: "scope",
    title: "重置类型",
    formatter: ({ cellValue }) => RESET_TYPE_MAP[cellValue],
  },
  {
    field: "type",
    title: "重置原因",
    formatter: ({ cellValue }) =>
      findLabelByValue(allForbidTags.value, cellValue, "type", "name"),
  },
  {
    field: "nowtime",
    title: "操作时间",
    formatter: ({ cellValue }) => formatTimestampToDate(cellValue),
  },
];
const getData = async ({ page, pageSize, scene, ...formValues }: any) => {
  let params: any = {
    page: page - 1,
    size: pageSize,
    ...transformTimeFields(formValues, [["dateRange", ["start_time", "end_time"]]]),
  };

  const res = await getSearchResetHomeUserLog(params);
  const items = data?.data?.logs || [];
  return { items, total: items.length == pageSize ? NO_LIMIT_NUM : 0 };
};
const paginationOptions = {
  pageSize: 20,
  currentPage: 1,
  layouts: ["PrevPage", "NextPage"],
};
</script>

<template>
  <table-template
    :pagination-options="paginationOptions"
    :columns="columns"
    :search-options="searchOptions"
    :query-method="getData"
  />
</template>
