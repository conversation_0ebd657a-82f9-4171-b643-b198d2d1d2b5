<script setup lang="ts">
import TableTemplate from '#/components/table/index.vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { computed, reactive, ref, shallowRef, nextTick, onMounted } from 'vue';
import { useForbidList } from './violation';
import { Cascader } from 'ant-design-vue';
import { formatTimestampToDate, getLastNDaysRange } from '#/utils/time';
import dayjs from 'dayjs';
import ActionDropdown from '../components/ActionDropdown.vue';
import FormModalComponent from '#/components/modal/form-modal.vue';
import { useVbenModal } from '@vben/common-ui';
import { getHomeForbidUser, updateHomeForbidUser } from '#/api/black-list';
import { mapToOptions } from '#/utils/transform';
import { useApiHandler } from '#/composables/common/use-api-handler';
import { z } from '#/adapter/form';
import { storeToRefs } from 'pinia';
import { findLabelByValue } from '#/utils/data';
import useForbidStore from '#/store/forbid';
const forbidStore = useForbidStore();
const { allForbidTags } = storeToRefs(forbidStore);

const { handleApiCall } = useApiHandler();

const listQuery = ref<Record<string, any>>({
  // 搜索条件
  start_time: getLastNDaysRange(14, 0, 'x')[0], // 接口的定义这里的start_time 要比 end_time 大
  end_time: getLastNDaysRange(14, 0, 'x')[1],
});
const { forbidList, loadData } = useForbidList();
onMounted(() => {
  loadData();
});
const paginationOptions = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1,
});
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: FormModalComponent,
});
const tableForm = ref<any>(null);
const HOME_USER_TYPE_MAP: Record<string, any> = {
  wps_id: 'wps账号',
  bbs_id: '社区账号',
};
const toolbarOptions = {
  export: true,
};
const searchOptions = {
  collapsed: false,
  schemas: {
    1: [
      {
        component: 'Input',
        componentProps: {
          clearable: true,
        },
        fieldName: 'home_user_id',
        label: '社区账号id:',
      },
      {
        component: 'Select',
        componentProps: {
          clearable: true,
          options: mapToOptions(HOME_USER_TYPE_MAP),
        },
        fieldName: 'home_user_id_type',
        label: '账号类型:',
      },
      {
        component: 'Select',
        componentProps: {
          clearable: true,
          options: [
            {
              label: '封禁',
              value: '1',
            },
            {
              label: '解封',
              value: '2',
            },
          ],
        },
        fieldName: 'operate_type',
        label: '状态:',
      },
      {
        component: 'Cascader',
        componentProps: {
          options: forbidList,
          showCheckedStrategy: Cascader.SHOW_CHILD,
          showSearch: true,
          clearable: true,
          expandTrigger: 'hover',
          //   :show-all-levels="false"
          fieldNames: {
            label: 'name',
            value: 'type',
            children: 'children',
          },
        },
        fieldName: 'type',
        label: '违规类型:',
      },
      {
        component: 'DatePicker',
        componentProps: {
          showTime: { format: 'HH:mm:ss' },
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'x', // 使用时间戳格式
          placeholder: '开始时间',
          disabledDate: disabledStartDate,
        },
        defaultValue: listQuery.value.start_time.toString(),
        fieldName: 'start_time',
        label: '最新违规时间:',
      },
      {
        component: 'DatePicker',
        componentProps: {
          showTime: { format: 'HH:mm:ss' },
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'x', // 使用时间戳格式
          placeholder: '结束时间',
          disabledDate: disabledEndDate,
        },
        defaultValue: listQuery.value.end_time.toString(),
        fieldName: 'end_time',
        label: '至',
      },
    ],
  },
  showCollapseButton: false,
  submitOnChange: false,
  handleValuesChange: handleValuesChange,
};

const columns = ref<VxeGridProps['columns']>([
  {
    field: 'user_id',
    title: 'UserID',
    align: 'center',
  },
  {
    field: 'user_id_type',
    title: '账号类型',
    align: 'center',
    slots: {
      default: ({ row }) => {
        return HOME_USER_TYPE_MAP[row.user_id_type];
      },
    },
  },
  {
    field: 'forbid_count',
    title: '累计封禁次数',
    align: 'center',
  },
  {
    field: 'type',
    title: '最新违规类型',
    align: 'center',
    formatter: ({ cellValue }) =>
      findLabelByValue(allForbidTags.value, cellValue, 'type', 'name'),
  },
  {
    field: 'modify_time',
    title: '最新操作封禁时间',
    align: 'center',
    formatter: ({ cellValue }) => formatTimestampToDate(cellValue),
  },
  {
    field: 'forbid_time',
    title: '最新封禁天数',
    align: 'center',
    slots: {
      default: ({ row }) => {
        return row.forbid_time == '-1'
          ? '永久封禁'
          : row.forbid_time / (3600 * 24);
      },
    },
  },
  {
    field: 'remark',
    title: '备注',
    align: 'center',
  },
  {
    title: '状态',
    align: 'center',
    slots: { default: 'status' },
  },
  {
    title: '操作',
    align: 'center',
    slots: { default: 'operation' },
  },
]);
const modalTitle = ref('');
const modalConfig = reactive({
  fullscreenButton: false,
  title: '',
});
const initForm = {
  user_id: '',
  user_id_type: '',
  forbid_type: '1',
  forbid_time: null,
  type: null,
  remark: '',
};
const form = ref<Record<string, any>>({ ...initForm });
const formConfig = {
  commonConfig: {
    labelWidth: 100,
    wrapperClass: 'pr-6',
  },
  schema: computed(() => {
    const schema = [
      {
        component: 'Input',
        componentProps: {
          disabled: modalTitle.value === '编辑',
        },
        defaultValue: form.value.user_id,
        fieldName: 'user_id',
        label: 'userid',
        rules: 'required',
      },
      {
        component: 'Select',
        componentProps: {
          options: mapToOptions(HOME_USER_TYPE_MAP),
          class: 'w-full',
        },
        defaultValue: form.value.user_id_type,
        fieldName: 'user_id_type',
        label: '账号类型',
        help: ' *账号类型与userid需匹配',
        rules: 'required',
      },
      {
        component: 'Select',
        componentProps: {
          options: [
            {
              label: '限时封禁',
              value: '1',
            },
            {
              label: '永久封禁',
              value: '2',
            },
          ],
          class: 'w-full',
        },
        defaultValue: form.value.forbid_type,
        fieldName: 'forbid_type',
        label: '封禁类型',
        rules: 'required',
      },
      {
        component: 'InputNumber',
        componentProps: {
          class: 'w-full',
        },
        suffix: () => '天',
        defaultValue: form.value.forbid_time,
        fieldName: 'forbid_time',
        label: '封禁时长',
        rules: z.number({ message: '' }).int({
          message: '请输入整数',
        }),
        dependencies: {
        // 当 forbid_type 为 '2'（永久封禁）时，返回 false，销毁该项
        if(values) {
          return values.forbid_type !== '2';
        },
        // 只有 forbid_type 变动时才触发
        triggerFields: ['forbid_type'],
      },
        // .optional(),
      },
      {
        component: 'Cascader',
        componentProps: {
          options: forbidList,
          showCheckedStrategy: Cascader.SHOW_CHILD,
          showSearch: true,
          clearable: true,
          expandTrigger: 'hover',
          //   :show-all-levels="false"

          fieldNames: {
            label: 'name',
            value: 'type',
            children: 'children',
          },
          class: 'w-full',
        },
        defaultValue: form.value.type,
        fieldName: 'type',
        label: '违规类型:',
        rules: 'required',
      },
      {
        component: 'Textarea',
        defaultValue: form.value.remark,
        fieldName: 'remark',
        label: '封禁原因',
      },
    ];
    return schema;
  }),
};

function handleValuesChange(values: any) {
  console.log('values', values);

  listQuery.value.start_time = values.start_time;
  listQuery.value.end_time = values.end_time;
}

function disabledStartDate(time: dayjs.Dayjs) {
  const { end_time } = listQuery.value;
  const currentTime = time.valueOf();
  if (end_time) {
    return currentTime > end_time;
  }
  return currentTime > Date.now();
}
function disabledEndDate(time: dayjs.Dayjs) {
  const { start_time } = listQuery.value;
  const currentTime = time.valueOf();
  if (start_time) {
    return currentTime <= start_time || currentTime > Date.now();
  }
}

async function getList({
  page,
  pageSize,
  ...formValues
}: {
  page: number;
  pageSize: number;
  [key: string]: any;
}) {
  let init: any = {
    page: page - 1,
    limit: pageSize,
  };
  for (const i in formValues) {
    formValues[i] ? (init[i] = formValues[i]) : null;
  }
  init['start_time'] = Math.round(init['start_time'] / 1000);
  init['end_time'] = Math.round(init['end_time'] / 1000);
  init.home_user_id = init.home_user_id?.trim();
  if (init.type) init.type = init.type[init.type?.length - 1];
  const res = await getHomeForbidUser(init);
  if (res && res.result) {
    return {
      items: res.data.records || [],
      total: res.data.count,
    };
  }
}
// 根据 type 值在指定数据源中找到对应的路径数组
function findTypePathInData(dataSource: any[], typeValue: string | number): any[] | null {
  if (!dataSource || !typeValue) {
    return null;
  }

  // 使用工具函数查找路径，forbidList 使用 'type' 作为值字段，返回值路径数组
  const result = findLabelByValue(dataSource, typeValue, 'type', 'name', undefined, 'children', '/', true);
  return Array.isArray(result) ? result : null;
}

// 根据 type 值在 forbidList 中找到对应的路径数组
function findTypePathInForbidList(typeValue: string | number): any[] | null {
  return findTypePathInData(forbidList.value, typeValue);
}

async function handleCommand(command: number, rowData: any) {
  if (command === 1) {
    modalTitle.value = '编辑';
    formModalApi.setState({ title: modalTitle.value });
    form.value = JSON.parse(JSON.stringify(rowData));
    form.value.forbid_time =
      form.value.forbid_time > 0 ? form.value.forbid_time / 24 / 60 / 60 : -1;
    form.value.forbid_type = form.value.forbid_time == -1 ? '2' : '1';

    // 确保 forbidList 数据已加载
    if (!forbidList.value || forbidList.value.length === 0) {
      await loadData();
    }

    // 将 type 值转换为 Cascader 需要的路径数组
    if (form.value.type) {
      // 首先在 forbidList 中查找
      let typePath = findTypePathInForbidList(form.value.type);

      // 如果在 forbidList 中找不到，尝试在 allForbidTags 中查找
      if (!typePath && allForbidTags.value) {
        typePath = findTypePathInData(allForbidTags.value, form.value.type);
      }

      // 设置转换后的路径，如果找不到则保持原值
      form.value.type = typePath || form.value.type;
    }

    formModalApi.open();
  } else {
    releaseItem(rowData);
  }
}
async function releaseItem(row: any) {
  await handleApiCall(
    updateHomeForbidUser,
    {
      user_id_type: row.user_id_type,
      user_id: row.user_id,
      operate_type: 2,
    },
    { onSuccess: () => tableForm.value.refreshTable() },
  );
}

async function handleConfirm(values: any) {
  let { user_id, user_id_type, forbid_type, forbid_time, type, remark } =
    values;

  // 处理 type 值：如果是数组，取最后一个值；如果不是数组，直接使用
  let typeValue = type;
  if (Array.isArray(type) && type.length > 0) {
    typeValue = type[type.length - 1]; // 取数组的最后一个值
  }

  let params: Record<string, any> = {
    user_id: Number(user_id),
    user_id_type,
    type: typeValue,
    remark,
    operate_type: 1,
  };
  if (forbid_type == 1) {
    if (forbid_time) {
      params.forbid_time = forbid_time * 60 * 60 * 24;
    }
  } else {
    params.forbid_time = -1;
  }
  await handleApiCall(updateHomeForbidUser, params, {
    onSuccess: () => {
      formModalApi.close();
      tableForm.value.refreshTable();
    },
  });
}
function handleAdd() {
  form.value = { ...initForm };
  modalTitle.value = '新增';
  formModalApi.setState({ title: modalTitle.value });
  formModalApi.open();
}
</script>
<template>
  <div>
    <table-template
      ref="tableForm"
      :search-options="searchOptions"
      :columns="columns"
      :pagination-options="paginationOptions"
      :query-method="getList"
      :toolbar-options="toolbarOptions"
    >
      <template #toolbar-left>
        <a-button type="primary" @click="handleAdd"> 新增 </a-button>
      </template>
      <template #status="{ row }">
        <a-tag :color="row.operate_type == '2' ? 'success' : 'error'">{{
          row.operate_type == 1 ? '封禁' : '正常'
        }}</a-tag>
      </template>
      <template #operation="{ row }">
        <action-dropdown
          :row-data="row"
          @command="handleCommand"
        ></action-dropdown>
      </template>
    </table-template>

    <form-modal
      :form-config="formConfig"
      :modal-config="modalConfig"
      @on-confirm="handleConfirm"
    >
    </form-modal>
  </div>
</template>
