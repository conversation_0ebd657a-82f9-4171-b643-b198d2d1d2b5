<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps, VxeGridListeners } from '#/adapter/vxe-table';
import { DownloadOutlined } from '@ant-design/icons-vue';

import { computed, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

// TODO of Props
// 1. Data
// 2. Column
// 3. Search
// 4. Toolbar
// 5. Pagination
// 6. Sort
// 7. Export
// 8. Print

// Props
interface Props {
  // 表格上方的描述部分
  pageOptions?: {
    description: string;
    title: string;
  };
  // 表格标题部分
  titleOptions?: {
    help: string;
    title: string;
  };
  maxHeight?: number;
  tabOptions?: {
    enable: boolean;
    list: {
      key: string;
      title: string;
    }[];
  };
  // 搜索部分
  searchOptions?: {
    collapsed: boolean;
    schemas: Record<string, VbenFormProps['schema']>;
    showCollapseButton: boolean;
    submitOnChange: boolean;
    wrapperClass?: string;
    commonConfig?: VbenFormProps['commonConfig'];
  };
  // 工具栏部分
  toolbarOptions?: VxeGridProps['toolbarConfig'];
  // 导出部分
  exportOptions?: {};
  // 表格列部分
  columns: VxeGridProps['columns'];
  // 查询方法
  queryMethod?: (params: any) => Promise<any>;
  // 分页部分
  paginationOptions?: {
    currentPage?: number;
    pageSize?: number;
    enabled?: boolean;
  };
  defaultActiveKey?: string;
  treeConfig?: VxeGridProps['treeConfig'];
  sortConfig?: any;
  extraConfig?: any;
  submitButtonOptions?: any;
  resetButtonOptions?: any;
  rowClassName?: VxeGridProps['rowClassName'];
  spanMethod?: VxeGridProps['spanMethod'];
  showHeader?: boolean; // 是否显示表头
  hasExport?: boolean; // 是否显示导出按钮
  data?: {
    items: any[]; // 数据列表
    total: number; // 数据总数
  };
  autoContent?: boolean;
  clearSpaces?: boolean;
}
const currentPage = ref(1);
const props = withDefaults(defineProps<Props>(), {
  pageOptions: () => ({
    title: '',
    description: '',
  }),
  titleOptions: () => ({
    title: '',
    help: '',
  }),
  tabOptions: () => ({
    enable: false,
    list: [],
  }),
  searchOptions: () => ({
    collapsed: false,
    schemas: {},
    showCollapseButton: true,
    submitOnChange: false,
  }),
  columns: () => [],
  data: () => ({
    items: [],
    total: 0,
  }),
  paginationOptions: () => ({
    pageSize: 10,
    currentPage: 1,
  }),
  toolbarOptions: () => ({
    refresh: false,
    zoom: false,
    custom: false,
    export: false, // 默认不展示导出
  }),
  defaultActiveKey: '1',
  exportOptions: () => ({
    filename: '', // filename 由页面动态传入
    type: 'csv',
    isHeader: true,
    original: false,
    useLabel: true,
    message: true,
    encoding: 'utf8',
    useTitleAsName: true,
  }),
  queryMethod: () => Promise.resolve({}),
  spanMethod: undefined,
  showHeader: true,
  hasExport: false,
  autoContent: true,
  clearSpaces: false,
});

// 定义标签页数据
const tabList = computed(() => {
  return props.tabOptions.list.map((item) => ({
    key: item.key,
    title: item.title,
  }));
});
console.log(props.data.items);
// 定义默认选中的标签页
const defaultActiveKey = ref(
  props.searchOptions?.schemas?.[props.defaultActiveKey]?.length > 0
    ? props.defaultActiveKey
    : '1',
);

// 在wrapperClass中添加gap
const newWrapperClass = computed(() => {
  return props.searchOptions?.wrapperClass
    ? `${props.searchOptions.wrapperClass} gap-x-3`
    : 'grid-cols-4 gap-x-3';
});

// 修改 formOptions 的定义和初始化
const formOptions = computed<VbenFormProps>(() => {
  return {
    ...props.searchOptions,
    submitButtonOptions: props.submitButtonOptions,
    resetButtonOptions: props.resetButtonOptions,
    wrapperClass: newWrapperClass.value,
    schema: props.searchOptions?.schemas?.[defaultActiveKey.value] || [],
    commonConfig: {
      componentProps: {
        allowClear: true,
        ...(props.clearSpaces && {
          onKeypress: (event: KeyboardEvent) => {
            if (event.key === ' ') {
              event.preventDefault();
            }
          },
        }),
      },
    },
  };
});
const gridEvents: VxeGridListeners<any> = {
  pageChange({ currentPage: newPage }) {
    currentPage.value = newPage;
  },
};
const showSearch = computed(() => {
  return Object.keys(props.searchOptions?.schemas ?? {}).length > 0;
});
// console.log(props.columns);
const gridOptions: VxeGridProps = {
  // height: "100%",
  maxHeight: props.maxHeight ? props.maxHeight : '100%',
  autoResize: true, // 内容变化时自动调整高度
  rowConfig: {
    isHover: true, // 悬停高亮
    height: 'auto', // 关键配置：行高自适应
    wrap: true, // 自动换行
  },

  showOverflow: false, // 禁用自动省略号（重要）
  columns: props.columns,
  pagerConfig: props.paginationOptions,
  toolbarConfig: props.toolbarOptions,
  sortConfig: props.sortConfig,
  exportConfig: props.exportOptions,
  spanMethod: props.spanMethod,
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        let res =
          props.data.items.length > 0
            ? props.data
            : await props.queryMethod({
                page: page.currentPage,
                pageSize: page.pageSize,
                ...formValues,
              });
        return res;
      },
    },
    props: {
      result: 'items',
      total: 'total',
    },
  },
  treeConfig: props.treeConfig,
  showHeader: props.showHeader,
  rowClassName: props.rowClassName,
  ...props.extraConfig,
};

const [Grid, gridApi] = useVbenVxeGrid({
  showSearchForm: showSearch.value,
  formOptions: formOptions,
  gridOptions,
  gridEvents,
});

// const showBorder = gridApi.useStore((state) => state.gridOptions?.border);
// const showStripe = gridApi.useStore((state) => state.gridOptions?.stripe);

// function changeBorder() {
//   gridApi.setGridOptions({
//     border: !showBorder.value,
//   });
// }

// function changeStripe() {
//   gridApi.setGridOptions({
//     stripe: !showStripe.value,
//   });
// }
function updateColumns(newColumns: VxeGridProps['columns']) {
  gridApi.setGridOptions({ columns: newColumns });
}

function changeLoading() {
  gridApi.setLoading(true);
  setTimeout(() => {
    gridApi.setLoading(false);
  }, 2000);
}
function getTableSelectedItems() {
  return gridApi.getCheckboxRecords();
}
// 添加刷新表格数据的方法
function refreshTable() {
  // 使用reload方法刷新表格数据

  gridApi.reload();
}

function getFormvalues() {
  return gridApi.formApi.getValues();
}
function updateSchema(schema: any) {
  return gridApi.formApi.updateSchema(schema);
}
function getCurrentPage() {
  return currentPage.value;
}
//刷新搜索表单
function refreshForm() {
  gridApi.formApi.resetForm();
}
const expandAll = () => {
  gridApi.grid?.setAllTreeExpand(true);
};

const collapseAll = () => {
  gridApi.grid?.setAllTreeExpand(false);
};
defineExpose({
  getCurrentPage,
  updateSchema,
  getFormvalues,
  updateColumns,
  refreshTable,
  changeLoading,
  getTableSelectedItems,
  expandAll,
  collapseAll,
  getCurrentTab: () => defaultActiveKey.value,
  refreshForm,
});

// 定义emit
const emit = defineEmits(['tab-change', 'export']);

const handleTabChange = (key: string) => {
  defaultActiveKey.value = key;
  gridApi.formApi.resetForm();
  // 发出标签页变化事件
  emit('tab-change', key);
};
</script>

<template>
  <Page
    :description="props.pageOptions.description"
    :title="props.pageOptions.title"
    class="h-full overflow-hidden"
    :auto-content-height="props.autoContent"
  >
    <div>
      <a-tabs
        v-if="props.tabOptions.enable"
        :default-active-key="defaultActiveKey"
        @change="handleTabChange"
      >
        <a-tab-pane v-for="tab in tabList" :key="tab.key" :tab="tab.title" />
      </a-tabs>
    </div>
    <Grid
      :key="defaultActiveKey"
      :table-title="props.titleOptions.title"
      :table-title-help="props.titleOptions.help"
    >
      <template #toolbar-actions>
        <slot name="toolbar-left"></slot>
      </template>
      <template #toolbar-tools>
        <!-- 添加导出按钮插槽，当 hasExport 为 true 时显示 -->
        <a-button
          shape="circle"
          v-if="props.hasExport && $isShowOpt('download')"
          @click="$emit('export')"
        >
          <template #icon>
            <DownloadOutlined />
          </template>
        </a-button>
        <slot name="toolbar-right"></slot>
      </template>
      <!-- 动态绑定插槽 -->
      <template
        v-for="column in props.columns"
        :key="column.field"
        #[column.slots?.default]="{ row }"
      >
        <slot :name="column.slots?.default" :row="row" />
      </template>
    </Grid>
  </Page>
</template>
