import { ref, computed, unref } from 'vue';
import { getPlatformService } from '#/api/platform';

export function useBizInfo(currentBiz?: any) {
  const bizList = ref<any>([]);

  function generatebizList(data: any): any {
    return data.map((item: any) => ({
      label: item.detail + `(${item.platform})`,
      value: item.platform,
    }));
  }

  function filterAIPlatforms(bizList: any): any {
    const aiPlatforms = ['AIGC_GATEWAY_IN', 'AIGC_GATEWAY_OUT'];
    return bizList.filter(
      (item: any) =>
        item.value.startsWith('AI_') || aiPlatforms.includes(item.value),
    );
  }

  const fetchData = async (isAI?: boolean) => {
    console.log('fetchData2', isAI);
    const response = await getPlatformService({ enable: '1' });
    const generatedList = generatebizList(response.data);
    bizList.value = isAI ? filterAIPlatforms(generatedList) : generatedList;
    return bizList.value;
  };

  // 统一处理参数为 ref
  // const bizRef = typeof currentBiz === 'function'
  //   ? computed(() => unref(currentBiz))
  //   : ref(currentBiz);
  // 业务类型判断计算属性
  const bizType = computed(() => {
    // const biz = unref(bizRef);
    const biz = unref(currentBiz);
    console.log('biz', biz);
    // console.log('biz33333', biz);
    return {
      isDrive: biz?.startsWith('drive'),
      isDocer: biz?.startsWith('docer'),
      isAK: biz?.startsWith('AK'),
      isHomeComment: biz === 'home_comment', // 家园评论 (特殊)
      isHomeTopic: biz === 'home_topic', // 家园帖子
      isForm: biz?.startsWith('ksform'), // 表单相关
      isWatermark: biz === 'kdocs_doc_watermark2', //水印 (特殊)
      isComment: [
        'kdocs_comment',
        'kdocs_doc_watermark2',
        'square_project',
        'home_comment',
        'plus_wps',
        'quality_kdocs_comment',
      ].includes(biz),
      isKdocsComment: biz === 'kdocs_comment', //全文评论
      isSquareProject: biz === 'square_project', // 大文集
      isWpsPlus: biz === 'plus_wps', // wps+
      isAccount: [
        'wps_account',
        'ciba_account',
        'quality_wps_account',
        'quality_ciba_account',
      ].includes(biz),
      isKsformAudit: biz === 'ksform_audit',
      isHome: [
        'home_comment',
        'home_topic',
        'ksform_audit',
        'enquiry_audit',
      ].includes(biz),
      isWpsAccount: [
        'wps_account',
        'ciba_account',
        'quality_wps_account',
        'quality_ciba_account',
      ].includes(biz),
      isAppPlatform: biz === '****************' || biz === '****************',
      isSentiment: biz.startsWith('sentiment'),
      isWpsAi: biz === 'airpageaigc',
      isImageDruve: biz === 'sentiment_image_drive_core',
      // isKdocsComment: [
      //   "kdocs_comment",
      //   "square_project",
      //   "plus_wps",
      //   "quality_kdocs_comment",
      // ].includes(currentBiz),
      // isSentiment: currentBiz?.startsWith("sentiment"),
      // isQuality: currentBiz?.startsWith("quality"),
      isViewChange: biz === 'kdocs_comment', // 家园评论 (特殊)
    };
  });

  // const isDocer = computed(() => {
  //   return currentBiz?.startsWith('docer');
  // })
  return {
    bizList,
    fetchData,
    bizType,
  };
}
