import { requestClient } from '#/api/request';
import { formatTimestampToDate } from '#/utils/time';

// 数据看板相关接口
const DASHBOARD_BASE_URL = '/manual/cms_report/custom_combined_report';

// 创建当天0点的本地时间对象
const startDate = new Date();
startDate.setHours(0, 0, 0, 0);

// 创建当天23:59:59的本地时间对象
const endDate = new Date();
endDate.setHours(23, 59, 59, 999);

const DASHBOARD_DEFAULT_QUERY = `biz=drive_core&export=false&time_type=hour&started_at=${formatTimestampToDate(Math.floor(startDate.getTime() / 1000), 'YYYY-MM-DD HH:mm:ss')}&ended_at=${formatTimestampToDate(Math.floor(endDate.getTime() / 1000), 'YYYY-MM-DD HH:mm:ss')}&is_today=false&is_ring=true`;

/**
 * 数据看板相关接口
 */

// 获取审核数据
export async function getDashboardStrategy() {
  return requestClient.get(
    `${DASHBOARD_BASE_URL}?${DASHBOARD_DEFAULT_QUERY}&rout_path=/statistic/custom_report/strategy/rv_report`,
  );
}

// 获取审核人数据
export async function getDashboardReviewer() {
  return requestClient.get(
    `${DASHBOARD_BASE_URL}?${DASHBOARD_DEFAULT_QUERY}&rout_path=/statistic/custom_report/operate/reviewer_num&distinct=reviewer_name`,
  );
}

// 获取机审数据
export async function getDashboardMachine() {
  return requestClient.get(
    `${DASHBOARD_BASE_URL}?${DASHBOARD_DEFAULT_QUERY}&rout_path=/statistic/custom_report/operate/rv_machine`,
  );
}

// 关键词操作日志 表格接口
export async function getKeywordsWordConfigLogs(queryString: string) {
  return requestClient.get(`/manual/keywords/wordconfig/logs?${queryString}`);
}

// 关键词操作日志 select接口
export async function getKeywordsWordLabels() {
  return requestClient.get('/manual/keywords/labels');
}

// 词库select组件接口
export async function getKeywordsWordBase(queryString?: string) {
  return requestClient.get(`/manual/keywords/getbase${queryString ? `?${queryString}` : ''}`);
}

// 黑库命中记录 表格接口
export async function getSentimentHitRecord(data) {
  return requestClient.get(
    `/manual/sentiment/query_hit_record`,{data},
  );
}

// 黑库命中日志 搜索栏业务select组件
export async function getPlatform() {
  return requestClient.get('/manual/control/get_platform');
}

// 黑库命中日志 搜索栏场景select组件
export async function getScenes(biz: string = 'drive_core') {
  return requestClient.get(`/manual/control/scenes?biz=${biz}`);
}

// 监管违规日志 表格接口
export async function getDangerIpLogs(data) {
  return requestClient.get(
    `/manual/record/danger_ip_logs`,{data}
  );
}

// 高敏行为日志 表格接口
export async function getSearchBehavorLogs(data) {
  return requestClient.get(
    `/manual/keywords/get_searchbehavor`,{data},
  );
}
export async function getReviewBehavorLogs(data) {
  return requestClient.get(
    `/manual/keywords/get_reviewbehavor`,{data},
  );
}
// 关键词修复日志
export async function getKeywordRocoverLog(data) {
  return requestClient.get(
    `/manual/keywords/get_keyword_recover_log`,{data},
  );
}

// 用户管理日志 表格接口1(封禁日志)
export async function getIllegalUserLogs(data) {
  return requestClient.get(`/manual/summary/illegal_user_log`,{data});
}

// 用户管理日志 表格接口2(禁分享日志)
export async function getYellowUserLogs(data) {
  return requestClient.get(`/manual/summary/yellow_user_log`,{data});
}

// 用户管理日志 表格接口3(社区封禁日志)
export async function getSearchHomeForbidUserLog(data) {
  return requestClient.get(`/manual/record/search_home_forbid_user_log`,{data});
}

// 网页一线账号日志 表格接口
export async function getSearchResetHomeUserLog(data) {
  return requestClient.get(`/manual/record/search_reset_home_user_log`,{data});
}

/**
 * 工单日志相关接口
 */

// 获取操作记录列表
export async function getOperateLogList(queryString: string) {
  return requestClient.get(`/manual/operate/logs?${queryString}`);
}

/**
 * 工单日志基础接口 - 标准日志接口
 * 用于大多数业务类型的日志查询
 */
export async function getRecordLogs(data: any) {
  return requestClient.get(`/manual/record/logs`,{data});
}

/**
 * 工单日志基础接口 - 文本日志接口
 * 用于文本相关的特殊业务类型
 */
export async function getTextLogs(data: any) {
  return requestClient.get(`/manual/task/v2/text/logs`,{data});
}

/**
 * 工单日志基础接口 - 历史日志接口
 * 用于头像审核等特殊业务类型
 */
export async function getHistoryLogs(data:any) {
  return requestClient.get(`/manual/record/history_logs`, {
    data,
  });
}

/**
 * WPS AI短文本日志接口
 */
export async function getWpsAiShortTextLogs(data: any) {
  return requestClient.get(`/manual/record/get_short_text_log`,{data});
}

/**
 * WPS AI小语言模型日志接口
 */
export async function getWpsAiMinLangLogs(data: any) {
  return requestClient.get(`/manual/record/get_min_lang_log`,{data});
}

/**
 * AI代答日志
 * */
export async function getAIAnswerLogs(data: any) {
  return requestClient.get(`/manual/record/get_ai_answer_log`,{data});
}
/**
 * WPS账号违规用户日志接口
 */
export async function getWpsAccountUserLogs(data:any) {
  return requestClient.get(`/manual/record/search_wps_account_user_log`,{data});
}

/**
 * 关键词模块相关接口
 */

// 获取关键词配置列表
export async function getKeywordsWordConfigs(queryString: string) {
  return requestClient.get(`/manual/keywords/wordconfigs?${queryString}`);
}

// 获取关键词实验室任务列表
export async function getKeywordsTaskList(queryString: string) {
  return requestClient.get(`/manual/pre_run/keywords/task/list?${queryString}`);
}

// 获取关键词相似词统计
export async function getKeywordsSimilarStatistics(queryString: string) {
  return requestClient.get(`/manual/keywords/get_similar_statistics?${queryString}`);
}

// 获取关键词监控数据
export const getKeywordsMonitor = (queryString: string) => {
    return requestClient.get(`/manual/keywords/getmonitor?${queryString}`);
};

// 黑库确认日志
export const getConfirmRecord = (data) => {
  return requestClient.get(`/manual/sentiment/query_confirm_record`,{data});
};



