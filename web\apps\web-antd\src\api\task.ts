import { requestClient } from '#/api/request';
import type { ApiCommonResponse } from '#/types/api';
import { MessageShowMode, SuccessMessageType } from '#/types/api';
import type { HandoverServiceRequest, QueueCountDelayData } from '#/types/task';
import { setPayload } from '#/composables/common/use-front-payload';
import { $t } from '#/locales';
// 拉单
export function pullTasks(data?: any) {
  setPayload(data, 'customError', [
    {
      matchText: 'fail to get tasks',
      message: $t('api.task.pull.customError'),
    },
  ]);
  return requestClient.post('/manual/task/v2/pull', data);
}
// 首次请求角标和时效数据
export function getAllCount(
  data?: any,
): Promise<ApiCommonResponse<QueueCountDelayData>> {
  return requestClient.get('/manual/task/all_count', data);
}

// 打标
export function pushTask(data?: any) {
  return requestClient.post('/manual/task/v2/push', data);
}
// 家园获取实时单量
export function getHourEfficiency(data?: any) {
  return requestClient.post('/manual/task/v2/efficiency/hour', data);
}

// 队列中机审图片识别
export function getPreviewImageInfo(data?: any) {
  return requestClient.get('/manual/preview/imageinfo', { data });
}

// 获取用户标签
export function getUserTag(data?: any) {
  return requestClient.get('/manual/fuser/tags', { data });
}

// 全文评论批量提交
export function pushTextTaskBatch(data?: any) {
  return requestClient.post('/manual/task/v2/push_text/batch', data);
}

export function taskV2PushBatch(data?: any) {
  return requestClient.post('/manual/task/v2/push/batch', data);
}

export function getRecordsDetail(data?: any) {
  return requestClient.get('/manual/record/search', { data });
}
// 更新移交上级（移交上级）
export function handoverService(data?: HandoverServiceRequest) {
  return requestClient.post('/manual/record/handover_superior', data);
}
// 无法审核
export function previewIncapable(data?: any) {
  return requestClient.post('/manual/record/preview_incapable', data);
}
// 获取打标拒绝类型
export function getForbidType(data?: any) {
  return requestClient.get('/manual/record/forbid_types', { data });
}

// 获取原帖链接
export function getOriginArticleUrl(data?: any) {
  return requestClient.post('/manual/task/v2/preview/url', data);
}

/**
 * 释放用户任务服务
 * @param data 请求数据
 * @returns Promise
 * 
 * @example
 * // 只显示失败消息，成功时不显示
 * releaseUserTaskService({})
 * 
 * // 显示所有消息，成功时使用info样式
 * const data = {};
 * setPayload(data, 'showMessage', MessageShowMode.All);
 * setPayload(data, 'successMessageType', SuccessMessageType.Info);
 * releaseUserTaskService(data);
 */
export function releaseUserTaskService(data?: any) {
  setPayload(data, 'showMessage', MessageShowMode.Fail)
  return requestClient.post('/manual/task/user_release', data);
}
// 云主机埋点
export function cloudBury(data?: any) {
  return requestClient.post('/manual/common/data/bury', data);
}
export function updateComment(data?: any) {
  return requestClient.post('/manual/task/update_comment_directly', data);
  // 账号安全拉队列
}
export function getPullAccountList(data?: any) {
  return requestClient.get('/manual/task/pull_violation_user', { data });
}
// 账号安全更新操作
export function updateAccountService(data?: any) {
  return requestClient.post('/manual/task/update_violation_user', data);
}

// 获取紧集延迟和普通延迟
export function getDelayEfficiency(data?: any) {
  return requestClient.post('/manual/task/v2/oldest', data);
}

/**
 * 示例：展示所有消息配置的可能组合
 * @param data 请求数据
 * @param showMessage 消息显示模式
 * @param successType 成功消息类型
 * @returns Promise
 * 
 * @example
 * // 显示所有消息，成功时使用success样式（默认）
 * exampleMessageConfig({}, MessageShowMode.All, SuccessMessageType.Success)
 * 
 * // 显示所有消息，成功时使用info样式
 * exampleMessageConfig({}, MessageShowMode.All, SuccessMessageType.Info)
 * 
 * // 只显示失败消息
 * exampleMessageConfig({}, MessageShowMode.Fail)
 * 
 * // 只显示成功消息，使用info样式
 * exampleMessageConfig({}, MessageShowMode.Success, SuccessMessageType.Info)
 * 
 * // 不显示任何消息
 * exampleMessageConfig({}, MessageShowMode.None)
 */
export function exampleMessageConfig(
  data: any = {}, 
  showMessage: MessageShowMode = MessageShowMode.None,
  successType: SuccessMessageType = SuccessMessageType.Success
) {
  setPayload(data, 'showMessage', showMessage);
  if (showMessage === MessageShowMode.All || showMessage === MessageShowMode.Success) {
    setPayload(data, 'successMessageType', successType);
  }
  return requestClient.post('/example/message-config', data);
}
