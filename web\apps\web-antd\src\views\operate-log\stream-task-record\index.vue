<script setup lang="ts">
import type { VxeGridProps } from '#/adapter/vxe-table';
import { ref } from 'vue';
import TableTemplate from '#/components/table/index.vue';
import { mapToOptions,validateParams } from '#/utils/transform';
import { COMPANY_TYPE_MAP,PAID_STATUS_MAP,VIDEO_STREAM_TASK_TYPE_MAP,VIDEO_STREAM_STATUS_MAP,VIDEO_STREAM_TASK_STATUS_MAP } from '#/constants/maps';
import { getTodayTimeRange,formatTimestampToDate } from "#/utils/time";
import {  getListStreamTaskRecord } from "#/api/operateLog";
import {useRouter} from "vue-router";
import {NO_LIMIT_NUM} from "#/constants/num";

const router = useRouter();
interface QueryParams {
  page: number;
  limit: number;
  start_time?: number;
  end_time?: number;
  [key: string]: any;
}

const paginationOptions = ref({
  pageSize: 20,
  currentPage: 1,
        // 不显示 “共 N 条”
  layouts: ['PrevPage', 'NextPage', 'Sizes','Jump'], // 显示按钮组成
});



const queryMethod = async ({
  page,
  pageSize,
  ...formValues
}: {
  page: number;
  pageSize: number;
  [key: string]: any;
}) => {
  const QueryParams:  QueryParams = {
    page: page - 1,
    limit: pageSize,
    ...formValues
  };
  // console.log("queryMethod called", page, pageSize);
  const res = await getListStreamTaskRecord(QueryParams);

  return {
    items: res.data.records,
    total: res.data.count ?? NO_LIMIT_NUM
    
  };
};

const searchOptions = {
  collapsed: false,
  schemas: {
    '1': [
      // 流ID 企业ID 认证企业等级 认证类型 付费状态 
      // 创建者ID 任务类型 任务ID 状态 运行状态 开始时间 结束时间
      {
        component: "Input",
        fieldName: 'stream_id',
        label: '流ID:',
        componentProps: {
          placeholder: '请输入',
        },
      },
      {
        component: "Input",
        fieldName: 'company_id',
        label: '企业ID:',
        componentProps: {
          placeholder: '请输入',
        },
      },
      {
        component: "InputNumber",
        fieldName: 'company_level',
        label: '认证企业等级:',
        componentProps: {
          placeholder: '请选择',
          min: 0,
          max: 10,
          
        },
      },
      {
        component: "Select",
        fieldName: 'company_type',
        label: '认证类型:',
        componentProps: {
          placeholder: '请选择',
          options: mapToOptions(COMPANY_TYPE_MAP),
        },
      },
      {
        component: "Select",
        fieldName: 'paid_status',
        label: '付费状态:',
        componentProps: {
          placeholder: '请选择',
          options: mapToOptions(PAID_STATUS_MAP),
        },
      },
      {
        component: "Input",
        fieldName: 'publisher_ID',
        label: '创建者ID:',
        componentProps: {
          placeholder: '请输入',
        },
      },
      {
        component: "Select",
        fieldName: 'task_type',
        label: '任务类型:',
        componentProps: {
          placeholder: '请选择',
          options: mapToOptions(VIDEO_STREAM_TASK_TYPE_MAP),
        },
      },
      {
        component: "Input",
        fieldName: 'task_id',
        label: '任务ID:',
        componentProps: {
          placeholder: '请输入',
        },
      },
      {
        component: "Select",
        fieldName: 'task_status',
        label: '运行状态:',
        componentProps: {
          placeholder: '请选择',
          options: mapToOptions(VIDEO_STREAM_TASK_STATUS_MAP),
        },
      },
      {
        component: "Select",
        fieldName: 'ai_label',
        label: '状态:',
        componentProps: {
          placeholder: '请选择',
          options:mapToOptions(VIDEO_STREAM_STATUS_MAP)
        },
      },
      {
        component: "RangePicker",
        fieldName: 'dateRange',
        label: '创建时间',
        class: "col-span-2",
        defaultValue: getTodayTimeRange(),
        componentProps: {
          valueFormat: "x",
          format: "YYYY-MM-DD HH:mm:ss", // 显示格式
          showTime: true,   // 显示时分秒选择器
        },
      },
    ],
  },
  showCollapseButton: true,
  submitOnChange: false,
  wrapperClass: 'grid-cols-6',
};

import { computed } from 'vue';
const columns = computed<VxeGridProps['columns']>(() => [
  {
    type: "seq",
    title: "序号",
    width: 80,
    align: "center",
  },
  {
    field: 'stream_id',
    title: '流ID',
  },
  {
    field: 'company_id',
    title: '企业ID',
  },
  {
    field: 'company_level',
    title: '认证企业等级',
  },
  {
    field: 'company_type',
    title: '认证类型',
    formatter: ({ cellValue }) => {
          return COMPANY_TYPE_MAP[cellValue] || "";
        },
  },
  {
    field: 'paid_status',
    title: '付费状态',
    formatter: ({ cellValue }) => {
          return PAID_STATUS_MAP[cellValue] || "";
        },
  },
  {
    field: 'paid_service',
    title: '付费套餐',
  },
  {
    field: 'publisher_id',
    title: '创建者ID',
  },
  {
    field: 'task_type',
    title: '任务类型',
    formatter: ({ cellValue }) => {
          return VIDEO_STREAM_TASK_TYPE_MAP[cellValue] || "";
        },
  },
  {
    title: '任务ID',
    field: 'taskid',
    slots: {
      default: 'task_id',
    },
  },
  {
    field: 'ai_label',
    title: '状态',
    formatter: ({ cellValue }) => {
          return VIDEO_STREAM_STATUS_MAP[cellValue] || "-";
        },
  },
  {
    field: 'task_status',
    title: '运行状态',
    formatter: ({ cellValue }) => {
          return VIDEO_STREAM_TASK_STATUS_MAP[cellValue] || "";
        },
  },
  {
    field: 'create_time',
    title: '开始时间',
    formatter: ({ cellValue }) => formatTimestampToDate(cellValue),
  },
  {
    field: 'finish_time',
    title: '结束时间',
    formatter: ({ cellValue }) => formatTimestampToDate(cellValue),
  },
]);

const toolbarOptions = {
  export: true,
};

const exportOptions = {
  filename: '表格数据', // 导出的文件名
  type: 'csv', // 导出格式
  isHeader: true, // 包含表头
  original: true, // 导出源数据
  useLabel: true, // 使用列标签作为字段名
  message: true, // 显示导出消息提示
  encoding: 'utf8', // 文件编码
};

function toDetail(row: any) {
  router.push({
    path: "/operateLog/stream_detail_log",
    query: {
      row: encodeURIComponent(JSON.stringify(row)),
    },
  });
}

</script>

<template>
    
  <table-template
    :columns="columns"
    :query-method="queryMethod"
    :pagination-options="paginationOptions"
    :toolbar-options="toolbarOptions"
    :export-options="exportOptions"
    :search-options="searchOptions"
  >
    
    <template #task_id="{ row }">
        <a 
          @click="toDetail(row)"
          class="color:blue-500 hover:text-blue-700"
        >{{ row.task_id }}</a>
    </template>

  </table-template>
  
  
</template>
