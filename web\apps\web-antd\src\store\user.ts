import { acceptHMRUpdate, defineStore } from 'pinia';
import type { SetUserInfoPayload } from '@vben/types';
import { clearLocalStorageExcept } from '#/utils/save';
import { logoutApi } from '#/api';

interface AccessState {
  /**
   * 高亮关键词
   */
  highLightkeyWords?: string;
  /**
   * 主题颜色
   */
  themeColor: string;
  userid: string;
  username: string;
  realname: string;
  supplier: string;
  manage_team: string[];
  flag: string;
  group_id: string;
  platform: string;
  // unDone: number;
  // dlgReleaseStatus: boolean;
}

/**
 * @zh_CN 用户信息相关
 */
export const useUserStore = defineStore('user', {
  state: (): AccessState => ({
    userid: '',
    username: '',
    realname: '',
    flag: '',
    supplier: '',
    manage_team: [],
    themeColor: '#343434',
    highLightkeyWords: '',
    group_id: '',
    platform: '',
  }),

  persist: true, // 整个状态都持久化
  getters: {
    isLoginStatus: (state) => !!state.userid,
  },
  actions: {
    setUserInfo(userInfo: SetUserInfoPayload | null) {
      console.log('设置用户信息', userInfo);
      // 设置用户信息
      this.userid = userInfo?.user_id || '';
      this.username = userInfo?.user_name || '';
      this.realname = userInfo?.real_name || '';
      this.supplier = userInfo?.supplier || '';
      this.manage_team = userInfo?.manage_team || [];
      this.flag = userInfo?.flag || '';
      this.themeColor = userInfo?.custom_info?.color || '';
      this.highLightkeyWords = userInfo?.keyword || '';
      this.group_id = userInfo?.group_id || '';
      this.platform = userInfo?.platform || '';
    },
    setFlag(flag: string) {
      this.flag = flag; // 直接更新 flag
    },

    setThemeColor(color: string) {
      // setItem('theme-color', color);
      this.themeColor = color;
    },
    async logOut() {
      clearLocalStorageExcept('');
      window.postMessage({ type: 'after-logout' }, '*');
      try {
        await logoutApi();
      } catch {}
    },
  },
});

// 解决热更新问题
if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useUserStore, import.meta.hot));
}
