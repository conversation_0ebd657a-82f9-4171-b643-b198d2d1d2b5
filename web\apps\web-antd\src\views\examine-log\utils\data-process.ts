import { transformTimeFields } from '#/utils/time';
import {
  EXAMINE_TYPE_MAP,
  EXAMINE_SENT_MAP,
  FLAG_MAP,
} from '#/constants/maps/examine-log';
export const processQueryParams = (
  formValues: any,
  page: number,
  pageSize: number,
  curCheckLogConfig: any,
  tableRef: any,
) => {
  const baseParams: Record<string, any> = {
    page: page - 1,
    limit: pageSize,
    from: curCheckLogConfig.value.biz,
  };

  const currentTab = tableRef.value.getCurrentTab();
  const allowedFieldsByTab: Record<string, string[]> = {
    '1': [
      'fname',
      'userid',
      'fileid',
      'fileinx',
      'f_fileid',
      'scene',
      'extra_id',
      'company',
      'status',
      'type',
      'role',
      'reviewer',
      'record_flag',
      'start_time',
      'end_time',
    ],
    '2': [
      'third_preview_url',
      'url_mode',
      'fileid',
      'fileinx',
      'scene',
      'extra_id',
      'resource_id',
    ],
    '3': ['word', 'start_time', 'end_time'],
  };

  if (formValues) {
    const timeFields = transformTimeFields(formValues, [
      ['filterTime', ['start_time', 'end_time']],
    ]);
    const allowedFields = allowedFieldsByTab[currentTab] || [];
    allowedFields.forEach((key) => {
      if (
        formValues[key] !== undefined &&
        formValues[key] !== null &&
        formValues[key] !== ' '
      ) {
        baseParams[key] = formValues[key];
      }
    });

    if (Array.isArray(formValues.type) && formValues.type.length > 0) {
      baseParams.type = formValues.type[1];
    }

    if (timeFields) {
      const { start_time, end_time } = timeFields;
      if (start_time !== undefined && allowedFields.includes('start_time')) {
        baseParams.start_time = start_time;
      }
      if (end_time !== undefined && allowedFields.includes('end_time')) {
        baseParams.end_time = end_time;
      }
    }
  }

  return { baseParams };
};

export const processResponseData = (
  response: any,
  page: number,
  pageSize: number,
  taskConfig: any,
) => {
  if (response?.data) {
    const records = response.data.records || response.data.results || [];
    const processedItems = records.map((item: any, index: number) => ({
      ...item,
      index: (page - 1) * pageSize + index + 1,
      cur_preview_priority:
        taskConfig.value.preview_config?.[item.scene]?.preview_priority ||
        '1,2,3',
      ex_status_obj: item.ex_status ? JSON.parse(item.ex_status) : {},
      file_level: item.ex_status_obj?.fl || 0,
      is_grey: (item.pvless & 0x02) === 2,
    }));

    return {
      items: processedItems,
      total: response.data.more ? page * pageSize + 1 : records.length,
    };
  }
  return { items: [], total: 0 };
};

export const parseRecordFlag = (flag: number) => {
  return FLAG_MAP.filter(({ mask }) => flag & mask)
    .map(({ label }) => label)
    .join(', ');
};

export const getStatusVariant = (row: any) => {
  if (row.status === 'ok') {
    if ((row.pvless & 0x01) === 1) {
      return 'ok_pvless_1';
    } else if ((row.pvless & 0x02) === 2) {
      return 'ok_pvless_2';
    } else {
      return 'ok_normal';
    }
  }
  return row.status || '';
};

export const commentTypeFilter = (type: string) => {
  return EXAMINE_TYPE_MAP[type] || '';
};

export const forbidTypeSentiTextFilter = (type: string) => {
  return EXAMINE_SENT_MAP[type] || '';
};
