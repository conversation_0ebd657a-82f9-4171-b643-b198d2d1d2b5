import { ref, reactive, computed } from 'vue';

import { defineStore } from 'pinia';
import type { Platform } from '#/types/platform';
import type { TaskConfig } from '#/types/task';
import { getPlatformService } from '#/api/platform';
import _ from 'lodash';
import { useRoute } from 'vue-router';

const usePlatformStore = defineStore('platform', () => {
  /// 过期时间
  const EXPIRE_TIME = 1000 * 60 * 60 * 3;
  /// 记录上次请求平台数据的时间
  const time = reactive({
    platform: 0,
    queueConfigs: 0,
  });
  const promise = reactive<{
    platform: Promise<Record<string, Platform>> | null;
    queueConfigs: Promise<void> | null;
  }>({
    platform: null,
    queueConfigs: null,
  });
  /// 平台数据
  const _platform = ref<Record<string, Platform>>({});
  /// 队列配置数据
  const _queueConfigs = ref<TaskConfig[]>([]);
  const currentQueueConfig = ref<TaskConfig | null>(null);
  const biz = ref<string>('');
  const queue = ref<string>('');
  const setPlatform = (platformData: Record<string, Platform>) => {
    time.platform = Date.now();
    _platform.value = platformData;
  };
  const hasLoadedPlatform = () => {
    return Object.keys(_platform.value).length > 0;
  };
  const setQueueConfigs = (queueConfigsData: TaskConfig[]) => {
    time.queueConfigs = Date.now();
    _queueConfigs.value = queueConfigsData;
  };
  const setCurrentQueueConfig = (queueConfig: TaskConfig) => {
    currentQueueConfig.value = queueConfig;
  };
  const getQueueConfig = (biz: string,queue: string) => {
    return _queueConfigs.value.find((item) => item.biz === biz && item.task_name === queue);
  };
  const fetchPlatform = async () => {
    /// 如果promise已经存在，则返回promise,避免并发场景重复请求
    if (promise.platform) {
      return promise.platform;
    }
    /// 创建promise
    promise.platform = (async () => {
      let res = await getPlatformService({
        enable: '1',
      });
      _platform.value = res.data.reduce((map: any, item: Platform) => {
        map[item.platform] = item;
        return map;
      }, {}); // 转化成map提高查找效率
      time.platform = Date.now();
      promise.platform = null;
      return _platform.value;
    })();
    return promise.platform;
  };
  const platform = computed(() => {
    if (Date.now() - time.platform > EXPIRE_TIME) {
      fetchPlatform();
    }
    return _platform.value;
  });
  const queueConfigs = computed(() => {
    return _queueConfigs.value;
  });
  const $reset = () => {
    _platform.value = {};
    _queueConfigs.value = [];
  };
  const setQueue = (queue0: string) => {
    queue.value = queue0;
  };
  return {
    isTextMark: computed(() => {
      return currentQueueConfig.value?.preview_config[""]?.type_preview===8;
    }),
    isDrive: computed(() => {
      const route = useRoute();
      // 如果 biz.value 不存在，尝试从路由中获取
      const currentBiz = biz.value || (route.query.biz as string) || '';
      return currentBiz === 'drive_core' || currentBiz === 'drive_test' || currentBiz.includes('drive_core');
    }),
    isSentiImage: computed(() => {
      const route = useRoute();
      // 如果 biz.value 不存在，尝试从路由中获取
      const currentBiz = biz.value || (route.query.biz as string) || '';
      return currentBiz.includes('sentiment_image');
    }),
    isDownloadShow: computed(() => {
      const route = useRoute();
      // 如果 biz.value 不存在，尝试从路由中获取
      const currentBiz = biz.value || (route.query.biz as string) || '';
      console.log('biz.value', currentBiz);
      return ["drive_core", "drive_test", "docer_v2"].includes(currentBiz);
    }),
    isSentiConfirm: computed(() => {
      const route = useRoute();
      // 如果 biz.value 不存在，尝试从路由中获取
      const currentBiz = biz.value || (route.query.biz as string) || '';
      return ["sentiment_image_confirm"].includes(currentBiz);
    }),
    platform,
    hasLoadedPlatform,
    queueConfigs,
    currentQueueConfig,
    getQueueConfig,
    biz,
    queue,
    setPlatform,
    setQueueConfigs,
    setCurrentQueueConfig,
    setQueue,
    fetchPlatform,
    $reset,
  };
});

export default usePlatformStore;
