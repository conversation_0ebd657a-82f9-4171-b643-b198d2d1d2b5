<script setup lang="ts">
import { onMounted, ref, useTemplateRef, watch } from 'vue';

import { httpsCheck } from '@vben/utils';

import { message } from 'ant-design-vue';
import { storeToRefs } from 'pinia';

import { audioDetail, getUrlData, videoDetail } from '#/api/preview';
import { MediaType } from '#/composables/task/use-task-media-type';
import {
  useLoadingState,
  ViewStatus,
} from '#/composables/ui/use-loading-state';
import { FRAME_CHECK_TYPE_MAP } from '#/constants/maps';
import { useTaskStore } from '#/store';

const props = defineProps<{
  audioUrls: string[];
  mediaType: MediaType;
  videoUrls: {
    ism3u8: boolean;
    src: string;
  }[];
}>();
const emit = defineEmits<{
  (e: 'loaded'): void;
  (e: 'loadFail'): void;
}>();
const frameDetail = ref<any[]>([]);
const ocrText = ref<any[]>([]);
const asrText = ref<any[]>([]);
const audioDetailData = ref<any[]>([]);
const { taskItem, biz } = storeToRefs(useTaskStore());
const audioUrl = ref<string>('');
const videoUrls0 = ref<string[]>([]);
const fileName = ref<string>('');
const { isLoading, setLoading, setLoaded, setLoadFailed } = useLoadingState(
  ViewStatus.UNLOAD,
);
const getVideoDetail = async () => {
  const resp = await videoDetail({
    fileinx: taskItem.value?.fileinx || '',
    scene: '',
    from: biz.value,
    force: false,
    id_new: false,
    nowtime: taskItem.value?.modify_time,
  });
  if (resp.result) {
    frameDetail.value = resp.frameDetail;
    ocrText.value = resp.ocr_text;
    asrText.value = resp.asr_text;
    fileName.value = resp.fname;
    const videoResp = await getUrlData(httpsCheck(resp.video_url));
    videoUrls0.value = videoResp.videos;
    setLoaded();
    emit('loaded');
    return;
  }
  setLoadFailed();
  emit('loadFail');
  message.warning('无法获取视频详细信息');
};
const audioPlayers = useTemplateRef<HTMLAudioElement[]>('audioPlayers');
const videoPlayers = useTemplateRef<HTMLVideoElement[]>('videoPlayers');
const getAudioDetail = async () => {
  const resp = await audioDetail({
    fileinx: taskItem.value?.fileinx || '',
    scene: '',
    from: biz.value,
    force: false,
    id_new: false,
    nowtime: taskItem.value?.modify_time,
  });
  if (resp.result) {
    audioDetailData.value = resp.audioDetail;
    asrText.value = resp.asr_text;
    audioUrl.value = httpsCheck(resp.audio_url);
    fileName.value = resp.fname;
    if (resp.audio_url) {
      const audioResp = await getUrlData(httpsCheck(resp.audio_url));
      console.log('audioResp', audioResp);
    }
    setLoaded();
    emit('loaded');
    return;
  }
  setLoadFailed();
  emit('loadFail');
  message.warning('无法获取音频详细信息');
};
const setup = async () => {
  setLoading();
  console.log('setup', props.mediaType);
  try {
    await (props.mediaType === MediaType.Video
      ? getVideoDetail()
      : getAudioDetail());
  } catch {
    setLoadFailed();
    emit('loadFail');
    message.error('加载失败');
  }
};
watch(taskItem, () => {
  frameDetail.value = [];
  ocrText.value = [];
  asrText.value = [];
  audioDetailData.value = [];
  videoUrls0.value = [];
  setup();
});
const handleWaiting = (e: Event) => {
  console.log('waiting', e);
};
const handleLoadedMetadata = () => {
  console.log('loadedmetadata');
};
const handleStalled = () => {
  console.log('stalled');
};
const handleSuspend = () => {
  console.log('suspend');
};
const handleEmptied = () => {
  console.log('emptied');
};
const handleCanplay = () => {
  console.log('canplay');
};
const handleCanplaythrough = () => {
  console.log('canplaythrough');
};
const handleError = () => {
  console.log('error');
};

/**
 * 处理抽帧图片点击事件，让视频跳转到对应时间
 * @param frameTime 帧对应的时间戳（秒）
 * @example handleFrameClick(30.5) // 跳转到30.5秒
 */
const handleFrameClick = (frameTime: number) => {
  try {
    // 优先使用ref获取视频元素
    let targetVideo: HTMLVideoElement | null = null;

    // 检查ref中的视频元素
    if (videoPlayers.value && videoPlayers.value.length > 0) {
      targetVideo = videoPlayers.value[0] || null;
    }
    // 如果ref中没有找到，则从DOM中查找
    else {
      const videos = document.querySelectorAll('video');
      if (videos.length > 0) {
        targetVideo = videos[0] as HTMLVideoElement;
      }
    }

    if (targetVideo) {
      // 记录当前是否在播放
      const wasPlaying = !targetVideo.paused;

      // 设置视频时间
      targetVideo.currentTime = frameTime;

      // 如果之前是播放状态，则继续播放；否则保持暂停状态
      if (wasPlaying) {
        targetVideo.play().catch((error) => {
          console.warn('视频播放失败:', error);
        });
      }

      message.success(
        `已定位到 ${new Date(frameTime * 1000).toISOString().slice(11, 19)}`,
      );
    } else {
      message.warning('未找到视频播放器');
    }
  } catch (error) {
    console.error('跳转视频时间失败:', error);
    message.error('跳转失败');
  }
};

onMounted(() => {
  console.log('audioPlayers', audioPlayers.value, props.videoUrls);
  setup();
});
</script>
<template>
  <div class="flex" v-loading="{ active: isLoading, text: '拼命加载中...' }">
    <div class="flex max-h-[calc(100vh-16rem)] w-full overflow-auto">
      <!-- 左侧：视频和抽帧图片区域 -->
      <div class="flex w-3/4 flex-wrap items-center gap-4 overflow-auto p-4">
        <!-- 视频部分 -->
        <template v-if="mediaType === MediaType.Video">
          <!-- 使用从API获取的视频URL列表 -->
          <template v-if="videoUrls0.length > 0">
            <video
              v-for="(item, index) in videoUrls0"
              :key="`video-api-${index}`"
              ref="videoPlayers"
              :src="item"
              class="m-2 h-[300px] w-[200px] object-cover"
              controls
              playsinline
            >
              对不起，您的浏览器不支持HTML5视频。
            </video>
          </template>

          <!-- 使用props传入的视频URL列表 -->
          <video
            v-for="(item, index) in videoUrls"
            v-else
            :key="`video-prop-${index}`"
            :src="item.src"
            class="m-2 h-[350px] w-[250px] object-cover"
            controls
            playsinline
            @canplay="handleCanplay"
            @canplaythrough="handleCanplaythrough"
            @emptied="handleEmptied"
            @error="handleError"
            @loadedmetadata="handleLoadedMetadata"
            @stalled="handleStalled"
            @suspend="handleSuspend"
            @waiting="handleWaiting"
          >
            对不起，您的浏览器不支持HTML5视频。
          </video>
        </template>

        <!-- 音频部分 -->
        <template v-if="mediaType === MediaType.Audio">
          <!-- 使用从API获取的音频URL -->
          <div v-if="audioUrl" class="m-2">
            <audio controls controlslist="nodownload" disablePictureInPicture>
              <source :src="audioUrl" type="audio/mpeg" />
              您的浏览器不支持音频播放
            </audio>
          </div>

          <!-- 使用props传入的音频URL列表 -->
          <div
            v-for="(item, index) in audioUrls"
            v-else
            :key="`audio-${index}`"
            class="m-2"
          >
            <audio
              ref="audioPlayers"
              :src="item"
              controls
              controlslist="nodownload"
              disablePictureInPicture
              @canplay="handleCanplay"
              @canplaythrough="handleCanplaythrough"
              @emptied="handleEmptied"
              @error="handleError"
              @loadedmetadata="handleLoadedMetadata"
              @stalled="handleStalled"
              @suspend="handleSuspend"
              @waiting="handleWaiting"
            >
              您的浏览器不支持音频播放
            </audio>
          </div>
        </template>

        <!-- 抽帧图片部分 -->
        <div
          v-for="(item, index) in frameDetail"
          :key="`frame-${index}`"
          class="relative m-2"
        >
          <div
            class="relative inline-block overflow-hidden border-2 border-red-500 p-0"
            style="border: 2px solid red"
          >
            <img
              :src="httpsCheck(item.imgUrl)"
              alt="frame"
              class="h-[300px] w-[200px] cursor-pointer object-cover transition-all duration-300 hover:scale-105 hover:opacity-95 hover:shadow-lg"
              title="点击跳转到此帧时间"
              @click="handleFrameClick(item.time)"
            />
            <div
              class="absolute left-0 top-0 bg-red-500 p-1 text-xs text-white"
            >
              {{ FRAME_CHECK_TYPE_MAP[item.imgType] }}
            </div>
            <div class="absolute right-0 top-0 bg-white p-1 text-xs text-black">
              {{ new Date(item.time * 1000).toISOString().slice(11, 19) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：文本信息部分 -->
      <div class="w-1/4 overflow-auto p-2">
        <!-- OCR文本区域 -->
        <div
          v-if="ocrText && ocrText.length > 0"
          class="mb-3 rounded-md border bg-gray-50 p-2"
        >
          <div class="mb-2 border-b pb-1 text-sm font-bold">OCR文本</div>
          <div
            v-for="(item, index) in ocrText"
            :key="`ocr-${index}`"
            class="mb-1 border-b border-gray-100 pb-1"
          >
            <div class="text-xs font-semibold text-blue-500">
              {{ new Date(item.time * 1000).toISOString().slice(11, 19) }}
            </div>
            <div class="pl-2 text-sm">{{ item.text }}</div>
          </div>
        </div>

        <!-- ASR文本区域 -->
        <div
          v-if="asrText && asrText.length > 0"
          class="rounded-md border bg-gray-50 p-2"
        >
          <div class="mb-2 border-b pb-1 text-sm font-bold">ASR文本</div>
          <div
            v-for="(item, index) in asrText"
            :key="`asr-${index}`"
            class="mb-1 border-b border-gray-100 pb-1"
          >
            <div class="text-xs font-semibold text-blue-500">
              {{ new Date(item.time * 1000).toISOString().slice(11, 19) }}
            </div>
            <div class="pl-2 text-sm">{{ item.text }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
