<script setup lang="ts">
import type { VxeGridProps } from "#/adapter/vxe-table";
import { shallowRef } from "vue";
import { ID_TYPE_MAP } from "#/constants/maps/operate-log";
import { BBS_OPT_TYPE } from "#/constants/maps/status";
import TableTemplate from "#/components/table/index.vue";
import { getSearchHomeForbidUserLog } from "#/api/operateLog";
import { storeToRefs } from "pinia";
import { mapToOptions } from "#/utils/transform";
import { findLabelByValue } from "#/utils/data";
import useForbidStore from "#/store/forbid";
import ForbidSelect from "#/components/select/ForbidType.vue";

const forbidStore = useForbidStore();
const { allForbidTags } = storeToRefs(forbidStore);
import {
  getLastNDaysRange,
  transformTimeFields,
  formatTimestampToDate,
} from "#/utils/time";
// 搜索配置
const searchOptions = {
  collapsed: false, // 默认展开
  schemas: {
    "1": [
      {
        component: "Input",
        fieldName: "home_user_id",
        label: "userId",
      },
      {
        component: "Select",
        fieldName: "home_user_id_type",
        label: "账号类型",
        componentProps: {
          allowClear: true,
          options: mapToOptions(ID_TYPE_MAP),
        },
      },
      {
        component: "Input",
        fieldName: "reviewer_name",
        label: "操作人",
        componentProps: {
          placeholder: "操作人",
        },
      },
      {
        fieldName: "type",
        component: shallowRef(ForbidSelect),
        componentProps: {
          isGLobalList: true,
          isCascader: true,
          filterType: [3, 5],
        },
        label: "违规类型",
      },
      {
        component: "Select",
        fieldName: "operate_type",
        label: "操作类型",
        componentProps: {
          allowClear: true,
          options: mapToOptions(BBS_OPT_TYPE),
        },
      },
      {
        component: "RangePicker",
        fieldName: "dateRange",
        label: "时间",
        defaultValue: getLastNDaysRange(14, 0, "X"),
        componentProps: {
          valueFormat: "X",
          showTime: true,
        },
      },
    ],
  },
  showCollapseButton: true,
  submitOnChange: false,
  wrapperClass: "grid-cols-4",
};

// 表格列配置
const columns: VxeGridProps["columns"] = [
  {
    type: "seq",
    align: "center",
    title: "序号",
    fixed: "left",
    width: 60,
  },
  {
    field: "user_id",
    title: "userId",
  },
  {
    field: "user_id_type",
    title: "账号类型",
    formatter: ({ cellValue }) => ID_TYPE_MAP[cellValue],
  },
  {
    field: "type",
    title: "违规类型",
    formatter: ({ cellValue }) =>
      findLabelByValue(allForbidTags.value, cellValue, "type", "name"),
  },
  {
    field: "remark",
    title: "备注",
  },
  {
    field: "operate_type",
    title: "操作类型",
    formatter: ({ cellValue }) => BBS_OPT_TYPE[cellValue],
  },
  {
    field: "nowtime",
    title: "操作时间",
    formatter: ({ cellValue }) => formatTimestampToDate(cellValue),
  },
  {
    field: "reviewer_name",
    title: "操作人",
  },
];

// 查询方法
const queryMethod = async ({
  page,
  pageSize,
  ...formValues
}: {
  page: number;
  pageSize: number;
  [key: string]: any;
}) => {
  let params = {
    page: page - 1,
    limit: pageSize,
    ...transformTimeFields(formValues, [["dateRange", ["start_time", "end_time"]]]),
    type: formValues.type?.[1],
  };
  const res = await getSearchHomeForbidUserLog(params);

  return {
    items: res.data.logs,
    total: res.data.count,
  };
};

// 分页配置
const paginationOptions = {
  total: 0,
  pageSize: 10,
  currentPage: 1,
};
</script>

<template>
  <div>
    <table-template
      :columns="columns"
      :pagination-options="paginationOptions"
      :query-method="queryMethod"
      :search-options="searchOptions"
    />
  </div>
</template>
