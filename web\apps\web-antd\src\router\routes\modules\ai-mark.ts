import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

import { $t } from '#/locales';
import { useBadgeStore } from '#/store/badge';
// ai复审
const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      // order: 100,
      icon: 'ant-design:robot-outlined',
      keepAlive: true,

      badge: () => useBadgeStore().badgeParentData['/aiMark'],
      badgeVariants: 'destructive',
      title: $t('page.aiMark.title'),
    },
    name: 'AiMark',
    path: '/aiMark',
    children: [
      {
        meta: {
          title: $t('page.aiMark.markSetting'),
        },
        name: 'MarkSetting',
        path: 'markSetting',
        component: () => import('#/views/ai-mark/mark-setting.vue'),
      },
      {
        path: "textGet",
        component: () => import('#/views/ai-mark/text-get.vue'), // 文档提取
        name: "TextGet",
        meta: {
          title:  $t('page.aiMark.textGet'),
          badge: () => useBadgeStore().badgeData['/aiMark/textGet'],
          badgeVariants: 'destructive',
        }
      },
      {
        path: "aiComplaintMarkTask",
        component: () =>  import('#/views/ai-mark/text-get.vue'), // ai举报标注
        name: "AIComplaintMarkTask",
        meta: {
          title: $t('page.aiMark.aiComplaintMarkTask'),
          badge: () => useBadgeStore().badgeData['/aiMark/aiComplaintMarkTask'],
          badgeVariants: 'destructive',
        }
      },
      {
        path: "aiViolateComfirm",
        component: () => import('#/views/ai-mark/ai-violate-comfirm.vue'), // AI账号违规确认
        name: "AiViolateComfirm",
        meta: {
          title: $t('page.aiMark.aiViolateComfirm'),
          badge: () => useBadgeStore().badgeData['/aiMark/aiViolateComfirm'],
          badgeVariants: 'destructive',
        }
      },
      {
        path: "specialMarkTask",
        component: () => import('#/views/ai-mark/special-mark.vue'), // 特殊监管用户复审
        name: "SpecialMarkTask",
        meta: {
          title: $t('page.aiMark.specialMarkTask'),
          badge: () => useBadgeStore().badgeData['/aiMark/specialMarkTask'],
          badgeVariants: 'destructive',
        }
      },
      {
        path: "markTask",
        component: () => import('#/views/ai-mark/offline-mark.vue'), // 离线数据标注
        name: "MarkTask",
        meta: {
          title: $t('page.aiMark.markTask'),
          badge: () => useBadgeStore().badgeData['/aiMark/markTask'],
          badgeVariants: 'destructive',
        }
      },
      {
        path: "onlineMarkTask",
        component: () => import('#/views/ai-mark/online-mark.vue'), // 线上数据标注
        name: "OnlineMarkTask",
        meta: {
          title: $t('page.aiMark.onlineMarkTask'),
          badge: () => useBadgeStore().badgeData['/aiMark/onlineMarkTask'],
          badgeVariants: 'destructive',
        }
      },
      {
        meta: {
          title: $t('page.aiMark.dataImport'), //数据导入
        },
        name: 'DataImport',
        path: '/aiMark/dataImport',
        
        component: () => import('#/views/ai-mark/data-import.vue'),
      },
      {
        meta: {
          title: $t('page.aiMark.aiMarkDataStatics'), //数据统计
        },
        name: 'AiMarkDataStatics',
        path: '/aiMark/aiMarkDataStatics',
        component: () => import('#/views/ai-mark/data-statics.vue'),
      },
      {
        meta: {
          title: $t('page.aiMark.aiMarkLog'), //操作日志
        },
        name: 'AiMarkLog',
        path: '/aiMark/aiMarkLog',
        component: () => import('#/views/ai-mark/ai-mark-log.vue'),
      },
      {
        meta: {
          title: $t('page.aiMark.aiMarkImgTextLog'), //图文操作日志
        },
        name: 'AiMarkImgTextLog',
        path: '/aiMark/aiMarkImgTextLog',
        component: () => import('#/views/ai-mark/ai-mark-img-text-log.vue'),
      },
      
    ],
  },
];

export default routes;
