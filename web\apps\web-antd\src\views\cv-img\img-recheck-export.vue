<script setup lang="ts">
import TableTemplate from '#/components/table/index.vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { reactive } from 'vue';
import { CV_IMG_MARK_MAP } from '#/constants/maps/ai-mark';
import { formatTimestampToDate } from '#/utils/time';
import { getMarkList } from '#/api/ai-mark';
import { useUserStore } from '#/store/user';

const userStore = useUserStore();
const paginationOptions = reactive({
  total: 0,
  pageSize: 20,
  currentPage: 1,
});
const columns: VxeGridProps['columns'] = [
  {
    field: 'id',
    title: '任务ID',
    align: 'center',
  },
  {
    field: 'type',
    title: '数据类型',
    align: 'center',
    slots: {
      default: 'type',
    },
  },
  {
    field: 'desc',
    title: '简述',
    align: 'center',
  },
  {
    field: 'user_name',
    title: '创建人',
    align: 'center',
  },
  {
    field: 'create_time',
    title: '创建时间',
    align: 'center',
    formatter: ({ cellValue }) => formatTimestampToDate(cellValue),
  },
  {
    field: 'sample_count',
    title: '样本个数',
    align: 'center',
  },
  {
    field: 'biz',
    title: '业务编号',
    align: 'center',
    slots: { default: 'biz' },
  },
];

async function getList({
  page,
  pageSize,
  ...formValues
}: {
  page: number;
  pageSize: number;
  [key: string]: any;
}) {
  let init = {
    page: page - 1,
    limit: pageSize,
    biz: 'cv_marking_drive_core',
  };
  try {
    const res = await getMarkList(init);
    if (res && res.result) {
      return {
        items: res.data.records,
        total: res.data.count,
      };
    }
  } catch (error) {
    return {
      items: [],
      total: 0,
    };
  }
}
function handleExport(row: any) {
  let { id, task_ids, entry } = row;
  let params = task_ids || id;
  let href = `/manual/cv/mark/export_record?user_id=${+userStore.userid}&task_id_range=${id}&task_ids=${params}&entry=${entry}`;
  location.href = href;
}
</script>
<template>
  <div>
    <table-template
      ref="tableRef"
      :columns="columns"
      :pagination-options="paginationOptions"
      :query-method="getList"
    >
      <template #type="{ row }">
        <span v-if="row.type == 2">文档提取</span>
        <span v-else-if="row.type == 1">离线数据</span>
        <span v-else-if="row.type == 3">线上提取</span>
        -<span>{{ CV_IMG_MARK_MAP[row.entry] }}</span>
      </template>
      <template #biz="{ row }">
        <a-button type="link" @click="handleExport(row)">导出</a-button>
      </template>
    </table-template>
  </div>
</template>
