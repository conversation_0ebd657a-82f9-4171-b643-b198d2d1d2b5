<script lang="ts" setup>
import { ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';

import { Select } from 'ant-design-vue';

import { getProductList } from '#/api/platform';

/**
 * 组件属性定义
 */
const props = defineProps({
  /**
   * 产品线类型
   *
   * 用于筛选不同类型的产品线数据
   *
   * 示例:
   * ```vue
   * <ProductSelect product-line-type="quality_platform" />
   * ```
   */
  productLineType: {
    type: String,
    required: false,
    default: '',
  },
});

/**
 * 事件定义
 */
const emit = defineEmits(['change']);

/**
 * 双向绑定的值
 */
const modelValue = defineModel();

/**
 * 存储选项数据，用于onChange时查找完整的option信息
 */
const optionsData = ref<
  Array<{ disabled: boolean; label: string; value: string }>
>([]);

/**
 * 生成选项数据
 * @param fieldList 字段列表
 * @returns 格式化的选项数组
 */
const generateOptions = (fieldList: Array<{ name: string; value: string }>) => {
  return fieldList.map((item: { name: string; value: string }) => ({
    label: item.name,
    value: item.value,
    disabled: false,
  }));
};

/**
 * 获取产品列表数据
 * @returns Promise<选项数组>
 */
async function fetchApi() {
  const response = await getProductList({
    product_line_type: props.productLineType,
  });
  const data = response.data.product_line;

  const targetData =
    props.productLineType === 'ai_mark'
      ? data.find((item: any) => item.name === 'AI模型厂商')
      : data[0];

  const options = generateOptions(targetData.field_list);

  // 存储选项数据供onChange使用
  optionsData.value = options;

  return options;
}

/**
 * 处理值变化事件
 * @param value 选中的值
 */
const handleChange = (value: string) => {
  // 根据value查找完整的option对象
  const selectedOption = optionsData.value.find(
    (option) => option.value === value,
  );

  // 触发change事件，传递完整的option对象
  if (selectedOption) {
    emit('change', selectedOption);
  } else {
    // 如果没找到匹配的选项（可能是清空操作），传递null
    emit('change', null);
  }
};
</script>

<template>
  <api-component
    v-model="modelValue"
    :api="fetchApi"
    :component="Select"
    :immediate="false"
    loading-slot="suffixIcon"
    visible-event="onDropdownVisibleChange"
    @change="handleChange"
  />
</template>
