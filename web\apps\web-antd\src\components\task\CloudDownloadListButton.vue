<script lang="ts" setup>
import usePlatformStore from '#/store/platform';
import { ref } from 'vue';
import DownloadDrawer from './DownloadDrawer.vue';
import { MynauiDownload } from '@vben/icons';
import float from '#/components/common/drag.vue';

/**
 * 定义组件属性
 * @example
 * <cloud-download-list-button :force-show="true" />
 */
defineProps({
  /**
   * 是否强制显示，设为true时将忽略platformStore.isDownloadShow的值始终显示
   */
  forceShow: {
    type: Boolean,
    default: false,
  },
});

const platformStore = usePlatformStore();
const downloadDrawerVisible = ref(false);
</script>
<template>
  <div>
    <float
      v-if="forceShow || platformStore.isDownloadShow"
      :append-to-body="true"
      :enable-snap="true"
      :right="10"
      :top="57"
      position-base="top-right"
      @click="downloadDrawerVisible = true"
    >
      <a-button
        class="w-22 ml-8 mr-2 flex items-center"
        type="primary"
        @click.stop.prevent
      >
        {{ $t('page.checkList.task.downloadList') }}
        <template #icon>
          <mynaui-download />
        </template>
      </a-button>
    </float>
    <download-drawer
      ref="downloadDrawerRef"
      v-model:visible="downloadDrawerVisible"
    />
  </div>
</template>
