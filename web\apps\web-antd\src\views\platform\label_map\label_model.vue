<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import TableTemplate from '#/components/table/index.vue';
import { updateCustmizeLabel, queryCustmizeLabel } from '#/api/ai-mark';
import ColorPicker from '#/components/color-picker/index.vue';
import { message } from 'ant-design-vue';
import { useApiHandler } from '#/composables/common/use-api-handler';
import { mapToOptions } from '#/utils/transform';
import { DETAIL_ENTRY_MAP } from '#/constants/maps/ai-mark';

const { loadings, handleApiCall } = useApiHandler();
const tableTemplateRef = ref<any>(null);
const loading = ref(false);

const shortKeyList = [
  'A',
  'B',
  'C',
  'D',
  'E',
  'F',
  'G',
  'H',
  'I',
  'J',
  'K',
  'L',
  'M',
  'N',
  'O',
  'P',
  'Q',
  'R',
  'S',
  'T',
  'U',
  'V',
  'W',
  'X',
  'Y',
  'Z',
  '1',
  '2',
  '3',
  '4',
  '5',
  '6',
  '7',
  '8',
  '9',
  '0',
  'Ctrl+A',
  'Ctrl+D',
  'Ctrl+E',
  'Ctrl+G',
  'Ctrl+H',
  'Ctrl+I',
  'Ctrl+J',
  'Ctrl+L',
  'Ctrl+M',
  'Ctrl+O',
  'Ctrl+P',
  'Ctrl+Q',
  'Ctrl+R',
  'Ctrl+S',
  'Ctrl+U',
  'Ctrl+X',
  'Ctrl+Y',
  'Ctrl+Z',
];

const sourceOptions = mapToOptions(DETAIL_ENTRY_MAP);

const props = defineProps<{
  purpose: string;
}>();

const getList = async (params: { purpose: string }) => {
  try {
    const res = await queryCustmizeLabel(params);
    return res;
  } catch (error) {
    console.error('查询失败:', error);
    throw error;
  }
};

const tableDataSource = ref<{ items: any[]; total: number }>({
  items: [],
  total: 0,
});

const fetchData = async () => {
  try {
    loading.value = true;
    const res = await getList({ purpose: props.purpose });
    const items = res.data.map((item: any) => ({
      ...item,
      entrys: item.entrys || [],
      edit: false,
    }));
    tableDataSource.value = { items, total: items.length };
    console.log(tableDataSource.value);
  } catch (error) {
    message.error('查询失败');
    tableDataSource.value = { items: [], total: 0 };
  } finally {
    loading.value = false;
  }
};

const checkParams = (data: any) => {
  if (!data.title || !data.short_key || !data.label) {
    message.warning('请输入完整信息');
    return false;
  }
  return true;
};

onMounted(() => {
  fetchData();
});

const saveItem = async (row: any, enable: number) => {
  if (!checkParams(row)) return;

  const params = {
    title: row.title,
    label_color: row.label_color,
    short_key: row.short_key,
    label: row.label,
    sentiment: Number(row.sentiment),
    enable,
    entrys: row.entrys,
    purpose: props.purpose,
    ...(row.id ? { id: row.id } : {}),
  };

  await handleApiCall(updateCustmizeLabel, params, {
    errorMsg: '快捷键已存在',
    onSuccess: () => {
      row.edit = false;
      row.enable = enable;
      fetchData();
    },
  });
};

const handleEdit = (row: any) => {
  row.edit = true;
};

const addSelfCustom = () => {
  tableDataSource.value.items.push({ edit: true, entrys: [], sentiment: null });
};

const paginationOptions = {
  enabled: false,
};

const columns = [
  {
    field: 'title',
    title: '标签名称',
    slots: { default: 'title' },
    width: 210,
  },
  {
    field: 'short_key',
    title: '快捷键',
    slots: { default: 'short_key' },
    width: 200,
  },
  {
    field: 'label_color',
    title: '颜色',
    slots: { default: 'label_color' },
    width: 180,
  },
  {
    field: 'label',
    title: '样本标签',
    slots: { default: 'label' },
    width: 200,
  },
  {
    field: 'sentiment',
    title: '样本导向',
    slots: { default: 'sentiment' },
    width: 200,
  },
  {
    field: 'entrys',
    title: '生效队列',
    slots: { default: 'entrys' },
    width: 330,
  },
  {
    field: 'operation',
    title: '操作',
    slots: { default: 'operation' },
  },
];

const searchOptions = {
  collapsed: false,
  showCollapseButton: false,
  submitOnChange: false,
  wrapperClass: 'grid-cols-4 gap-3',
  schemas: {},
};

const handleColorChange = (row: any, color: string) => {
  row.label_color = color;
};
</script>

<template>
  <div v-if="tableDataSource.items.length > 0">
    <table-template
      ref="tableTemplateRef"
      :columns="columns"
      :data="tableDataSource"
      :loading="loading"
      :pagination-options="paginationOptions"
      :search-options="searchOptions"
      class="setting-table"
    >
      <template #toolbar-left>
        <a-button type="primary" @click="addSelfCustom">新增标签</a-button>
      </template>

      <template #title="{ row }">
        <span v-if="row.id">{{ row.title }}</span>
        <a-input
          v-else
          v-model:value="row.title"
          class="w-40"
          placeholder="请输入标签名称"
        />
      </template>

      <template #short_key="{ row }">
        <span v-if="!row.edit && row.id">{{ row.short_key }}</span>
        <a-select
          v-else
          v-model:value="row.short_key"
          class="w-40"
          placeholder="请选择快捷键"
          allow-clear
          show-search
        >
          <a-select-option
            v-for="item in shortKeyList"
            :key="item"
            :value="item"
          >
            {{ item }}
          </a-select-option>
        </a-select>
      </template>

      <template #label_color="{ row }">
        <color-picker
          :value="row.label_color"
          :disable="!row.edit"
          @change="(color) => handleColorChange(row, color)"
          class="w-40"
        />
      </template>

      <template #label="{ row }">
        <span v-if="row.id">{{ row.label }}</span>
        <a-input
          v-else
          v-model:value="row.label"
          class="w-40"
          placeholder="请输入样本标签"
        />
      </template>

      <template #sentiment="{ row }">
        <a-select
          v-model:value="row.sentiment"
          :disabled="!!row.id"
          class="w-40"
          placeholder="请选择"
        >
          <a-select-option :value="1">白样本</a-select-option>
          <a-select-option :value="0">黑样本</a-select-option>
          <a-select-option :value="-1">研判</a-select-option>
        </a-select>
      </template>

      <template #entrys="{ row }">
        <a-select
          v-model:value="row.entrys"
          :disabled="!row.edit"
          mode="multiple"
          class="w-72"
          placeholder="请选择生效队列"
          allow-clear
          show-search
          :max-tag-count="1"
        >
          <a-select-option
            v-for="item in sourceOptions"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </a-select-option>
        </a-select>
      </template>

      <template #operation="{ row }">
        <div class="flex items-center justify-center gap-2">
          <a-button
            v-if="row.edit"
            type="primary"
            :loading="loadings.save"
            @click="saveItem(row, 1)"
          >
            保存
          </a-button>
          <a-button
            v-else
            :class="
              row.enable === 0 ? 'border-green-500 bg-green-500 text-white' : ''
            "
            :loading="loadings.save"
            @click="handleEdit(row)"
          >
            {{ row.enable === 1 ? '编辑' : '启用' }}
          </a-button>
          <a-popconfirm
            title="确定禁用吗？"
            ok-text="确定"
            cancel-text="取消"
            @confirm="saveItem(row, 0)"
          >
            <a-button
              v-if="row.enable === 1"
              type="primary"
              danger
              :loading="loadings.save"
            >
              禁用
            </a-button>
          </a-popconfirm>
        </div>
      </template>
    </table-template>
  </div>
</template>
