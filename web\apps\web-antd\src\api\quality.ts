import { requestClient } from '#/api/request';

export function getSupplierUsersService(data: any) {
  return requestClient.get('/manual/record/get_supplier_users', { data });
}

// 获取质检报表
export function getQualityMonthsReport(data: any) {
  return requestClient.get('/manual/quality/months_report', { data });
}

// 获取质检配置
export function getQualitySetListService(data: any) {
  return requestClient.get('/manual/quality/configs', { data });
}

// 获取质检配置
export function getQualitySettingService(data: any) {
  return requestClient.get('/manual/quality/config', { data });
}

// 质检员概况报表
export function getMemberQualityList(data: any) {
  return requestClient.get('/manual/quality/member_quality', { data });
}

export function delQuality(data: any) {
  return requestClient.delete('/manual/quality/config', { data });
}

//生成样本
export function qualitySample(data: any) {
  return requestClient.post('/manual/quality/sampling', data);
}

// 重置队列
export function resetQuality(data: any) {
  return requestClient.post('/manual/quality/reset_sample', data);
}

// 新增更新质检配置
export function updateQuality(data: any) {
  return requestClient.post('/manual/quality/config', data);
}
