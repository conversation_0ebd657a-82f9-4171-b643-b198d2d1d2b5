/* 本文件存放一些与数据处理、格式转换有关的公共方法 */

import type { LabelItem, TreeNode } from '#/types/category';

/**
 * 验证并处理请求参数
 * @param params 原始参数对象
 * @param options 配置选项
 * @returns 处理后的参数对象
 */

// 后续废弃
export const validateParams = (
  params: Record<string, any>,
  options?: {
    stringifyNumbers?: boolean;
  },
) => {
  // 参数校验
  if (!params || typeof params !== 'object') {
    return '';
  }

  // 过滤无效值并处理复杂类型
  const validParams = Object.fromEntries(
    Object.entries(params)
      .filter(([_, value]) => {
        if (value === undefined || value === null || value === '') {
          return false;
        }
        // 处理数组
        if (Array.isArray(value)) {
          return value.length > 0;
        }
        return true;
      })
      .map(([key, value]) => [
        key,
        // 处理数组和对象
        Array.isArray(value) || typeof value === 'object'
          ? JSON.stringify(value)
          : // 处理数字
          options?.stringifyNumbers && typeof value === 'number'
            ? String(value)
            : value,
      ]),
  );

  return new URLSearchParams(validParams).toString();
};

/**
 * 将对象映射为选项数组
 * @param map 原始对象
 * @param keyMap 键值映射配置，默认 { label: 'label', value: 'value' }
 * @returns 转换后的选项数组
 */
export const mapToOptions = (
  map: { [key: string]: string },
  keyMap: { label?: string; value?: string } = {
    label: 'label',
    value: 'value',
  },
) => {
  const { label = 'label', value = 'value' } = keyMap;

  return Object.entries(map).map(([val, text]) => ({
    [label]: text, // 动态设置 label 键
    [value]: val === ' ' ? val : isNaN(Number(val)) ? val : Number(val), // 动态设置 value 键
  }));
};

// 将数组转换为树形结构
export const arrayToMap = (array: any, key = 'value', value = 'label') => {
  return array.reduce((map: any, item: any) => {
    map[item[key]] = item[value];
    return map;
  }, {});
};

/**
 * 将标签列表转换为树形结构
 * @param labels 标签列表，每个标签包含 label（编码）、desc（描述）和 parent（父级编码）
 * @returns 转换后的树形结构数组
 *
 * 说明：
 * - 标签编码长度代表层级：2位为一级，3位为二级，5位为三级
 * - 例如：10(一级) -> 10A(二级) -> 10A20(三级)
 */
export function transformLabelsToTree(labels: LabelItem[]): TreeNode[] {
  // 使用 Map 存储不同层级的节点，key 为层级（2/3/5），value 为该层级所有节点的 Map
  const levelMaps = new Map<number, Map<string, TreeNode>>();
  // 存储最终的树形结构，只包含一级节点
  const treeData: TreeNode[] = [];

  // 初始化各层级的 Map：
  // - 2位编码对应一级节点
  // - 3位编码对应二级节点
  // - 5位编码对应三级节点
  levelMaps.set(2, new Map());
  levelMaps.set(3, new Map());
  levelMaps.set(5, new Map());

  // 遍历所有标签，构建树形结构
  labels.forEach((item) => {
    // 通过标签编码长度判断层级
    const level = item.label.length;
    // 创建当前节点
    const node: TreeNode = {
      label: item.desc, // 显示文本为标签描述
      value: item.label, // 节点值为标签编码
      children: level < 5 ? [] : undefined, // 非三级节点才有 children 数组
    };

    // 将节点存入对应层级的 Map 中
    const currentLevelMap = levelMaps.get(level);
    if (currentLevelMap) {
      currentLevelMap.set(item.label, node);

      if (level === 2) {
        // 一级节点直接加入结果数组
        treeData.push(node);
      } else {
        // 非一级节点找到父节点并添加到其 children 中
        const parentMap = levelMaps.get(item.parent.length);
        const parentNode = parentMap?.get(item.parent);
        parentNode?.children?.push(node);
      }
    }
  });

  return treeData;
}

// 添加百分比渲染函数
export const renderPercentage = (value: number) => {
  if (value === undefined || value === null) return '0%';
  const percentage = (Number(value) * 100).toFixed(2);
  return `${percentage}%`;
};
/**
 * 将对象转换为 URL 查询参数字符串
 * @param obj 要转换的对象
 * @param keys 可选的键数组（不传则转换所有可枚举属性）
 * @returns 生成的查询字符串（如 `"?name=foo&age=20"`），无参数时返回空字符串 `""`
 */
export function generateUrlParams(obj: any, keys?: string[]) {
  // 如果不传 keys，则默认使用对象的所有键
  const targetKeys = Array.isArray(keys) ? keys : Object.keys(obj);
  const params = targetKeys
    .filter((key) => obj.hasOwnProperty(key) &&
      obj[key] !== undefined &&
      obj[key] !== null
      ) // 过滤掉不存在的键
    .map((key) => `${key}=${encodeURIComponent(obj[key])}`)
    .join('&');
  return params ? `?${params}` : '?';
}

// 移除文件名后缀
export function removeFileExtension(filename: string) {
  if (!filename) return '';
  return filename.replace(/\.[^/.]+$/, '');
}
// 处理参数  将数据中需要转换成数字的字段转换成数字
export function convertFieldsToNumbers(obj: any, fieldsToConvert: string[]) {
  return fieldsToConvert.reduce(
    (result, field) => {
      if (field in result) {
        result[field] = Number(result[field]);
      }
      return result;
    },
    { ...obj },
  );
}

/**
 * 在树形结构中查找从叶子节点到根节点的完整路径
 *
 * @param items - 树形结构数组，每个节点都应该包含compareField指定的字段和可选的children字段
 * @param targetValue - 要查找的目标值
 * @param options - 配置选项
 * @param options.compareField - 用于比较的字段名，默认为'type'
 * @param options.childrenField - 子节点数组的字段名，默认为'children'
 * @returns 从根节点到目标节点的路径数组，如果未找到则返回null
 *
 * @example
 * // 示例1：查找类型为'leaf'的节点路径
 * const tree = [
 *   {
 *     type: 'root',
 *     children: [
 *       {
 *         type: 'branch',
 *         children: [
 *           { type: 'leaf' }
 *         ]
 *       }
 *     ]
 *   }
 * ];
 * const path = findPathToRoot(tree, 'leaf');
 * // 返回: ['root', 'branch', 'leaf']
 *
 * @example
 * // 示例2：使用自定义字段名
 * const tree = [
 *   {
 *     id: '1',
 *     subItems: [
 *       {
 *         id: '2',
 *         subItems: [
 *           { id: '3' }
 *         ]
 *       }
 *     ]
 *   }
 * ];
 * const path = findPathToRoot(tree, '3', {
 *   compareField: 'id',
 *   childrenField: 'subItems'
 * });
 * // 返回: ['1', '2', '3']
 */
export const findPathToRoot = <T extends Record<string, any>>(
  items: T[],
  targetValue: any,
  options: {
    compareField?: string;
    childrenField?: string;
  } = {},
): any[] | null => {
  const { compareField = 'type', childrenField = 'children' } = options;

  const findPath = (
    items: T[],
    targetValue: any,
    path: any[],
  ): any[] | null => {
    for (const item of items) {
      const currentPath = [...path, item[compareField]];
      if (item[compareField] === targetValue) {
        return currentPath;
      }
      if (item[childrenField]) {
        const found = findPath(item[childrenField], targetValue, currentPath);
        if (found) return found;
      }
    }
    return null;
  };

  return findPath(items, targetValue, []);
};

/**
 * 将对象中的特定字段转换为字符串类型
 * @param obj 要处理的对象
 * @param fieldsToConvert 需要转换为字符串的字段数组
 * @returns 处理后的新对象
 */
export function convertFieldsToString(obj: any, fieldsToConvert: string[]) {
  return fieldsToConvert.reduce(
    (result, field) => {
      if (
        field in result &&
        result[field] !== null &&
        result[field] !== undefined
      ) {
        result[field] = String(result[field]);
      }
      return result;
    },
    { ...obj },
  );
}

/**
 * 构建树形结构
 * @param data 数据源
 * @returns 构建后的树形结构
 */
// export function buildTree(data: any[]) {
//   const map = Object.create(null); // 使用更干净的空对象
//   const tree: any[] = [];
//   // 一次性遍历构建映射和树结构
//   data.forEach(item => {
//     const node = { ...item, children: [] };
//     map[item.label] = node;
//     if (item.parent) {
//       // 如果父节点还不存在，先创建一个空的
//       if (!map[item.parent]) {
//         map[item.parent] = { children: [] };
//       }
//       map[item.parent].children.push(node);
//     } else {
//       tree.push(node);
//     }
//   });

//   return tree;
// }
interface BuildTreeOptions {
  idKey?: string;
  parentKey?: string;
  childrenKey?: string;
}
export function buildTree(data: any[], options: BuildTreeOptions = {}) {
  const {
    idKey = 'id', // 节点标识字段名
    parentKey = 'parent', // 父节点引用字段名
    childrenKey = 'children', // 子节点集合字段名
  } = options;
  const map = Object.create(null);
  const tree: any[] = [];

  // 第一遍：创建所有节点的映射
  data.forEach((item) => {
    map[item[idKey]] = {
      ...item,
      [childrenKey]: item[childrenKey] || [],
    };
  });

  // 第二遍：构建树结构
  data.forEach((item) => {
    const node = map[item[idKey]];
    const parentId = item[parentKey];

    if (parentId !== undefined && parentId !== null && parentId !== '') {
      if (map[parentId]) {
        // 如果父节点存在，添加到父节点的children中
        map[parentId][childrenKey].push(node);
      } else {
        // 如果父节点不存在，作为根节点
        tree.push(node);
      }
    } else {
      // 没有父节点引用，作为根节点
      tree.push(node);
    }
  });

  return tree;
}

// 对象转 URL 查询参数（支持嵌套）
export function encodeURIData(obj: any) {
  return encodeURIComponent(JSON.stringify(obj));
}

// URL 参数转对象
export function decodeURIData(str: string) {
  return JSON.parse(decodeURIComponent(str));
}
