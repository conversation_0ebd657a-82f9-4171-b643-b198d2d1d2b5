<script lang="tsx" setup>
import { ref, watch } from 'vue';

import { ApiComponent } from '@vben/common-ui';

import { Cascader, Select } from 'ant-design-vue';

import { getSupplierUsersService } from '#/api/quality';

/**
 * 组件属性定义
 */
const props = defineProps({
  /**
   * 是否多选
   *
   * 控制选择器是否支持多选功能
   *
   * 示例:
   * ```vue
   * <user-select :multiple="true" />
   * ```
   */
  multiple: {
    type: Boolean,
    default: true,
  },
  /**
   * 是否使用级联选择器
   *
   * 当为true时显示层级结构（团队->用户），为false时显示扁平列表
   *
   * 示例:
   * ```vue
   * <user-select :is-cascader="true" />
   * ```
   */
  isCascader: {
    type: Boolean,
    default: false,
  },
  /**
   * 是否禁用
   *
   * 控制选择器是否为禁用状态
   *
   * 示例:
   * ```vue
   * <user-select :disabled="true" />
   * ```
   */
  disabled: {
    type: Boolean,
    default: false,
  },
  /**
   * 最大标签数量
   *
   * 在多选模式下显示的最大标签数，超出时显示"+N"
   *
   * 示例:
   * ```vue
   * <user-select :max-tag-count="3" />
   * ```
   */
  maxTagCount: {
    type: Number,
    default: 5,
  },
});

/**
 * 事件定义
 */
const emit = defineEmits(['update:modelValue', 'change']);

/**
 * 双向绑定的值
 */
const modelValue = defineModel('modelValue');

/**
 * 存储用户数据的引用，用于反向查找
 */
const userData = ref<any[]>([]);
/**
 * 将用户名数组转换为Cascader路径数组
 * @param usernames 用户名数组
 */
const convertUsernamesToPaths = (usernames: string[]) => {
  if (userData.value.length === 0) {
    console.log('用户数据还未加载，等待数据加载完成...');
    return;
  }

  const paths: string[][] = [];

  usernames.forEach((username) => {
    // 在userData中查找用户名对应的团队
    for (const team of userData.value) {
      console.log('team', team);
      if (team.children) {
        const user = team.children.find(
          (child: any) => child.label === username,
        );
        if (user) {
          paths.push([team.value, username]);
          console.log(`找到用户 ${username} 属于团队 ${team.label}`);
          break;
        }
      }
    }
  });

  if (paths.length > 0) {
    console.log('转换后的路径数组:', paths);
    // 更新值为路径数组格式
    modelValue.value = paths;
  }
};

/**
 * 当是Cascader模式时，监听值变化并打印调试信息
 */
watch(
  () => modelValue.value,
  (newValue: any, oldValue: any) => {
    if (props.isCascader) {
      console.log('UserSelect Cascader 值变化:', {
        oldValue,
        newValue,
        type: typeof newValue,
        isArray: Array.isArray(newValue),
      });

      // 如果传入的是用户名数组，需要转换为路径数组
      if (
        newValue &&
        Array.isArray(newValue) &&
        newValue.length > 0 &&
        typeof newValue[0] === 'string'
      ) {
        console.log('检测到用户名数组，开始转换为路径数组...');
        convertUsernamesToPaths(newValue);
      }
    }
  },
  { deep: true },
);

/**
 * 获取并处理供应商用户数据
 *
 * 根据isCascader属性决定返回层级结构还是扁平结构
 *
 * @returns Promise<any[]> 处理后的用户数据
 */
async function fetchUserData(): Promise<any[]> {
  try {
    const resultData = await getSupplierUsersService({});
    if (resultData && resultData.result) {
      if (props.isCascader) {
        // Cascader模式：保持完整的父子层级结构
        // 确保数据结构符合 Cascader 的要求（label, value, children）
        // 对于用户选择，我们需要确保value使用的是用户名而不是用户ID
        const cascaderData = resultData.data.map((team: any) => ({
          label: team.label,
          value: team.value, // 团队值保持不变
          children: (team.children || []).map((user: any) => ({
            label: user.label, // 用户显示名
            value: user.label, // 使用用户名作为value，而不是user.value（用户ID）
          })),
        }));

        // 存储用户数据供反向查找使用
        userData.value = cascaderData;
        console.log('UserSelect 用户数据已加载:', userData.value);

        // 如果当前值是用户名数组，尝试转换
        if (
          modelValue.value &&
          Array.isArray(modelValue.value) &&
          modelValue.value.length > 0 &&
          typeof modelValue.value[0] === 'string'
        ) {
          setTimeout(
            () => convertUsernamesToPaths(modelValue.value as string[]),
            100,
          );
        }

        return cascaderData;
      } else {
        // Select模式：扁平化处理，只取子元素（用户）
        return resultData.data.reduce((acc: any[], item: any) => {
          if (item.children) {
            acc.push(...item.children);
          }
          return acc;
        }, []);
      }
    }
    return [];
  } catch (error) {
    console.error('获取供应商用户数据失败:', error);
    return [];
  }
}

/**
 * 选择值变化处理
 *
 * 当用户选择项发生变化时触发
 *
 * @param value 选中的值
 */
const onChange = (value: string | string[]) => {
  emit('update:modelValue', value);
  emit('change', value);
};
const displayRender = ({ labels }: any) => {
  return <span>{labels.join(' / ')}</span>;
};
</script>

<template>
  <div>
    <!-- 使用ApiComponent包装Cascader或Select组件 -->
    <api-component
      v-model="modelValue"
      :api="fetchUserData"
      :component="isCascader ? Cascader : Select"
      :disabled="disabled"
      :immediate="true"
      :max-tag-count="maxTagCount"
      :multiple="multiple"
      :placeholder="isCascader ? '请选择团队和用户' : '请选择用户'"
      allow-clear
      children-field="children"
      class="w-full"
      label-field="label"
      loading-slot="suffixIcon"
      model-prop-name="value"
      show-search
      value-field="value"
      :display-render="isCascader ? displayRender : undefined"
      :show-checked-strategy="Cascader.SHOW_CHILD"
      @change="onChange"
    />
  </div>
</template>
