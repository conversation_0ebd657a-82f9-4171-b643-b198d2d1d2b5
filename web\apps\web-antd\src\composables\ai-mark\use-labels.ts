import { ref, computed, onMounted, type Ref } from 'vue';
import { queryCustmizeLabel } from '#/api/ai-mark';

export function useLabels(entry: Ref<string>, lazyEntry: boolean) {
  // 标签相关状态
  const labelList = ref<any[]>([]);
  const isLabelLoading = ref<boolean>(false);

  // 获取标签名称 - 使用computed缓存函数
  const getLabel = computed(() => (typeId: number): string => {
    const label = labelList.value.find((item) => item.type === typeId);
    return label ? label.label : '';
  });

  // 获取标签颜色 - 使用computed缓存函数
  const getLabelColor = computed(() => (typeId: number): string => {
    const label = labelList.value.find((item) => item.type === typeId);
    return label ? label.label_color : '#cccccc';
  });

  // 加载标签列表
  const loadLabels = async () => {
    try {
      isLabelLoading.value = true;
      // const resp = await queryCustmizeLabel({ show_level: true, enable: 1, entry: entry.value });
      const resp = await queryCustmizeLabel({ enable: 1 , entry: entry.value });
      if (resp.data) {
        labelList.value = resp.data;
      }
    } catch (error) {
      console.error('加载标签失败:', error);
    } finally {
      isLabelLoading.value = false;
    }
  };

  // 通过快捷键找到对应标签
  const getLabelByShortKey = (key: string) => {
    return labelList.value.find((label) => label.short_key === key);
  };

  // 在组件挂载时加载标签
  onMounted(() => {
    if (!lazyEntry || entry.value) {
      loadLabels();
    }
  });

  return {
    labelList,
    isLabelLoading,
    getLabel,
    getLabelColor,
    getLabelByShortKey,
    loadLabels,
  };
}
