// composables/useApiHandler.ts
import { reactive, ref } from 'vue';
import { message } from 'ant-design-vue';
import type { ApiCallOptions } from '#/types/api';

/**
 * 扩展的API调用选项
 */
export interface ExtendedApiCallOptions extends ApiCallOptions {
  /** 行ID，用于按行管理loading状态 */
  rowId?: string;
  /** 表格引用，用于自动刷新 */
  tableRef?: any;
  /** 是否显示消息，如果为false则不显示任何消息 */
  showMessage?: boolean;
  /** 是否自动刷新表格 */
  autoRefresh?: boolean;
}

export function useApiHandler() {
  const loadings = reactive<Record<string, boolean>>({});
  // 按行ID管理的loading状态
  const rowLoadings = ref<Record<string, Set<string>>>({}); // action -> Set<rowId>
  
  /**
   * 检查指定行是否处于loading状态
   * @param action 操作类型
   * @param rowId 行ID
   * @returns 是否正在loading
   */
  const isRowLoading = (action: string, rowId: string): boolean => {
    return rowLoadings.value[action]?.has(rowId) || false;
  };

  /**
   * 设置行loading状态
   * @param action 操作类型
   * @param rowId 行ID
   * @param loading 是否loading
   */
  const setRowLoading = (action: string, rowId: string, loading: boolean) => {
    if (!rowLoadings.value[action]) {
      rowLoadings.value[action] = new Set();
    }
    if (loading) {
      rowLoadings.value[action].add(rowId);
    } else {
      rowLoadings.value[action].delete(rowId);
    }
  };

  /**
   * 处理 API 调用
   * @param apiFn API 函数
   * @param params API 参数
   * @param options 配置选项
   */
  const handleApiCall = async (
    apiFn: (...args: any[]) => Promise<any>,
    params: any,
    options: ExtendedApiCallOptions = {},
  ) => {
    const {
      loadingKey = 'default',
      rowId,
      successMsg = '操作成功',
      errorMsg = '操作失败,请稍后再试~',
      onSuccess,
      onError,
      tableRef,
      showMessage = true,
      autoRefresh = true,
    } = options;

    // 生成action key，用于行loading管理
    const actionKey = rowId ? loadingKey : '';

    // 如果有rowId，检查是否已在loading中
    if (rowId && isRowLoading(actionKey, rowId)) {
      console.warn(`操作 ${actionKey} 对行 ${rowId} 正在进行中，忽略重复请求`);
      return false;
    }

    // 设置loading状态
    if (rowId) {
      setRowLoading(actionKey, rowId, true);
    } else {
      loadings[loadingKey] = true;
    }

    try {
      const res = await apiFn(params);
      if (res.result) {
        if (showMessage) {
          message.success(successMsg);
        }
        // 自动刷新表格
        if (autoRefresh && tableRef?.refreshTable) {
          tableRef.refreshTable();
        }
        onSuccess?.(res.data);
        return true;
      } else {
        if (showMessage) {
          message.error((res as any).message || errorMsg);
        }
        onError?.();
        return false;
      }
    } catch (error) {
      if (showMessage) {
        message.error(errorMsg);
      }
      onError?.(error);
      console.error('API调用错误:', error);
      return false;
    } finally {
      // 清除loading状态
      if (rowId) {
        setRowLoading(actionKey, rowId, false);
      } else {
        loadings[loadingKey] = false;
      }
    }
  };

  return {
    loadings, // 返回所有 loading 状态
    rowLoadings, // 返回按行的loading状态
    isRowLoading, // 检查行loading状态的方法
    handleApiCall
  };
}
