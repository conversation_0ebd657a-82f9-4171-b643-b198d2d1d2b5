<script lang="ts" setup>
import type { ImageItem } from '#/types/task';

import { computed, nextTick, onUnmounted, ref, useTemplateRef, watch } from 'vue';

import { storeToRefs } from 'pinia';

import { testImageUrlService } from '#/api/tools';
import ImageView from '#/components/image/index.vue';
import { useTaskStore } from '#/store';

// 定义图片加载状态类型
type ImageLoadingState = 'failed' | 'loaded' | 'loading' | 'not_loaded';

const props = defineProps<{
  batchSize?: number;
  columnWidth?: number;
  file?: any;
  gap?: number;
  images: ImageItem[];
  onLoadImages?: (params: ImageItem[]) => Promise<string[]>;
  rootRef?: HTMLElement | null;
}>();

const emit = defineEmits(['loaded', 'imageLoad']);

// 响应式变量
const containerRef = useTemplateRef<HTMLElement>('container');
const displayedImages = ref<ImageItem[]>([]);
const imageObserver = ref<IntersectionObserver | null>(null);
const imageLoaderRef = useTemplateRef<HTMLDivElement>('imageLoader');
const imageLoadBatchSize = ref(props.batchSize || 10); // 每次加载的图片数量
const currentImageIndex = ref(0);
const masonry = ref<HTMLElement | null>(null);
const loadedIndices = ref<Record<number, ImageLoadingState>>({});
// QR码相关状态
const qrCodeVisible = ref(false);
const qrInfo = ref<string[]>([]);
const { taskItem } = storeToRefs(useTaskStore());
// 新增：记录加载失败的图片数量
const failedImagesCount = ref(0);
// 新增：当前预览的图片索引（用于大图查看时的左右切换）
const currentPreviewIndex = ref<number>(0);
// 新增：全局大图查看器显示状态
const showGlobalViewer = ref<boolean>(false);

// 添加调试：监听currentPreviewIndex变化
watch(
  () => currentPreviewIndex.value,
  (newIndex, oldIndex) => {
    console.log('currentPreviewIndex 变化:', { oldIndex, newIndex, totalImages: displayedImages.value.length });
  }
);

// 添加调试：监听showGlobalViewer变化
watch(
  () => showGlobalViewer.value,
  (newValue, oldValue) => {
    console.log('showGlobalViewer 变化:', { oldValue, newValue });
  }
);

/**
 * 检查字符串是否为链接
 * @param str 待检查的字符串
 * @returns 是否为链接
 */
const isLink = (str: string) => {
  return /^https?:\/\//i.test(str);
};

/**
 * 处理QR码链接点击事件
 * @param link QR码链接文本
 */
const qrLink = (link: string) => {
  if (isLink(link)) {
    window.open(link, '_blank');
  }
};
/**
 * 处理图片双击事件，尝试识别QR码
 * @param image 图片URL
 */
const onImageDbClick = async (image: string) => {
  try {
    const params = {
      req_args: { extract_qr: 'true' },
      image_urls: [image],
    };

    const resp = await testImageUrlService(params);
    qrInfo.value = resp.data[0];

    if (qrInfo.value.length > 0) {
      qrCodeVisible.value = true;
    }
  } catch (error) {
    console.error('二维码识别失败', error);
  }
};


/**
 * 包装图片点击事件，先设置索引再触发预览
 * @param index 图片索引
 */
const handleImageClick = (index: number) => {
  // 先设置当前预览索引
  currentPreviewIndex.value = index;
  showGlobalViewer.value = true;
  console.log('handleImageClick - 设置索引并打开查看器:', { index, showGlobalViewer: showGlobalViewer.value });
};

/**
 * 处理切换到上一张图片
 */
const handlePrevImage = () => {
  console.log('handlePrevImage - 当前索引:', currentPreviewIndex.value, '总数:', displayedImages.value.length);
  if (currentPreviewIndex.value > 0) {
    currentPreviewIndex.value--;
    console.log('handlePrevImage - 更新后索引:', currentPreviewIndex.value);
  }
};

/**
 * 处理切换到下一张图片
 */
const handleNextImage = () => {
  console.log('handleNextImage - 当前索引:', currentPreviewIndex.value, '总数:', displayedImages.value.length);
  if (currentPreviewIndex.value < displayedImages.value.length - 1) {
    currentPreviewIndex.value++;
    console.log('handleNextImage - 更新后索引:', currentPreviewIndex.value);
  }
};

/**
 * 关闭全局大图查看器
 */
const handleCloseViewer = () => {
  showGlobalViewer.value = false;
  console.log('handleCloseViewer - 关闭查看器');
};

/**
 * 加载更多图片
 */
const loadMoreImages = async () => {
  if (currentImageIndex.value >= props.images.length) return;

  const endIndex = Math.min(
    currentImageIndex.value + imageLoadBatchSize.value,
    props.images.length,
  );

  try {
    // 获取当前批次的图片参数
    // 创建key到原始图片项的映射
    const currentBatch =
      props.images.slice(currentImageIndex.value, endIndex) || [];
    const keyToImageMap = new Map<string, ImageItem>();

    // 标记图片为加载中状态
    for (let i = currentImageIndex.value; i < endIndex; i++) {
      loadedIndices.value[i] = 'loading';
    }

    const imagesToLoad = currentBatch.filter((item) => {
      if (item.key) {
        keyToImageMap.set(item.key, item);
        return true;
      }
      return false;
    });
    // 如果有需要加载的图片参数，则调用onLoadImages方法
    if (imagesToLoad.length > 0 && props.onLoadImages) {
      const respUrls = await props.onLoadImages(imagesToLoad);

      // 更新图片URL
      for (const [index, newUrl] of respUrls.entries()) {
        const originalItem = imagesToLoad[index];
        if (newUrl && originalItem?.key) {
          // 通过key找到对应的原始图片项并更新URL
          const targetItem = keyToImageMap.get(originalItem.key);
          if (targetItem) {
            targetItem.url = newUrl;
          }
        }
      }
    }

    displayedImages.value = [...displayedImages.value, ...currentBatch];
    currentImageIndex.value = endIndex;
  } catch (error) {
    console.error('Failed to load images:', error);
  }
};

/**
 * 检查是否存在滚动条，如果不存在则加载更多图片
 */
const checkAndLoadImages = () => {
  if (!containerRef.value) return;
  // 检查是否存在滚动条
  const hasScrollbar =
    containerRef.value.scrollHeight > containerRef.value.clientHeight;

  // 如果没有滚动条，加载更多图片
  if (!hasScrollbar && displayedImages.value.length < props.images.length) {
    loadMoreImages();
    nextTick(() => {
      checkAndLoadImages(); // 递归检查，直到出现滚动条
    });
  }
};

const onImageLoad = (index: number) => {
  if (currentImageIndex.value >= props.images.length - 1) {
    emit('loaded');
  }
  emit('imageLoad');
  loadedIndices.value[index] = 'loaded';
};

/**
 * 设置图片懒加载的交叉观察器
 */
const setupImageObserver = () => {
  if (imageObserver.value) {
    imageObserver.value.disconnect();
  }

  imageObserver.value = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (
          entry.isIntersecting &&
          currentImageIndex.value < props.images.length
        ) {
          loadMoreImages();
        }
      });
    },
    {
      root: props.rootRef || containerRef.value,
      rootMargin: '0px 0px 200px 0px', // 提前200px触发加载
      threshold: 0.1,
    },
  );

  if (imageLoaderRef.value) {
    imageObserver.value.observe(imageLoaderRef.value);
  }
};

/**
 * 更新瀑布流布局
 */
const updateMasonryLayout = () => {
  console.log(props.images);
  if (!masonry.value) return;
  const items = masonry.value.querySelectorAll<HTMLElement>('.masonry-item');
  const columnWidth = props.columnWidth || 200; // 图片宽度
  const gap = props.gap || 24; // 间距
  const maxHeight = 800; // 固定最大高度

  // 计算可以容纳的列数
  const containerWidth = masonry.value.offsetWidth;
  const columnsCount = Math.floor((containerWidth + gap) / (columnWidth + gap));

  if (columnsCount <= 0) return;

  // 初始化每列的高度
  const columnsHeight: number[] = Array.from<number>({
    length: columnsCount,
  }).fill(0);
  
  // 保存每个项目的底部位置，用于计算容器高度
  let maxBottomPosition = 0;
  
  // 为每个元素找到最短的列并放置
  items.forEach((item: HTMLElement, index: number) => {
    const image = displayedImages.value[index];
    if (!image) return;

    // 找到高度最小的列
    const minHeightIndex = columnsHeight.indexOf(Math.min(...columnsHeight));
    // 设置元素位置
    const leftPosition = minHeightIndex * (columnWidth + gap);
    const topPosition = columnsHeight[minHeightIndex] || 0;
    item.style.position = 'absolute';
    item.style.left = `${leftPosition}px`;
    item.style.top = `${topPosition}px`;
    // 永远设置布局宽度为columnWidth，确保布局计算正确
    item.style.width = `${columnWidth}px`;

    // 使用后端返回的宽高信息计算显示高度
    const naturalRatio = image.height / image.width;
    let displayHeight = columnWidth * naturalRatio;
    
    // 对于特别长的图片进行特殊处理
    if (displayHeight > maxHeight) {
      displayHeight = maxHeight;
      // 计算按比例缩小后的实际显示宽度（保持原始宽高比）
      const scaledWidth = maxHeight / naturalRatio;
      
      // 添加长图标记
      item.classList.add('long-image');
      
      // 直接找到对应的image-view元素并设置宽度
      nextTick(() => {
        const imageView = item.querySelector('image-view');
        if (imageView instanceof HTMLElement) {
          imageView.style.width = `${scaledWidth}px`;
          imageView.style.maxWidth = `${columnWidth - 10}px`;
        }
      });
    } else {
      // 移除长图标记
      item.classList.remove('long-image');
      
      // 恢复正常宽度
      nextTick(() => {
        const imageView = item.querySelector('image-view');
        if (imageView instanceof HTMLElement) {
          imageView.style.width = '100%';
          imageView.style.maxWidth = '';
        }
      });
    }
    
    // 设置元素高度
    item.style.height = `${displayHeight}px`;

    // 更新该列的高度
    if (columnsHeight[minHeightIndex] !== undefined) {
      columnsHeight[minHeightIndex] += displayHeight + gap;
    }
    
    // 计算此项目的底部位置
    const itemBottomPosition = topPosition + displayHeight;
    // 更新最大底部位置
    maxBottomPosition = Math.max(maxBottomPosition, itemBottomPosition);
  });
  
  // 设置容器的最小高度，确保能包含所有项目
  // 增加额外的空间以便底部的加载指示器或"没有更多图片"提示能显示在所有图片下方
  const extraSpace = 100; // 额外空间，确保"没有更多图片"提示不会与图片重叠
  const masonryHeight = maxBottomPosition + extraSpace;
  
  // 设置容器高度
  masonry.value.style.height = `${masonryHeight}px`;
};

// 初始化加载
const initialize = () => {
  // 清空显示的图片和重置索引
  displayedImages.value = [];
  currentImageIndex.value = 0;
  // 重置失败图片计数
  failedImagesCount.value = 0;

  // 如果没有图片或参数，直接返回
  if (!props.images?.length) {
    return;
  }

  // 清除现有状态并将所有图片初始状态设置为未加载
  loadedIndices.value = {};
  for (let i = 0; i < props.images.length; i++) {
    loadedIndices.value[i] = 'not_loaded';
  }
  // 开始加载图片
  checkAndLoadImages();
};

// 监听容器参考变化
watch(
  () => [containerRef.value, props.rootRef],
  () => {
    if (containerRef.value) {
      setupImageObserver();
    }
  },
  { immediate: true },
);

// 监听瀑布流布局更新
watch(
  () => displayedImages.value,
  () => {
    nextTick(() => {
      updateMasonryLayout();
    });
  },
);

// 监听props.images和props.imageParams的变化，重新初始化
watch(
  () => [props.images],
  (newVal, oldVal) => {
    // 检查是否真的有变化
    const [newImages = []] = newVal;
    const [oldImages = []] = oldVal || [];

    // 检查数组长度是否变化
    const imagesLengthChanged = newImages.length !== oldImages.length;

    // 如果有变化，则重新初始化
    if (imagesLengthChanged) {
      initialize();
    }
  },
  { immediate: true },
);

// 在组件卸载前清理观察器
onUnmounted(() => {
  if (imageObserver.value) {
    imageObserver.value.disconnect();
  }
  // 移除窗口大小调整监听
  window.removeEventListener('resize', updateMasonryLayout);
});
const onImageError = (index: number) => {
  console.error(`Image at index ${index} failed to load`);
  loadedIndices.value[index] = 'failed';
  failedImagesCount.value++; // 增加失败计数
};
// 添加窗口大小调整监听
setTimeout(() => {
  window.addEventListener('resize', updateMasonryLayout);
}, 0);

// 监听窗口大小变化
window.addEventListener('resize', () => {
  setTimeout(() => {
    updateMasonryLayout();
  }, 100);
});

// 计算属性：判断是否所有图片都已加载完成
const allImagesLoaded = computed(() => {
  return currentImageIndex.value >= props.images.length;
});

// 计算属性：判断是否应该显示"没有更多图片"提示
const shouldShowNoMoreImages = computed(() => {
  return allImagesLoaded.value && props.images.length > 0;
});

// 计算属性：判断是否应该显示加载中状态
const isLoading = computed(() => {
  return currentImageIndex.value < props.images.length;
});

/**
 * 计算属性：获取当前显示图片的URL列表（用于大图切换）
 * @returns {string[]} 图片URL列表
 */
const displayedImageUrls = computed(() => {
  return displayedImages.value.map(img => img.url).filter(Boolean) as string[];
});
</script>

<template>
  <div ref="container" class="h-full w-full">
    <div ref="masonry" class="relative min-h-[200px] w-full">
      <div
        v-for="(image, index) in displayedImages"
        :key="index"
        :style="{ width: `${props.columnWidth || 200}px` }"
        class="masonry-item absolute overflow-hidden transition-all duration-300 ease-in-out"
      >
        <div class="relative h-full w-full flex justify-center">
          <image-view
            :file-item="taskItem"
            :image-item="image"
            :src="image.url"
            :current-index="currentPreviewIndex"
            :total-images="displayedImages.length"
            :image-list="displayedImageUrls"
            :is-current="index === currentPreviewIndex"
            :show-global-viewer="showGlobalViewer"
            :enable-image-navigation="true"
            :show-overlay="true"
            :overlay-color="'#737373'"
            class="h-full"
            @dblclick="onImageDbClick(image.url)"
            @error="onImageError(index)"
            @load="onImageLoad(index)"
            @prev-image="handlePrevImage"
            @next-image="handleNextImage"
            @close-viewer="handleCloseViewer"
            @click="() => handleImageClick(index)"
          />
        </div>
      </div>
    </div>

    <div ref="imageLoader" class="h-10 w-full items-center justify-center" style="margin-top: 20px;">
      <a-spin v-if="isLoading" />
      <div
        v-else-if="shouldShowNoMoreImages"
        class="flex w-full justify-center pb-2"
      >
        没有更多图片~
      </div>
    </div>

    <!-- 二维码信息对话框 -->
    <a-modal
      v-model:open="qrCodeVisible"
      :footer="null"
      class="z-[1000]"
      title="二维码信息"
      width="30%"
    >
      <div
        v-for="(item, index) in qrInfo"
        :key="index"
        class="qrcode-item py-1"
      >
        <a
          :class="{ 'cursor-pointer text-blue-500 underline': isLink(item) }"
          @click="qrLink(item)"
        >
          {{ item }}
        </a>
      </div>
    </a-modal>
  </div>
</template>

<style scoped>
.masonry-item {
  position: absolute;
  transition: all 0.1 ease;
}

.masonry-item :deep(img) {
  object-fit: cover; /* 使用cover替代contain，确保图片填满整个容器 */
}

/* 长图片特殊处理 */
.masonry-item.long-image :deep(image-view) {
  width: auto; /* 允许宽度自动缩放 */
  max-width: calc(100% - 10px); /* 确保不会超出容器 */
}

/* 让JavaScript和CSS配合，使用attr()读取data-scaled-width属性 */
.masonry-item.long-image {
  display: flex;
  justify-content: center;
}
</style>
