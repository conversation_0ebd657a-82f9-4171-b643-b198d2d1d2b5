<script setup lang="ts">
import violation from "./violation.vue";
import { getList } from "#/api/black-list";
import { NO_LIMIT_NUM } from "#/constants/num";

const props = withDefaults(
  defineProps<{
    tabPlatform: string;
  }>(),
  {
    tabPlatform: "",
  }
);

async function getListData({
  page,
  pageSize,
  ...formValues
}: {
  page: number;
  pageSize: number;
  [key: string]: any;
}) {
  let init: any = {
    from: props.tabPlatform,
    page: page - 1,
    limit: pageSize,
  };
  console.log("formValues11", formValues);
  for (const i in formValues) {
    formValues[i] ? (init[i] = formValues[i]) : null;
  }
  if (init.type) init.type = init.type[init.type.length - 1];
  init["start_time"] = Math.round(init["start_time"] / 1000);
  init["end_time"] = Math.round(init["end_time"] / 1000);
  const res: any = await getList(init);
  if (res && res.result) {
    let list = res.data.data || [];
    return {
      items: list,
      total: res.data.more ? NO_LIMIT_NUM : 0,
    };
  }
}
</script>
<template>
  <violation :tabPlatform="props.tabPlatform" :queryMethod="getListData"></violation>
</template>
