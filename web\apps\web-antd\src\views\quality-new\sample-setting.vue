<script setup lang="ts">
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { EnhancedFormSchema } from '#/components/form/enhanced-form.vue';

import { onBeforeMount, ref, shallowRef } from 'vue';

import { supplierRegisterService } from '#/api/platform';
import {
  delQuality,
  getQualitySettingService,
  qualitySample,
  resetQuality,
  updateQuality,
} from '#/api/quality';
import EnhancedForm from '#/components/form/enhanced-form.vue';
import ProductSelect from '#/components/select/product.vue';
import QueueSelect from '#/components/select/queue.vue';
import SupplierSelect from '#/components/select/supplier.vue';
import UserSelect from '#/components/select/user.vue';
import TableTemplate from '#/components/table/index.vue';
import { useApiHandler } from '#/composables/common/use-api-handler';
import { QUALITY_MODULE_TYPE_MAP } from '#/constants/maps/statistics';
import { FORBID_FIELD_CONFIGS } from '#/constants/options';
import { mapToOptions } from '#/utils/transform';

import ForbidType from './components/forbid-type.vue';
import SampleStatus from './components/sample-status.vue';

const { handleApiCall, isRowLoading } = useApiHandler();

// 表格组件引用
const tableTemplateRef = ref<any>(null);
// 控制编辑模态框的显示
const isEditModalVisible = ref<boolean>(false);
// 当前编辑的行数据
const currentEditRow = ref<any>(null);
const teamMap = ref<Record<string, string>>({});
// 状态类型映射

const columns: VxeGridProps['columns'] = [
  { type: 'seq', width: 60, title: '序号' },
  { field: 'biz_name', title: '业务名称', align: 'center' },
  {
    field: 'module_type',
    title: '模块类型',
    align: 'center',
    formatter: ({ cellValue }) => {
      const value = cellValue || '';
      return QUALITY_MODULE_TYPE_MAP[value] || '';
    },
  },
  {
    field: 'supplier',
    title: '团队',
    align: 'center',
    formatter: ({ cellValue }) => {
      return teamMap.value[cellValue] || '';
    },
  },
  {
    fixed: 'right',
    title: '操作',
    width: 350,
    slots: { default: 'action' },
  },
];

// 表单组件引用
const formRef = ref<any>(null);
const commonConfig = {
  labelWidth: 80,
  componentProps: {
    class: 'w-full',
  },
};
const schema: EnhancedFormSchema[] = [
  {
    fieldName: 'module_type',
    component: 'Select',
    componentProps: {
      class: 'w-full',
      placeholder: '请选择模块',
      options: mapToOptions(QUALITY_MODULE_TYPE_MAP),
    },
    label: '模块',
    required: true,
  },
  {
    fieldName: 'biz',
    component: shallowRef(ProductSelect),
    disableAutoBinding: true, // 禁用自动事件绑定
    componentProps: {
      class: 'w-full',
      productLineType: 'quality_platform',
      placeholder: '请选择业务编号',
      modelPropName: 'value',
      immediate: true,
      // 业务编号改变时清空工单来源，并设置业务名称
      onChange: (option: any) => {
        // 使用setTimeout确保在下次tick时执行，避免与表单更新冲突
        setTimeout(() => {
          console.log('option', option);
          if (formRef.value && formRef.value.setFieldsValue) {
            formRef.value.setFieldsValue({
              origin_entries: [], // 清空工单来源
              biz_name: option?.label || '', // 设置业务名称
            });
          }
        }, 0);
      },
    },
    label: '业务编号',
    required: true,
  },
  {
    fieldName: 'biz_name',
    component: 'Input',
    formItemClass: 'hidden', // 隐藏该字段
    componentProps: {
      style: { display: 'none' }, // 确保完全隐藏
    },
    label: '业务名称',
  },
  {
    fieldName: 'supplier',
    component: shallowRef(SupplierSelect),
    disableAutoBinding: true, // 禁用自动事件绑定
    componentProps: {
      class: 'w-full',
      placeholder: '请选择团队',
      immediate: true,
    },
    label: '团队',
  },
  {
    fieldName: 'time',
    label: '时间',
    component: 'RangePicker',
    componentProps: {
      showTime: false, // 不显示时间选择器
      valueFormat: 'X', // 直接返回时间戳
      placeholder: ['开始日期', '结束日期'],
      class: 'w-full',
    },
    required: true,
  },
  {
    fieldName: 'sample_status',
    label: '样本状态',
    component: shallowRef(SampleStatus),
    disableAutoBinding: true, // 禁用自动事件绑定
    componentProps: {
      defaultStatus: 1,
      isFormField: true,
      onChange: (values: any) => {
        console.log('样本状态onChange:', values);
      },
    },
  },
  {
    fieldName: 'forbid_type',
    label: '违规类型',
    component: shallowRef(ForbidType),
    disableAutoBinding: true, // 禁用自动事件绑定
    componentProps: {
      defaultType: 1,
      isFormField: true,
      onChange: (values: any) => {
        console.log('违规类型组件onChange:', values);
        console.log('违规类型数据结构:', {
          type: values?.type,
          randomPercent: values?.randomPercent,
          percentages: values?.percentages,
        });
      },
    },
  },
  {
    component: shallowRef(QueueSelect),
    fieldName: 'origin_entries',
    label: '工单来源',
    modelPropName: 'model-value', // 明确指定绑定属性
    componentProps: {
      placeholder: '请选择工单来源',
      immediate: false, // 改为false，避免没有biz时立即加载
      multiple: true,
    },
    dependencies: {
      componentProps(values: any) {
        console.log('values', values.biz);
        return {
          biz: values.biz, // 动态更新 options
          immediate: !!values.biz, // 只有当biz有值时才立即加载
        };
      },
      // 添加显示条件：只有选择了业务编号后才显示工单来源字段
      show(values: any) {
        return !!values.biz; // 只有当biz有值时才显示该字段
      },
      triggerFields: ['biz'],
    },
    required: true,
  },
  {
    fieldName: 'man_access',
    label: '进审类型',
    component: 'Select',
    componentProps: {
      class: 'w-full',
      placeholder: '请选择进审类型',
      options: [
        { value: '', label: '全部' },
        { value: 'preview_forbid', label: '先审后发' },
        { value: 'preview_ok', label: '先发后审' },
      ],
    },
  },
  {
    component: shallowRef(UserSelect),
    fieldName: 'user',
    label: '操作角色',
    disableAutoBinding: true, // 禁用自动事件绑定
    componentProps: {
      placeholder: '请选择操作角色',
      class: 'w-full',
      isCascader: true,
      // 确保返回的是用户名而不是用户ID
      labelInValue: false, // 不返回{label, value}对象，直接返回value
    },
    required: true,
  },
];
const getKeywordOpLogs = async ({
  page,
  pageSize,
  ...formValues
}: {
  [key: string]: any;
  page: number;
  pageSize: number;
}) => {
  try {
    const baseParams = {
      page: page - 1,
      limit: pageSize,
      ...formValues,
    };
    const baseResponseData = await getQualitySettingService(baseParams);
    return {
      items: baseResponseData.data || [],
      total: baseResponseData.data.length || 0,
    };
  } catch {
    return {
      items: [],
      total: 0,
    };
  }
};

// 设置分页配置，通过 enabled: false 来取消分页显示
const paginationOptions = {
  enabled: false,
};
const isEdit = ref(false);
/**
 * 处理添加操作
 * 设置为新增模式，清空表单数据，显示弹窗
 */
const handleAdd = () => {
  // 设置为添加模式
  isEdit.value = false;
  currentEditRow.value = null;
  isEditModalVisible.value = true;

  // 弹窗显示后重置表单并设置默认值
  setTimeout(() => {
    if (formRef.value) {
      // 先重置表单
      formRef.value.resetForm();

      // 设置默认值，确保字段有正确的初始状态
      const defaultForbidType = {
        type: 1,
        randomPercent: 0,
        percentages: FORBID_FIELD_CONFIGS.reduce(
          (acc, { field }) => {
            acc[field] = 0;
            return acc;
          },
          {} as Record<string, number>,
        ),
      };

      console.log('新增模式 - 设置默认违规类型:', defaultForbidType);

      formRef.value.setFieldsValue({
        module_type: '',
        biz: '',
        biz_name: '', // 业务名称初始化为空
        supplier: '',
        time: undefined,
        sample_status: {
          status: 1,
          randomPercent: 0,
          okPercent: 0,
          forbidPercent: 0,
        },
        forbid_type: defaultForbidType,
        origin_entries: [], // 确保工单来源初始化为空数组
        man_access: '',
        user: undefined,
      });
    }
  }, 100); // 延长等待时间，确保组件完全加载
};

// 处理编辑
const handleEdit = (row: any) => {
  // 设置为编辑模式
  isEdit.value = true;
  currentEditRow.value = { ...row };
  console.log('currentEditRow', currentEditRow.value);
  isEditModalVisible.value = true;

  // 延长等待时间，确保异步组件加载完成
  setTimeout(() => {
    console.log('编辑模式 - 开始设置表单数据');
    console.log('formRef.value:', formRef.value);
    if (formRef.value) {
      // 确定样本状态值
      const sampleStatus =
        currentEditRow.value.status_random_percent > 0 ? 1 : 2;

      // 确定违规类型配置
      const isRandomType = currentEditRow.value.type_random_percent > 0;
      const forbidTypeValue = {
        type: isRandomType ? 1 : 2,
        randomPercent: currentEditRow.value.type_random_percent || 0,
        percentages: {} as Record<string, number>,
      };

      // 填充违规类型百分比数据
      FORBID_FIELD_CONFIGS.forEach(({ field }) => {
        forbidTypeValue.percentages[field] = currentEditRow.value[field] || 0;
      });

      /**
       * 处理操作角色字段的格式转换
       * reviewers字段只包含用户名数组，UserSelect组件需要自己匹配对应的团队
       */
      let userValue = null;

      // 获取reviewers用户名数组
      const reviewers = currentEditRow.value.reviewers;

      if (reviewers && Array.isArray(reviewers) && reviewers.length > 0) {
        // 直接传递用户名数组给UserSelect组件
        // UserSelect组件会根据用户名自动匹配到对应的团队路径
        userValue = reviewers;
        console.log('reviewers用户名数组:', userValue);
      } else if (currentEditRow.value.user) {
        // 兼容可能存在的user字段（备用方案）
        userValue = currentEditRow.value.user;
        console.log('使用备用user字段:', userValue);
      }

      // 格式化数据以适应表单组件
      const formattedData = {
        ...currentEditRow.value,
        module_type: String(currentEditRow.value.module_type || ''),
        biz_name: currentEditRow.value.biz_name || '', // 确保业务名称被设置
        // 时间字段格式 - 直接传递时间戳，让enhanced-form组件自动转换为dayjs对象
        time:
          currentEditRow.value.start_time && currentEditRow.value.end_time
            ? [currentEditRow.value.start_time, currentEditRow.value.end_time]
            : undefined,
        // 样本状态处理
        sample_status: {
          status: sampleStatus,
          randomPercent: currentEditRow.value.status_random_percent || 0,
          okPercent: currentEditRow.value.status_ok_percent || 0,
          forbidPercent: currentEditRow.value.status_forbid_percent || 0,
        },
        // 违规类型处理
        forbid_type: forbidTypeValue,
        // 操作角色处理 - 转换为Cascader组件期望的格式
        user: userValue,
        // 其他百分比
        sample_random_percent: currentEditRow.value.status_random_percent || 0,
        sample_ok_percent: currentEditRow.value.status_ok_percent || 0,
        sample_forbid_percent: currentEditRow.value.status_forbid_percent || 0,
      };
      console.log('原始编辑数据:', currentEditRow.value);
      console.log('转换后的user值:', userValue);
      console.log('完整回填数据:', formattedData);
      formRef.value.setFieldsValue(formattedData);

      // 验证数据是否正确设置
      setTimeout(() => {
        console.log('设置后的表单值:', formRef.value.getFieldsValue());
      }, 100);
    }
  }, 500);
};

// 处理删除
const handleDelete = async (id: string) => {
  await handleApiCall(
    delQuality,
    { config_id: id },
    {
      onSuccess: () => {
        tableTemplateRef.value?.refreshTable();
      },
    },
  );
};

onBeforeMount(async () => {
  const resp = await supplierRegisterService();
  resp.data.register_list.forEach((item: any) => {
    teamMap.value[item.supplier] = item.mark;
  });
  teamMap.value.custom = '自定义团队';
  console.log('resp', resp);
});
// 模态框保存成功后刷新表格数据
const handleSaveSuccess = () => {
  isEditModalVisible.value = false;
  tableTemplateRef.value?.refreshTable();
};

/**
 * 处理取消按钮，关闭模态框
 * 关闭弹窗时不重置表单数据，保持用户已输入的内容
 */
const handleCancel = () => {
  isEditModalVisible.value = false;
};

/**
 * 处理确定按钮点击
 * 验证表单并提交数据
 */
const handleConfirm = async () => {
  try {
    console.log('开始处理确定按钮点击');

    // 先验证表单
    if (!formRef.value) {
      console.error('表单引用未找到');
      return;
    }

    // 验证表单
    const values = await formRef.value.validate();
    console.log('表单验证成功，获取到的表单值:', values);

    // 检查是否获取到有效数据
    if (!values) {
      console.error('无法获取表单数据');
      return;
    }

    // 调用提交函数
    await handleFormSubmit(values);
  } catch (error) {
    console.error('表单验证失败或提交失败:', error);

    // 如果是验证错误，给用户友好的提示
    if (error && typeof error === 'object' && 'errorFields' in error) {
      console.log('验证失败的字段:', error.errorFields);
      // Ant Design Vue的验证错误会自动显示在对应字段下方
      // 这里可以添加额外的提示逻辑
    }
  }
};

/**
 * 构建API参数
 * 将表单数据转换为API期望的格式
 * @param values 处理后的表单数据
 * @returns API参数对象
 */
const buildApiParams = (values: any) => {
  const baseParams: any = {
    biz: values.biz,
    biz_name: values.biz_name || '', // 如果没有biz_name，设置为空字符串
    module_type: values.module_type,
    supplier: values.supplier,
    origin_entries: values.origin_entries || [],
    reviewers: values.reviewers || [],
    start_time: (() => {
      const startTime = values.time?.[0];
      if (startTime === null || startTime === undefined || startTime === '') {
        return 0;
      }
      const converted = Number(startTime);
      return isNaN(converted) ? 0 : converted;
    })(),
    end_time: (() => {
      const endTime = values.time?.[1];
      if (endTime === null || endTime === undefined || endTime === '') {
        return 0;
      }
      const converted = Number(endTime);
      return isNaN(converted) ? 0 : converted;
    })(),
    man_access: values.man_access || '', // 进审类型，现在是必填字段
  };

  console.log('构建API参数 - 原始values:', values);
  console.log('构建API参数 - 时间字段处理:', {
    'values.time': values.time,
    'start_time处理结果': baseParams.start_time,
    'end_time处理结果': baseParams.end_time,
    '时间数组长度': values.time?.length,
    '开始时间原始值': values.time?.[0],
    '结束时间原始值': values.time?.[1],
  });
  console.log('构建API参数 - 基础参数:', baseParams);

  // 处理样本状态相关参数
  if (values.sample_status) {
    // 根据样本状态的选择模式，设置对应的参数
    if (values.sample_status.status === 1) {
      // 随机抽取模式：使用随机比例，按比例抽取的百分比设为0
      baseParams.status_random_percent = Number(
        values.sample_status.randomPercent || 0,
      );
      baseParams.status_ok_percent = 0;
      baseParams.status_forbid_percent = 0;
      console.log('样本状态 - 随机抽取模式:', {
        status_random_percent: baseParams.status_random_percent,
        status_ok_percent: baseParams.status_ok_percent,
        status_forbid_percent: baseParams.status_forbid_percent,
      });
    } else if (values.sample_status.status === 2) {
      // 按比例抽取模式：使用按比例百分比，随机比例设为0
      baseParams.status_random_percent = 0;
      baseParams.status_ok_percent = Number(values.sample_status.okPercent || 0);
      baseParams.status_forbid_percent = Number(
        values.sample_status.forbidPercent || 0,
      );
      console.log('样本状态 - 按比例抽取模式:', {
        status_random_percent: baseParams.status_random_percent,
        status_ok_percent: baseParams.status_ok_percent,
        status_forbid_percent: baseParams.status_forbid_percent,
      });
    } else {
      // 默认情况：所有比例设为0
      baseParams.status_random_percent = 0;
      baseParams.status_ok_percent = 0;
      baseParams.status_forbid_percent = 0;
    }
  } else {
    baseParams.status_random_percent = 0;
    baseParams.status_ok_percent = 0;
    baseParams.status_forbid_percent = 0;
  }

  // 处理违规类型相关参数
  console.log('违规类型原始数据:', values.forbid_type);
  console.log('违规类型数据类型:', typeof values.forbid_type);
  console.log('是否为对象:', typeof values.forbid_type === 'object');
  console.log('是否为null:', values.forbid_type === null);

  if (values.forbid_type) {
    let forbidType: any;

    // 处理不同类型的forbid_type数据
    if (
      typeof values.forbid_type === 'string' ||
      typeof values.forbid_type === 'number'
    ) {
      console.log('违规类型数据是字符串或数字，转换为对象格式');
      // 如果是字符串或数字，说明可能是组件初始化问题，使用默认值
      forbidType = {
        type: 1,
        randomPercent: 0,
        percentages: FORBID_FIELD_CONFIGS.reduce(
          (acc, { field }) => {
            acc[field] = 0;
            return acc;
          },
          {} as Record<string, number>,
        ),
      };
    } else if (
      typeof values.forbid_type === 'object' &&
      values.forbid_type !== null
    ) {
      forbidType = values.forbid_type;
    } else {
      console.log('违规类型数据格式异常，使用默认值');
      forbidType = {
        type: 1,
        randomPercent: 0,
        percentages: {},
      };
    }

    console.log('处理后的违规类型配置:', forbidType);

    if (forbidType.type === 1) {
      // 随机抽取模式
      baseParams.type_random_percent = Number(forbidType.randomPercent || 0);
      console.log('随机抽取模式，比例:', baseParams.type_random_percent);

      // 随机模式下，所有具体类型百分比设为0
      FORBID_FIELD_CONFIGS.forEach(({ field }) => {
        baseParams[field] = 0;
      });
    } else if (forbidType.type === 2) {
      // 按比例抽取模式
      baseParams.type_random_percent = 0; // 随机比例设为0

      // 设置各种违规类型的百分比
      const percentages = forbidType.percentages || {};
      console.log('按比例抽取模式，百分比数据:', percentages);

      FORBID_FIELD_CONFIGS.forEach(({ field }) => {
        const value = percentages[field] || 0;
        baseParams[field] = Number(value);
        console.log(`设置 ${field} = ${baseParams[field]}`);
      });
    }
  } else {
    console.log('违规类型数据为空，设置默认值');
    baseParams.type_random_percent = 0;
    // 设置所有违规类型百分比为0
    FORBID_FIELD_CONFIGS.forEach(({ field }) => {
      baseParams[field] = 0;
    });
  }

  // 如果是编辑模式，添加id和其他只读字段
  if (isEdit.value && currentEditRow.value) {
    baseParams.id = currentEditRow.value.id;
    // 保留一些系统字段
    if (currentEditRow.value.create_time) {
      baseParams.create_time = currentEditRow.value.create_time;
    }
    if (currentEditRow.value.modify_time) {
      baseParams.modify_time = currentEditRow.value.modify_time;
    }
    if (currentEditRow.value.sampling_task_key) {
      baseParams.sampling_task_key = currentEditRow.value.sampling_task_key;
    }
    if (currentEditRow.value.sampling_task_status) {
      baseParams.sampling_task_status =
        currentEditRow.value.sampling_task_status;
    }
  }

  return baseParams;
};

/**
 * 处理表单提交
 * 将Cascader格式的user数据转换回后端期望的supplier + reviewers格式
 * @param values 表单数据
 */
const handleFormSubmit = async (values: any) => {
  // 确保values不为空
  if (!values) {
    console.error('无法获取表单数据');
    return;
  }

  console.log('原始表单提交数据:', values);

  // 处理user字段的格式转换
  const processedValues = { ...values };

  if (values.user && Array.isArray(values.user)) {
    console.log('原始user数据:', values.user);
    console.log('user数据类型检查:', {
      isArray: Array.isArray(values.user),
      length: values.user.length,
      firstElement: values.user[0],
      firstElementType: typeof values.user[0],
      isFirstElementArray: Array.isArray(values.user[0]),
    });

    let reviewers: string[] = [];

    // 检查是否为嵌套数组（Cascader路径格式）
    if (values.user.length > 0 && Array.isArray(values.user[0])) {
      // Cascader多选格式：[["team", "user1"], ["team", "user2"]]
      reviewers = values.user.map((path: string[]) => {
        console.log('处理路径:', path, '提取用户名:', path[1]);
        return path[1]; // 只提取用户名部分
      });
    } else if (values.user.length === 2 && typeof values.user[0] === 'string') {
      // Cascader单选格式：["team", "user"]
      reviewers = [values.user[1]];
      console.log('单选格式，提取用户名:', values.user[1]);
    } else {
      // 可能是直接的用户名数组或ID数组
      reviewers = values.user;
      console.log('直接使用user数组:', reviewers);
    }

    // 更新处理后的数据 - 只设置reviewers，不修改supplier字段
    processedValues.reviewers = reviewers;
    // 移除user字段，因为后端不需要
    delete processedValues.user;

    console.log('最终转换后的reviewers:', reviewers);
  }

  // 构建API参数
  const apiParams = buildApiParams(processedValues);
  console.log('最终API参数:', apiParams);

  // 调用保存API
  await handleApiCall(updateQuality, apiParams, {
    loadingKey: isEdit.value ? 'update' : 'create',
    successMsg: isEdit.value ? '更新成功' : '新增成功',
    onSuccess: () => {
      handleSaveSuccess();
    },
  });
};

/**
 * 检查生成样本按钮是否处于加载状态
 * @param rowId 行ID
 * @returns 是否正在加载
 */
const isGeneratingSample = (rowId: string) => {
  return isRowLoading('generateSample', rowId);
};

/**
 * 检查重置队列按钮是否处于加载状态
 * @param rowId 行ID
 * @returns 是否正在加载
 */
const isResettingQueue = (rowId: string) => {
  return isRowLoading('resetQueue', rowId);
};

/**
 * 生成样本处理函数
 * @param row 表格行数据
 */
const handleGenerateSample = async (row: any) => {
  await handleApiCall(
    qualitySample,
    { config_id: row.id },
    {
      loadingKey: 'generateSample',
      rowId: row.id,
      tableRef: tableTemplateRef.value,
      successMsg: '生成样本成功',
      errorMsg: '生成样本失败，请稍后再试',
      showMessage: true,
      autoRefresh: true,
    },
  );
};

/**
 * 重置队列处理函数
 * @param row 表格行数据
 */
const handleResetQueue = async (row: any) => {
  await handleApiCall(
    resetQuality,
    { config_id: row.id },
    {
      loadingKey: 'resetQueue',
      rowId: row.id,
      tableRef: tableTemplateRef.value,
      successMsg: '重置队列成功',
      errorMsg: '重置队列失败，请稍后再试',
      showMessage: true,
      autoRefresh: true,
    },
  );
};
</script>

<template>
  <div>
    <table-template
      ref="tableTemplateRef"
      :columns="columns"
      :pagination-options="paginationOptions"
      :query-method="getKeywordOpLogs"
    >
      <template #action="{ row }">
        <a-button type="link" @click="handleEdit(row)">编辑</a-button>
        <a-button
          v-if="
            row.module_type !== 'customize-live' &&
            row.module_type !== 'quality_custom_live'
          "
          :disabled="isGeneratingSample(row.id)"
          :loading="isGeneratingSample(row.id)"
          type="link"
          @click="handleGenerateSample(row)"
        >
          生成样本
        </a-button>
        <a-button
          :disabled="isResettingQueue(row.id)"
          :loading="isResettingQueue(row.id)"
          danger
          type="link"
          @click="handleResetQueue(row)"
        >
          重置队列
        </a-button>
        <a-popconfirm
          cancel-text="取消"
          ok-text="确定"
          title="确定删除？"
          @confirm="handleDelete(row.id)"
        >
          <a-button danger type="link">删除</a-button>
        </a-popconfirm>
      </template>
      <template #toolbar-left>
        <a-button type="primary" @click="handleAdd">添加</a-button>
      </template>
    </table-template>

    <a-modal
      v-model:open="isEditModalVisible"
      :width="700"
      title="质检样本配置"
      @cancel="handleCancel"
    >
      <template #footer>
        <div class="flex justify-end gap-2">
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleConfirm">确定</a-button>
        </div>
      </template>

      <enhanced-form
        ref="formRef"
        :common-config="commonConfig"
        :label-align="true"
        :schema="schema"
        :show-default-actions="false"
        label-width="auto"
        layout="horizontal"
      />
    </a-modal>
  </div>
</template>
