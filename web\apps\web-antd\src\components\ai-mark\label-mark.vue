<script setup lang="ts">
import { ref, watch } from 'vue';

import { usePreferences } from '@vben/preferences';

import { useDebounceFn, useMagicKeys } from '@vueuse/core';

import { useLabels } from '#/composables/ai-mark/use-labels';

interface Label {
  id: number | string;
  label: string;
  label_color: string;
  short_key: string;
  type: string;
  title: string;
  sentiment?: number;
  children?: Label[];
}

const props = defineProps<{
  disabled?: boolean;
  entry: string;
  lazyEntry?: boolean;
  showLevel?: boolean;
  type?: 'button' | 'tag'; // 按钮类型可以点击触发，tag类型不可以点击触发，必须通过键盘触发
}>();

const emit = defineEmits<{
  (e: 'keyRelease'): void;
  (e: 'mark', label: Label): void;
}>();

const entryRef = ref(props.entry);

const { labelList, loadLabels, getLabelByShortKey } = useLabels(
  entryRef,
  props.lazyEntry,
  props.showLevel,
);

const { isDark } = usePreferences();
const className = ref(isDark.value ? 'bg-[#1C1E23]' : 'bg-white');

const selectedLabel = ref<Label | null>(null);

watch(
  () => props.entry,
  (v) => {
    entryRef.value = v;
    loadLabels();
  },
);

watch(
  () => isDark.value,
  (v) => {
    className.value = v ? 'bg-[#1C1E23]' : 'bg-white';
  },
);

const handleClick = (label: Label) => {
  if (label.children && label.children.length > 0) {
    selectedLabel.value = label;
  } else {
    selectedLabel.value = null;
    emit('mark', label);
  }
};

const handleChildClick = (childLabel: Label) => {
  if (!props.disabled) {
    emit('mark', childLabel);
    selectedLabel.value = null;
  }
};

const { current } = useMagicKeys();

const debouncedHandleClick = useDebounceFn((label: Label) => {
  if (!props.disabled) {
    handleClick(label);
  }
}, 300);

watch(current, (v) => {
  if (props.disabled) return;
  const keys = [...v]
    .map((key) =>
      key === 'control' ? 'Ctrl' : key.charAt(0).toUpperCase() + key.slice(1),
    )
    .join('+');
  const matchedLabel = getLabelByShortKey(keys);

  if (
    selectedLabel.value &&
    selectedLabel.value.children &&
    selectedLabel.value.children.length > 0
  ) {
    const matchedChildLabel = selectedLabel.value.children.find(
      (child) => child.short_key === keys,
    );
    if (matchedChildLabel && !props.disabled) {
      emit('mark', matchedChildLabel);
      selectedLabel.value = null;
      return;
    }
  }

  if (matchedLabel) {
    if (!props.disabled) {
      if (matchedLabel.children && matchedLabel.children.length > 0) {
        selectedLabel.value = matchedLabel;
      } else {
        selectedLabel.value = null;
        emit('mark', matchedLabel);
      }
    }
  } else {
    emit('keyRelease');
  }
});

const getLabelList = () => {
  return labelList.value;
};

// 计算子标签按钮样式
const getChildLabelStyle = (childLabel: Label, disabled: boolean) => {
  return {
    backgroundColor: disabled
      ? '#f5f5f5'
      : childLabel.sentiment === 1
        ? '#d9f7be' // 浅绿色
        : childLabel.sentiment === 0
          ? '#ffccc7' // 浅红色
          : childLabel.label_color
            ? `${childLabel.label_color}40`
            : `${selectedLabel.value?.label_color || ''}40`, // 添加透明度使颜色变浅
    color: disabled ? 'rgba(0, 0, 0, 0.25)' : '#333',
    borderColor:
      childLabel.sentiment === 1
        ? '#b7eb8f' // 绿色边框
        : childLabel.sentiment === 0
          ? '#ffa39e' // 红色边框
          : childLabel.label_color || selectedLabel.value?.label_color || '',
    borderWidth: '1px',
    borderStyle: 'solid',
    '--hover-bg-color':
      childLabel.sentiment === 1
        ? '#52c41a' // 深绿色
        : childLabel.sentiment === 0
          ? '#ff4d4f' // 深红色
          : childLabel.label_color || selectedLabel.value?.label_color || '',
  };
};

// 处理子标签按钮悬停
const handleChildLabelMouseEnter = (e: MouseEvent) => {
  const target = e.target as HTMLElement;
  target.style.backgroundColor =
    target.style.getPropertyValue('--hover-bg-color');
  target.style.borderColor = 'transparent';
  target.style.color = '#fff';
};

// 处理子标签按钮离开
const handleChildLabelMouseLeave = (e: MouseEvent, childLabel: Label) => {
  const target = e.target as HTMLElement;
  target.style.backgroundColor = props.disabled
    ? '#f5f5f5'
    : childLabel.sentiment === 1
      ? '#d9f7be'
      : childLabel.sentiment === 0
        ? '#ffccc7'
        : childLabel.label_color
          ? `${childLabel.label_color}40`
          : `${selectedLabel.value?.label_color || ''}40`;
  target.style.borderColor = props.disabled
    ? 'transparent'
    : childLabel.sentiment === 1
      ? '#b7eb8f'
      : childLabel.sentiment === 0
        ? '#ffa39e'
        : childLabel.label_color || selectedLabel.value?.label_color || '';
  target.style.color = '#333';
};

// 计算父级按钮样式
const getParentButtonStyle = (label: Label, disabled: boolean) => {
  return {
    backgroundColor: disabled
      ? '#f5f5f5'
      : label.sentiment === 1
        ? '#52c41a'
        : label.sentiment === 0
          ? '#ff4d4f'
          : label.label_color,
    color: disabled ? 'rgba(0, 0, 0, 0.25)' : '#fff',
  };
};

defineExpose({
  getLabelList,
});
</script>

<template>
  <div class="flex flex-wrap items-center gap-2">
    <template v-for="label in labelList" :key="label.id">
      <a-button
        v-if="type === 'button'"
        :danger="label.sentiment === 0"
        :disabled="disabled"
        :style="getParentButtonStyle(label, !!disabled)"
        :type="label.sentiment === 1 ? 'primary' : 'primary'"
        class="cursor-pointer border-none px-3 py-1 transition-all duration-300 ease-in-out hover:-translate-y-0.5 hover:shadow-md"
        @click="debouncedHandleClick(label)"
      >
        {{ label.title }} ({{ label.short_key }})
      </a-button>
      <span v-else>
        <a-tag :color="label.label_color"> {{ label.short_key }} </a-tag>-{{
          label.title
        }}
      </span>
    </template>
  </div>

  <div
    v-if="
      selectedLabel &&
      selectedLabel.children &&
      selectedLabel.children.length > 0
    "
    class="relative mt-2"
  >
    <div
      :class="className"
      class="absolute -top-2 left-2 z-10 px-1 text-xs text-red-500"
    >
      *点击即确认，请谨慎操作！
    </div>
    <div class="max-h-[400px] overflow-y-auto border px-1 pb-1 pt-2">
      <div
        v-for="childLabel in selectedLabel.children"
        :key="childLabel.id"
        :class="[disabled ? 'opacity-50' : '']"
        :style="disabled ? 'cursor: not-allowed;' : 'cursor: pointer;'"
        class="mt-1 inline-block"
      >
        <a-button
          :danger="childLabel.sentiment === 0"
          :style="getChildLabelStyle(childLabel, !!disabled)"
          :type="childLabel.sentiment === 1 ? 'primary' : 'primary'"
          class="child-btn mb-1 mr-3 p-2 transition-all duration-300 ease-in-out hover:shadow-sm"
          size="small"
          @click="!disabled && handleChildClick(childLabel)"
          @mouseenter="handleChildLabelMouseEnter"
          @mouseleave="
            (e: MouseEvent) => handleChildLabelMouseLeave(e, childLabel)
          "
        >
          {{ childLabel.title }} ({{ childLabel.short_key }})
        </a-button>
      </div>
    </div>
  </div>
</template>
