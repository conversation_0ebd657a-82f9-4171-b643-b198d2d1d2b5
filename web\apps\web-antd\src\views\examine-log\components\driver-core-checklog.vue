<script lang="ts" setup>
import { computed, ref, watch, onMounted, shallowRef, h } from 'vue';
import TableTemplate from '#/components/table/index.vue';
import SceneSelect from '#/components/select/scene.vue';
import ForbidSelect from '#/components/select/ForbidType.vue';
import StatusTag from '#/components/tag/index.vue';
import { getItem } from '#/utils/save';
import { useRoute } from 'vue-router';
import { useRouter } from 'vue-router';
import { storeToRefs } from 'pinia';
import { message } from 'ant-design-vue';
import FilePreview from '#/components/file/index.vue';
import { validateParams, mapToOptions } from '#/utils/transform';
import { useDateRangeLimit } from '#/composables/common/use-daterange-validator';
import { findLabelByValue } from '#/utils/data';
import { formatTimestampToDate, getLastNDaysRange } from '#/utils/time';
import ToolWrapper from './tool-wrapper.vue';
import { updateComment } from '#/api/task';
import {
  commitCommentStatusV2Single,
  commitQualityUpdateTextStatus,
} from '#/api/comment';
import {
  EXAMINE_STATUS_MAP,
  EXAMINE_ROLE_MAP,
  RECORD_FLAG_MAP,
  EXAMINE_TYPE_MAP,
  EXAMINE_TAG_MAP,
  EXAMINE_COLOR_MAP,
  OFFLINE_STATUS_MAP,
} from '#/constants/maps/examine-log';
import { getRecordsDetail, getExaminListForContent } from '#/api/examine-log';
import UpdateForbidCommentBtn from '#/components/button/UpdateForbidComment.vue';
import { useBizInfo } from '#/composables/common/use-biz';
import { useSupplier } from '#/composables/common/use-supplier';
import useForbidStore from '#/store/forbid';
import Selectinput from '#/components/select/selectinput.vue';
import Titleinput from '#/components/select/examine-log.vue';
import {
  processQueryParams,
  processResponseData,
  getStatusVariant,
  commentTypeFilter,
  forbidTypeSentiTextFilter,
} from '../utils/data-process';
import { generateColumns, tabOptions } from '../utils/generate-colums';
import { handleTypeChange, handleOperation } from '../utils/bussiness-logic';
import { searchTaskResult, submitSearchTask } from '#/api/offline-search';

const { supplierData, fetchData } = useSupplier();

const { disabledDate, onCalendarChange, onOpenChange } = useDateRangeLimit();
const router = useRouter();
const forbidStore = useForbidStore();
const { allForbidTags } = storeToRefs(forbidStore);
// 定义 props
const props = defineProps<{
  bizConfig: { biz: string; scene?: string };
  isLoading?: boolean;
  fromOfflineSearch?: boolean;
}>();
// 定义 emits
const emit = defineEmits<{
  (e: 'update:biz', biz: any): void;
  (e: 'loading-change', loading: boolean): void;
  (e: 'update-status', row: any, status: string, types?: string[]): void;
}>();

const route = useRoute();
const queryParams = ref({
  platform: '',
  type: '',
  id: '',
  page: '',
  word: '',
  start_time: '',
  end_time: '',
  task_type: '',
  keyword: '',
});
const isFormChanged = ref(false);
const username = getItem('user_name');
const userid = getItem('user_id');
const imageView = ref<boolean>(false);
const scene = ref();
const tableRef = ref();
const url_mode = ref(' ');
const curCheckLogConfig = ref(props.bizConfig);
const schemall = ref<any[]>([]);
const taskConfig = ref<any>({});
const bizTypes = computed(() => {
  const biz = props.bizConfig.biz;
  const { bizType } = useBizInfo(biz);
  return bizType;
});
const sceneOptions = ref<{ label: string; value: string }[]>([]);
// 获取 SceneSelect 选项的处理函数
const handleGetSceneOptions = (
  getOptions: () => { label: string; value: string }[],
) => {
  sceneOptions.value = getOptions();
};
const forbidList = ref<any[]>([]);
const forbidListorgin = ref<any[]>([]);
const visible = ref(false);
// 分页状态
const offlinePage = ref({
  page: Number(queryParams.value.page) || 1,
  pageCount: 1,
  limit: 100,
  sort: [],
  hasMore: false,
});
onMounted(() => {
  forbidStore.fetchForbidList();
  if (curCheckLogConfig.value.biz.includes('drive_core')) {
    fetchData();
  }
  queryParams.value = {
    platform: (route.query.platform as string) || '',
    type: (route.query.type as string) || '',
    id: (route.query.id as string) || '',
    page: (route.query.page as string) || '1',
    word: (route.query.word as string) || '',
    start_time: (route.query.start_time as string) || '',
    end_time: (route.query.end_time as string) || '',
    task_type: (route.query.task_type as string) || '',
    keyword: route.query.keyword
      ? decodeURIComponent(route.query.keyword as string)
      : '',
  };
  offlinePage.value.page = Number(queryParams.value.page) || 1;
});
// 监听 Store 的 forbidTagsFilterMiddleTags 数据
watch(
  () => forbidStore.forbidTagsFilterMiddleTags,
  (newTags) => {
    forbidList.value = newTags;
    forbidListorgin.value = forbidStore.enabledForbidTags;
  },
  { immediate: true },
);
// 获取场景标签
const getSceneLabel = (value: string): string => {
  const option = sceneOptions.value.find((item) => item.value === value);

  return option ? option.label : value;
};

// 跳转到禁止信息页面
const linkToForbidInfo = (row: any) => {
  if (!currentBizType.value.isDrive) return;
  const query: Record<string, string> = {
    platform: curCheckLogConfig.value.biz,
  };
  const fields = ['fileid', 'fileinx', 'scene', 'extra_id'];
  fields.forEach((field) => {
    if (row[field]) query[field] = row[field];
  });
  router.push({
    path: '/forbidInfo',
    query,
  });
};
const currentBizType = computed(() => {
  const bt = bizTypes.value.value;
  return {
    isWpsAccount: bt.isWpsAccount,
    isComment: bt.isComment,
    isHomeComment: bt.isHomeComment,
    isDocerPlat: bt.isDocer,
    isDrive: bt.isDrive,
    isWatermark: bt.isWatermark,
    isKdocsComment: bt.isKdocsComment,
    isKsformAudit: bt.isKsformAudit,
    isHomeTopic: bt.isHomeTopic,
    isAppPlatform: bt.isAppPlatform,
    isSentiment: bt.isSentiment,
    isHome: bt.isHome,
    isViewChange: bt.isViewChange,
  };
});
const isCommentText = computed(() => {
  return currentBizType.value.isComment && !imageView.value;
});

// 计算属性：是否为评论图片模式
const isCommentImage = computed(() => {
  return currentBizType.value.isComment && imageView.value;
});
// 根据业务类型显示不同的标签页
const visibleTabs = computed(() => {
  const { isWpsAccount, isComment, isHomeComment } = currentBizType.value;
  if (isWpsAccount || (isComment && !isHomeComment)) {
    return tabOptions.list.filter((tab) => tab.key === '1');
  }
  return tabOptions.list;
});
console.log(props.bizConfig.biz);
const dynamicTabOptions = computed(() => ({
  ...tabOptions,
  list: visibleTabs.value,
}));

interface QueryParams {
  page: number;
  limit: number;
  from: string;
  activeTab?: string;
  start_time?: number;
  end_time?: number;
  [key: string]: any;
}

// “新建”按钮点击处理
const submitTask = () => {
  visible.value = true; // 显示弹窗
};

const handleOk = async () => {
  const targetPage = offlinePage.value.page + 1;
  try {
    const wordParams = queryParams.value.keyword.split(';');
    const result = await submitSearchTask({
      from: queryParams.value.platform,
      word: wordParams,
      user_id: userid,
      start_time: queryParams.value.start_time,
      end_time: queryParams.value.end_time,
      page: targetPage,
      task_type: queryParams.value.task_type,
      search_after: JSON.stringify(offlinePage.value.sort),
    });
    if (result && result.result) {
      message.success('任务创建成功');
      router.push({ path: '/offlineSearch' });
    }
  } catch (error) {
    message.error('任务创建失败');
    console.error(error);
  } finally {
    visible.value = false; // 关闭弹窗
  }
};

const handleCancel = () => {
  visible.value = false; // 关闭弹窗
};

// 动态表单配置生成
const generateFormSchema = (tabKey: string) => {
  const bt = bizTypes.value.value;
  const common = bt.isDrive || bt.isDocer || bt.isAppPlatform;

  if (props.fromOfflineSearch) {
    const schema = [
      {
        component: 'Select',
        fieldName: 'status',
        label: '状态',

        componentProps: {
          placeholder: '请选择状态',
          options: mapToOptions(OFFLINE_STATUS_MAP),
        },
      },
      {
        fieldName: 'type',
        component: shallowRef(ForbidSelect),
        componentProps: {
          isGLobalList: true,
          isCascader: true,
          filterType: [3, 5],
        },
        label: '操作标签',
      },
    ];
    return schema;
  }

  if (tabKey === '1') {
    let schema = [];
    schema.push({
      component: shallowRef(Titleinput),
      fieldName: 'fname',
      componentProps: {
        placeholder: isCommentText.value
          ? bt.isWatermark
            ? '水印内容'
            : '文本内容'
          : '名称',
        modeValue: isCommentText.value
          ? bt.isWatermark
            ? '水印搜索'
            : '文本搜索'
          : '标题搜索',
      },
    });

    schema.push({
      component: 'Input',
      fieldName: 'userid',
      label: 'UserID',
      componentProps: {
        placeholder: '请输入用户ID',
      },
    });
    if (!bt.isDrive && !bt.isComment && !bt.isDocer && !bt.isWpsAccount) {
      schema.push({
        component: 'Input',
        fieldName: 'fileid',
        label: 'fileID',
        componentProps: {
          placeholder: '请输入fileID',
        },
      });
    }
    if (bt.isDrive || bt.isComment || bt.isDocer) {
      schema.push({
        component: 'Input',
        fieldName: 'fileinx',
        label: bt.isWatermark
          ? '水印id'
          : isCommentText.value
            ? '文本id'
            : 'fileID',
        componentProps: {
          placeholder: '请输入',
        },
      });
    }
    if (isCommentText.value && !bt.isHomeComment) {
      schema.push({
        component: 'Input',
        fieldName: 'f_fileid',
        label: '文档id',
        componentProps: {
          placeholder: '请输入文档id',
        },
      });
    }
    if (
      (bt.isDrive ||
        bt.isComment ||
        bt.isDocer ||
        bt.isHomeTopic ||
        bt.isWpsAccount) &&
      !bt.isWatermark
    ) {
      schema.push({
        component: shallowRef(SceneSelect),
        fieldName: 'scene',
        label: '场景',
        dependencies: {
          componentProps() {
            return {
              biz: curCheckLogConfig.value.biz || '',
              ...(!bt.isDrive && { immediate: true }),
            };
          },
          triggerFields: ['platform'],
        },
        componentProps: {
          placeholder: '请选择场景',
          hasEmpty: true,
          onChange: (value: string) => {
            scene.value = value;
            isFormChanged.value = true;
            if (tableRef.value && bt.isWpsAccount) {
              tableRef.value?.refreshTable();
            }
          },
          onGetOptions: handleGetSceneOptions,
        },
      });
    }
    if (
      bt.isDrive ||
      bt.isHomeComment ||
      bt.isDocer ||
      bt.isHomeTopic ||
      isCommentImage.value
    ) {
      schema.push({
        component: 'Input',
        fieldName: 'extra_id',
        label: '附件id',
        componentProps: {
          placeholder: '请输入附件id',
        },
      });
    }
    if (bt.isDrive) {
      schema.push({
        component: 'Input',
        fieldName: 'company',
        label: '企业id',
        componentProps: {
          placeholder: '请输入企业id',
        },
        colProps: { span: 6 },
      });
    }
    schema.push({
      component: 'Select',
      fieldName: 'status',
      label: '状态',
      defaultValue: ' ',
      componentProps: {
        placeholder: '请选择状态',
        options: mapToOptions(EXAMINE_STATUS_MAP),
      },
      colProps: { span: 6 },
    });
    if ((bt.isComment && !bt.isHomeComment) || bt.isWpsAccount) {
      schema.push({
        component: 'Select',
        fieldName: 'type',
        label: '类型',
        componentProps: {
          options: mapToOptions(EXAMINE_TYPE_MAP),
          placeholder: '请选择类型',
        },
        colProps: { span: 6 },
      });
    } else {
      schema.push({
        component: shallowRef(ForbidSelect),
        fieldName: 'type',
        label: '类型',
        componentProps: {
          placeholder: '请选择类型',
          isGLobalList: true,
          isCascader: true,
        },
        colProps: { span: 6 },
      });
    }
    schema.push({
      component: 'Select',
      fieldName: 'role',
      label: '审批角色',
      defaultValue: 'manual',
      componentProps: {
        placeholder: '请选择审批角色',
        options: mapToOptions(EXAMINE_ROLE_MAP),
      },
      colProps: { span: 6 },
    });
    schema.push({
      component: 'Input',
      fieldName: 'reviewer',
      label: '操作人',
      componentProps: {
        placeholder: '请输入操作人',
      },
      colProps: { span: 6 },
    });
    schema.push({
      component: 'RangePicker',
      fieldName: 'filterTime',
      label: '时间',
      defaultValue: getLastNDaysRange(7, 1, 'X', true),
      componentProps: {
        placeholder: ['开始时间', '结束时间'],
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'X',
        disabledDate,
        onCalendarChange,
        onOpenChange,
      },
      colProps: { span: 12 },
    });
    if (!bt.isComment && !bt.isWpsAccount) {
      schema.push({
        component: 'Select',
        fieldName: 'record_flag',
        defaultValue: 0,
        label: '文档类型',
        componentProps: {
          placeholder: '请选择文档类型',
          options: mapToOptions(RECORD_FLAG_MAP),
        },
      });
    }
    schemall.value = schema;

    return schema;
  } else if (tabKey === '2') {
    if (bt.isWpsAccount || (bt.isComment && !bt.isHomeComment)) {
      return [];
    }
    const schema = [];

    schema.push({
      component: shallowRef(Selectinput),
      fieldName: 'third_preview_url',
      label: '预览链接',
      componentProps: {
        placeholder: '请输入预览链接',
        modeValue: ' ',
        tabPlatform: props.bizConfig.biz,
        'onUpdate:protocolValue': (value: any) => {
          url_mode.value = value;
          console.log('protocolValue 更新为:', value); // 调试输出
        },
      },
    });

    const shouldAddFileinx =
      bt.isHomeComment ||
      common ||
      bt.isKsformAudit ||
      bt.isHomeTopic ||
      bt.isSentiment;
    if (shouldAddFileinx) {
      schema.push({
        component: 'Input',
        fieldName: 'fileinx',
        label: bt.isHomeComment
          ? '文本id'
          : bt.isWatermark
            ? '水印id'
            : 'fileID',
        componentProps: {
          placeholder: '请输入',
        },
      });
    }
    if (shouldAddFileinx) {
      schema.push({
        component: shallowRef(SceneSelect),
        fieldName: 'scene',
        label: '场景',
        dependencies: {
          componentProps() {
            return {
              biz: curCheckLogConfig.value.biz || '',
              ...(!bt.isDrive && { immediate: true }),
            };
          },
          triggerFields: ['platform'],
        },
        componentProps: {
          placeholder: '请选择场景',
          onGetOptions: handleGetSceneOptions,
          hasEmpty: true,
        },
      });
    }
    if (common || bt.isKsformAudit || bt.isHomeTopic || bt.isSentiment) {
      schema.push({
        component: 'Input',
        fieldName: 'extra_id',
        label: '附件id',
        componentProps: {
          placeholder: '请输入附件id',
        },
      });
    }
    if (bt.isDocer) {
      schema.push({
        component: 'Input',
        fieldName: 'resource_id',
        label: '资源id',
        componentProps: {
          placeholder: '请输入资源id',
        },
      });
    }
    return schema;
  } else if (tabKey === '3') {
    if (bt.isWpsAccount || (bt.isComment && !bt.isHomeComment)) {
      return [];
    }
    return [
      {
        component: 'Input',
        fieldName: 'word',
        label: '全文搜索',
        componentProps: {
          placeholder: '请输入搜索内容',
          maxlength: 20,
        },
      },
      {
        component: 'RangePicker',
        fieldName: 'filterTime',
        label: '时间',
        defaultValue: getLastNDaysRange(7, 1, 'X', true),
        componentProps: {
          placeholder: ['开始时间', '结束时间'],
          showTime: true,
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'X',
          disabledDate,
          onCalendarChange,
          onOpenChange,
        },
        colProps: { span: 12 },
      },
    ];
  }
  return [];
};

const searchOptions = computed(() => {
  const schemas = {
    '1': generateFormSchema('1'),
    '2': generateFormSchema('2'),
    '3': generateFormSchema('3'),
  };

  return {
    collapsed: false,
    schemas,
    showCollapseButton: true,
    submitOnChange: false,
    wrapperClass: 'grid-cols-4',
  };
});

// 切换平台清空场景
watch(
  () => props.bizConfig,
  (newBiz) => {
    curCheckLogConfig.value = newBiz;
    scene.value = '';
    imageView.value = false;
  },
  { deep: true },
);

// 监听场景根据选项动态更新表单和列
watch(
  () => scene.value,
  (newScene, oldScene) => {
    if (currentBizType.value.isWpsAccount && tableRef.value) {
      tableRef.value.updateColumns(dynamicColumns.value);
      if (newScene === 'avatar' || newScene === 'avatar_async') {
        schemall.value[4] = {
          component: shallowRef(ForbidSelect),
          fieldName: 'type',
          label: '类型',
          componentProps: {
            placeholder: '请选择类型',

            isGLobalList: true,
            isCascader: true,
          },
          colProps: { span: 6 },
        };
        tableRef.value.updateSchema(schemall.value);
      } else {
        schemall.value[4] = {
          component: 'Select',
          fieldName: 'type',
          label: '类型',
          componentProps: {
            options: mapToOptions(EXAMINE_TYPE_MAP),
            placeholder: '请选择类型',
          },
          colProps: { span: 6 },
        };
        tableRef.value.updateSchema(schemall.value);
      }
    }
  },
  { immediate: true },
);

// 动态列配置
const dynamicColumns = generateColumns(
  bizTypes,
  scene,
  currentBizType,
  allForbidTags,
  supplierData,
  getItem,
  isCommentText,
  isCommentImage,
);

// 处理标签页变化
const handleTabChange = () => {
  if (tableRef.value) {
  }
};

const searchTitle = () => {
  if (tableRef.value) {
    tableRef.value.refreshTable();
  }
};

const handleAction = async (row: any, value: string[]) => {
  const command = value[value.length - 1];
  await handleOperation(
    row,
    command,
    [command],
    curCheckLogConfig,
    currentBizType,
    username,
    userid,
    tableRef,
  );
};

const handleConfirm = async (row: any) => {
  if (!row.paramsTypes || row.paramsTypes.length === 0) return;
  const types = row.paramsTypes.map((item: string[]) => item[item.length - 1]);
  const command = types[0];
  await handleOperation(
    row,
    command,
    types,
    curCheckLogConfig,
    currentBizType,
    username,
    userid,
    tableRef,
  );
};

// 查询数据方法
const queryData = async ({ page, pageSize, ...formValues }: QueryParams) => {
  if (
    isFormChanged.value &&
    currentBizType.value.isWpsAccount &&
    ['avatar', 'avatar_async'].includes(scene.value || '')
  ) {
    isFormChanged.value = false;
    return { items: [], total: 0 };
  }
  if (currentBizType.value.isWpsAccount && formValues.scene === undefined) {
    scene.value = '';
  }

  try {
    emit('loading-change', true);
    formValues.url_mode = url_mode.value;
    const currentTab = tableRef.value.getCurrentTab();
    const { baseParams } = processQueryParams(
      formValues,

      page,
      pageSize,
      curCheckLogConfig,
      tableRef,
    );
    if (isCommentText.value) {
      baseParams.text_view = true;
    }
    let queryString = validateParams(baseParams);

    let response;

    if (currentTab === '3') {
      response = await getExaminListForContent(queryString);
    } else {
      response = await getRecordsDetail(queryString);
    }

    return processResponseData(response, page, pageSize, taskConfig);
  } catch (error) {
    console.error('查询失败:', error);
    return { items: [], total: 0 };
  } finally {
    emit('loading-change', false);
  }
};
const handleNormalOperation = async (row: any, status: number) => {
  const params = {
    entry: `commentGlobal_${row.scene}`,
    from: 'kdocs_comment',
    fileid: row.fileid,
    scene: row.scene,
    extra_id: row.extra_id,
    content: row.content,
    f_biz: row.f_biz,
    f_fileid: row.f_fileid,
    f_scene: row.f_scene,
    f_extra_id: row.f_extra_id,
    uuid: row.uuid,
    type: status,
    status: status === 0 ? 'ok' : 'forbid',
    create_time: row.link_ctime,
    delay_time: row.delay_time,
  };

  try {
    const result = await updateComment(params);
    if (result.result) {
      return true; // 成功
    } else {
      throw new Error(result.message || '操作失败');
    }
  } catch (error) {
    throw error;
  }
};

// V2 操作的 callback 函数
const handleV2Operation = async (row: any, status: number) => {
  const params = {
    biz: 'wps_account',
    entry: 'taskGloble',
    fileid: row.fileid,
    extra_id: row.extra_id,
    scene: row.scene,
    status: status === 0 ? 'ok' : 'forbid',
    fname: row.fname,
    type: status,
    content: row.content,

    f_biz: row.f_biz,
    f_extra_id: row.f_extra_id,
    f_fileid: row.f_fileid,
    f_scene: row.f_scene,
    userid: row.userid,
    create_time: row.link_ctime,
    task_tag: row.task_tag,
  };

  try {
    const result = await commitCommentStatusV2Single(params);
    if (result.result) {
      return true; // 成功
    } else {
      throw new Error(result.message || '操作失败');
    }
  } catch (error) {
    throw error;
  }
};
const queryOfflineData = async ({
  page,
  pageSize,
  ...formValues
}: QueryParams) => {
  try {
    console.log(formValues);
    emit('loading-change', true);
    const params = {
      task_id: queryParams.value.id,
    };
    const response = await searchTaskResult(params);
    const { items, total } = processResponseData(
      response,
      page,
      pageSize,
      taskConfig,
    );

    const filteredItems = items.filter((item) => {
      const statusMatch =
        !formValues.status?.length || formValues.status.includes(item.status);

      const typeMatch =
        !formValues.type?.length || formValues.type.includes(item.type);
      return statusMatch && typeMatch;
    });

    offlinePage.value.page = page;
    offlinePage.value.sort = response.data.sort || [];
    offlinePage.value.hasMore = response.data.hasMore || false;
    offlinePage.value.pageCount = response.data.hasMore
      ? response.data.pageCount
      : 0;

    return { items: filteredItems, total };
  } catch (error) {
    console.error('查询失败:', error);
    return { items: [], total: 0 };
  }
};

watch(
  () => props.bizConfig,
  (newBiz) => {
    curCheckLogConfig.value = newBiz;
  },
  { deep: true },
);
const defaultActiveKey = '2';
const paginationOptions = computed(() => {
  if (props.fromOfflineSearch) {
    return {
      pageSize: 100,
      currentPage: offlinePage.value.page,
      enabled: true,
      layouts: ['NextPage'],
      showCurrentPage: true,
    };
  }
  return {
    pageSize: 20,
    currentPage: 1,
    enabled: true,
    layouts: ['PrevPage', 'NextPage', 'FullJump'],
  };
});
</script>

<template>
  <table-template
    :key="`${curCheckLogConfig.biz}-${imageView}`"
    :defaultActiveKey="defaultActiveKey"
    :paginationOptions="paginationOptions"
    ref="tableRef"
    :clearSpaces="true"
    :search-options="searchOptions"
    :columns="dynamicColumns"
    :tab-options="props.fromOfflineSearch ? undefined : dynamicTabOptions"
    :query-method="props.fromOfflineSearch ? queryOfflineData : queryData"
    :rowClassName="({ row }) => (row.is_grey ? 'text-gray-500 opacity-50' : '')"
    @tab-change="handleTabChange"
    v-if="!isLoading"
    :grid-options="{
      maxHeight: props.fromOfflineSearch ? 800 : 600,
      rowConfig: {
        isHover: true,
        keyField: 'fileid',
      },
      treeConfig: {
        children: 'related_review_records',
        hasChild: 'hasChildren',
      },
    }"
  >
    <template v-if="fromOfflineSearch" #toolbar-left>
      <a-button
        type="primary"
        class="mr-2"
        :disabled="!offlinePage.hasMore"
        @click="submitTask"
      >
        新建
      </a-button>
    </template>
    <template #toolbar-right>
      <div
        v-if="currentBizType.isComment"
        class="absolute right-[5px] top-[-240px] z-20 mb-4 flex items-center justify-start gap-2"
      >
        <span :class="{ 'text-blue-500': !imageView, 'text-black': !imageView }"
          >文本</span
        >
        <a-switch
          v-model:checked="imageView"
          class="mx-2"
          :disabled="!currentBizType.isViewChange"
        />
        <span :class="{ 'text-blue-500': imageView, 'text-black': imageView }"
          >图片</span
        >
      </div>
    </template>
    <template #seq="{ row }">
      <a-button
        v-if="currentBizType.isDrive"
        type="link"
        @click="linkToForbidInfo(row)"
      >
        {{ row.index }}
      </a-button>
      <span v-else>{{ row.index }}</span>
    </template>
    <template #avatar="{ row }">
      <a-image
        v-if="['avatar', 'avatar_async'].includes(scene)"
        style="width: 40px; height: 40px; border-radius: 3px"
        :src="row.file_url"
        :preview-src-list="[row.file_url]"
      />
    </template>
    <template #status="{ row }">
      <status-tag
        :status="getStatusVariant(row)"
        :tag-map="EXAMINE_TAG_MAP"
        :color-map="EXAMINE_COLOR_MAP"
        default-tag="未操作"
        other-color="grey"
      />
    </template>
    <template #fname="{ row }">
      <template v-if="row.is_grey">
        <span>{{ row.fname }}</span>
      </template>
      <template v-else>
        <file-preview :item="row" queue="taskGloble" />
      </template>
    </template>
    <template #extra_name="{ row }">
      <file-preview :item="row" queue="taskGloble" />
    </template>

    <template #link_ctime="{ row }">
      <span>
        {{
          row.create_time
            ? formatTimestampToDate(row.create_time)
            : row.link_ctime
              ? formatTimestampToDate(row.link_ctime)
              : ''
        }}
      </span>
    </template>
    <template #content="{ row }">
      <tool-wrapper :content="row.content" />
    </template>
    <template #scene="{ row }">
      <template
        v-if="!currentBizType.isWatermark && !currentBizType.isKdocsComment"
      >
        <span>
          {{ getSceneLabel(row.scene) }}
        </span>
      </template>
      <template v-else>
        <span>
          {{ row.scene }}
        </span>
      </template>
    </template>
    <template #fileinx="{ row }">
      <span v-if="row.task_tag == 4 && !currentBizType.isHomeComment"
        >{{ row.task_extra_json.simulate_push_id }}_{{
          row.task_extra_json.simulate_pool_id
        }}</span
      >
      <span v-else>{{ row.fileinx }}</span>
    </template>
    <template #type="{ row }">
      <span :class="{ grey: row.is_grey }">
        <template v-if="currentBizType.isSentiment">
          {{ forbidTypeSentiTextFilter(row.type) }}
        </template>
        <template
          v-else-if="
            (currentBizType.isComment && !currentBizType.isHomeComment) ||
            currentBizType.isWpsAccount
          "
        >
          {{ row.type === 0 ? '通过' : commentTypeFilter(row.type) }}
        </template>
        <template v-else>
          {{ findLabelByValue(allForbidTags, row.type, 'type', 'name') }}
        </template>
      </span>
    </template>
    <template v-if="curCheckLogConfig.biz.length > 0" #actions="{ row }">
      <span v-if="row.is_grey"> 已删除 </span>
      <div v-else>
        <update-forbid-comment-btn
          v-if="isCommentText && !currentBizType.isHomeComment"
          :key="curCheckLogConfig.biz"
          :isWatermark="currentBizType.isWatermark"
          :tabPlatform="curCheckLogConfig.biz"
          :scope="row"
          :callBack="searchTitle"
          :callback="handleNormalOperation"
        />
        <update-forbid-comment-btn
          v-else-if="
            currentBizType.isWpsAccount &&
            !['avatar', 'avatar_async'].includes(scene || '')
          "
          :tabPlatform="curCheckLogConfig.biz"
          :scope="row"
          :callBack="searchTitle"
          :callback="handleV2Operation"
        />
        <div v-else>
          <div
            v-if="currentBizType.isDrive && (getItem('flag') & 0x100) === 0x100"
            class="flex items-center"
          >
            <forbid-select
              v-model:value="row.paramsTypes"
              :version="'v2'"
              :pass="true"
              class="ml-2 w-[190px]"
              :isCascader="true"
              :multiple="true"
              :isClear="true"
              :maxTagCount="1"
              :filterType="[3, 5]"
              :tabPlatform="curCheckLogConfig.biz"
              @change="(values) => handleTypeChange(row, values)"
            />
            <a-button
              type="link"
              class="ml-2"
              :disabled="!row.paramsTypes?.length"
              @click="handleConfirm(row)"
            >
              确认
            </a-button>
          </div>
          <forbid-select
            v-else
            v-model="row.selectedType"
            :isGLobalList="true"
            :pass="true"
            :filterType="currentBizType.isHome ? [3, 4, 5, 9] : [3, 5]"
            :isCascader="true"
            class="ml-5 w-[120px]"
            :isClear="true"
            :tabPlatform="curCheckLogConfig.biz"
            :callback="(value: any) => handleAction(row, value)"
          />
        </div>
      </div>
    </template>
  </table-template>
  <a-modal
    v-model:open="visible"
    title="创建新任务"
    @ok="handleOk"
    @cancel="handleCancel"
    ok-text="确定"
    cancel-text="取消"
  >
    <p class="">关键字: {{ queryParams.keyword }}</p>
    <p class="modal-content">
      时间范围:
      {{ formatTimestampToDate(Number(queryParams.start_time)) }} -
      {{ formatTimestampToDate(Number(queryParams.end_time)) }}
    </p>
    <p class="modal-content">页码: {{ offlinePage.page + 1 }}</p>
  </a-modal>
</template>
