<script setup lang="ts">
import type { VxeGridProps } from "#/adapter/vxe-table";
import TableTemplate from "#/components/table/index.vue";
import {
  getLastNDaysRange,
  transformTimeFields,
  formatTimestampToDate,
} from "#/utils/time";
import {
  SENTIMENT_TYPE,
  OPERATE_MANUAL_STAUTS,
  DOC_MANUAL_STATUS,
  DOC_MANUAL_COLOR,
} from "#/constants/maps";
import { mapToOptions } from "#/utils/transform";
import { getConfirmRecord } from "#/api/operateLog";
import FileLink from "#/components/file/index.vue";
import StatusTag from "#/components/tag/index.vue";
import { BLACK_IMG_OPTIONS } from "#/constants/array/task";
import { findLabelByValue } from "#/utils/data";
import { SAMPLE_TYPE_MAP } from "#/constants/maps/label-map";
const searchOptions = {
  collapsed: false,
  schemas: {
    "1": [
      {
        component: "Select",
        fieldName: "purpose",
        label: "命中类型",
        defaultValue: "sentiment_image",
        componentProps: {
          options: mapToOptions(SENTIMENT_TYPE),
        },
      },
      {
        component: "Select",
        fieldName: "confirm_label",
        label: "人审结果",
        componentProps: {
          options: mapToOptions(OPERATE_MANUAL_STAUTS),
        },
      },
      {
        component: "Input",
        fieldName: "fileid",
        label: "fileid",
      },
      {
        component: "RangePicker",
        fieldName: "dateRange",
        label: "时间",
        class: "col-span-2",
        defaultValue: getLastNDaysRange(14, 0, "X"),
        componentProps: {
          valueFormat: "X",
          showTime: true,
        },
      },
    ],
  },
  showCollapseButton: true,
  submitOnChange: false,
  wrapperClass: "grid-cols-6",
};

const columns: VxeGridProps["columns"] = [
  {
    field: "purpose",
    title: "命中类型",
    align: "center",
    formatter: ({ cellValue }) => SENTIMENT_TYPE[cellValue],
  },
  {
    field: "fileid",
    title: "fileid",
  },
  {
    field: "extra_id",
    title: "附件id",
  },
  {
    field: "scene",
    title: "场景",
  },
  {
    title: "名称",
    width: 200,
    slots: {
      default: "fname",
    },
  },
  {
    title: "人工确认结果",
    slots: {
      default: "status",
    },
  },
  {
    field: "operate_time",
    title: "操作时间",
    formatter: ({ cellValue }) => formatTimestampToDate(cellValue),
  },
  {
    field: "confirm_label_desc",
    title: "人工确认标签",
    formatter: ({ cellValue }) =>
      findLabelByValue(BLACK_IMG_OPTIONS, cellValue, "type", "label"),
  },
  {
    field: "sample_id",
    title: "样本id",
  },
  {
    field: "sample_category",
    title: "样本类型",
    formatter: ({ cellValue }) => SAMPLE_TYPE_MAP[cellValue],
  },
];
const getData = async ({ page, pageSize, scene, ...formValues }: any) => {
  let params: any = {
    page: page - 1,
    size: pageSize,
    ...transformTimeFields(formValues, [["dateRange", ["start_time", "end_time"]]]),
  };

  const res = await getConfirmRecord(params);
  return { items: res?.data?.data || [], total: res?.data?.total_count || 0 };
};
</script>

<template>
  <table-template
    :columns="columns"
    :search-options="searchOptions"
    :query-method="getData"
  >
    <template #fname="{ row }">
      <file-link :item="{ ...row, fname: row.f_name }" />
    </template>
    <template #status="{ row }">
      <status-tag
        :status="row.confirm_label"
        :tag-map="DOC_MANUAL_STATUS"
        :color-map="DOC_MANUAL_COLOR"
      />
    </template>
  </table-template>
</template>
