<script lang="ts" setup>
import StandardForm from '#/components/form/index.vue';

import { reactive } from 'vue';
import { TYPES, DIFFICULTIES, TAGS } from '#/constants/maps/exam';
import { mapToOptions } from '#/utils/transform';

const emits = defineEmits([
  'changeType',
  'changeDifficulty',
  'changeTag',
  'changeWrong',
  'onSearch',
]);
const queryConditions = reactive({
  curTypeIndex: 0,
  curDifficultyIndex: 0,
  isWrongBank: false,
});

const schema = [
  {
    component: 'Select',
    componentProps: {
      options: mapToOptions(TAGS),
      placeholder: '请选择',

      allowClear: true,
    },
    fieldName: 'tags',
    label: '标签：',
    onChange: (value: string) => {
      handleTagChange(value);
    },
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入名称',
      style: 'width:200px',
    },
    fieldName: 'title',
    label: '试题名称：',
  },
  {
    component: 'Input',
    componentProps: {
      style: 'width:200px',
    },
    fieldName: 'remarks',
    label: '备注：',
  },
];
const commonConfig = {
  labelClass: 'w-auto',
  formItemClass: 'p-0',
};
const wrapperClass = 'grid grid-flow-col auto-cols-auto gap-4';
const actionWrapperClass = 'col-span-1 p-0'; //表单操作区域class
const submitButtonOptions = {
  content: '查询',
};
const resetButtonOptions = {
  show: false,
};

function handleTypeChange(e: Event) {
  emits('changeType', queryConditions.curTypeIndex);
}
function handleWrongChange() {
  emits('changeWrong', queryConditions.isWrongBank);
}
function handleDifficultyChange(e: Event) {
  emits('changeDifficulty', queryConditions.curDifficultyIndex);
}
function handleTagChange(value: any) {
  emits('changeTag', value);
}
function handleSearch(value: any) {
  emits('onSearch', value);
}
</script>
<template>
  <div class="bg-card mx-4 my-4 mt-4 p-2">
    <div>
      题型：
      <a-radio-group
        v-model:value="queryConditions.curTypeIndex"
        @change="handleTypeChange"
        option-type="button"
        :options="mapToOptions(TYPES)"
      >
      </a-radio-group>
      <a-checkbox
        v-model:checked="queryConditions.isWrongBank"
        @change="handleWrongChange"
        style="margin-left: 15px"
        >错题库</a-checkbox
      >
    </div>
    <div class="mt-4">
      难度：
      <a-radio-group
        v-model:value="queryConditions.curDifficultyIndex"
        @change="handleDifficultyChange"
        option-type="button"
        :options="mapToOptions(DIFFICULTIES)"
      >
      </a-radio-group>
    </div>
    <div class="flex items-center justify-between">
      <standard-form
        :schema="schema"
        :common-config="commonConfig"
        :wrapper-class="wrapperClass"
        :action-wrapper-class="actionWrapperClass"
        :submit-button-options="submitButtonOptions"
        :reset-button-options="resetButtonOptions"
        @submit="handleSearch"
      >
      </standard-form>
      <div>
        <slot name="search-right"></slot>
      </div>
    </div>
  </div>
</template>
