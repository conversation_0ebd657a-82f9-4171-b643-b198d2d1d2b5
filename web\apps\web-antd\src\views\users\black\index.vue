<script setup lang="ts">
import Roster from '../components/Roster.vue';
import type { VxeGridProps } from '#/adapter/vxe-table';

import {
  getBlackUsers,
  addBlackUser,
  deleteBlackUser,
  updateBlackUser,
} from '#/api/black-user';
import { ref, reactive, computed, watch } from 'vue';
import { deepClone } from '#/utils';
import { getItem } from '#/utils/save';
import { useApiHandler } from '#/composables/common/use-api-handler';
import type { FormSchema } from '#/types/form';
import StandardForm from '#/components/form/index.vue';
import {
  formatTimestampToDate,
  disabledDate,
  disabledDateTime,
  getDateAfterNDays,
} from '#/utils/time';

const { handleApiCall } = useApiHandler();

const formKey = ref(0);
const rosterTable = ref<any>(null);
const open = ref(false);
const modalType = ref('');
const enableOptions = [
  {
    label: '全部',
    value: '',
  },
  {
    label: '封禁',
    value: '1',
  },
  {
    label: '正常',
    value: '0',
  },
];
const forbidOptions = [
  {
    label: '全部',
    value: '',
  },
  {
    label: '政治',
    value: 10,
  },
  {
    label: '色情',
    value: 20,
  },
  {
    label: '暴恐',
    value: 30,
  },
  {
    label: '非法',
    value: 40,
  },
  {
    label: '低俗',
    value: 50,
  },
];
const searchOptions = {
  collapsed: false,
  schemas: {
    '1': [
      {
        component: 'Input',
        fieldName: 'userid',
        label: 'ID',
      },

      {
        component: 'Select',
        defaultValue: '',
        componentProps: {
          options: enableOptions,
        },
        fieldName: 'enable',
        label: '账号状态',
      },
      {
        component: 'Select',
        defaultValue: '',
        componentProps: {
          options: forbidOptions,

          clearable: true,
        },
        fieldName: 'forbid_type',
        label: '封禁类型',
      },
    ],
  },
};
const columns: VxeGridProps['columns'] = [
  {
    type: 'seq',
    title: '序号',
    width: 60,
    align: 'center',
  },
  {
    field: 'userid',
    title: 'UserID',
    align: 'center',
  },
  {
    field: 'forbid_type',
    title: '封禁类型',
    align: 'center',
    formatter: ({ cellValue }) => {
     const item = forbidOptions.find(item => item.value === cellValue)
      return item ? item.label : cellValue
    }
  },
  {
    field: 'forbidPeriod',
    title: '封禁周期',
    // "effect_time": -1, 且 "enable": 1, 有效时间 -1 代表永久封禁
    align: 'center',
    slots: { default: 'forbidPeriod' },
  },
  {
    field: 'modify_time',
    title: '操作时间',
    align: 'center',
    formatter: ({ cellValue }) =>
      formatTimestampToDate(cellValue, 'YYYY/M/D HH:mm:ss'),
  },
  {
    field: 'reviewer_name',
    title: '操作人',
    align: 'center',
  },
  {
    field: 'mark',
    title: '封禁原因',
    align: 'center',
  },
  {
    field: 'enable',
    title: '账号状态',
    align: 'center',
    slots: { default: 'enable' },
  },
  {
    title: '操作',
    align: 'center',
    slots: {
      default: 'operation',
    },
  },
];
const initialForm: Record<string, any> = {
  userid: '',
  enable: 1,
  mark: '',
  forbid_type: '',
  platform: '',
  expire_time: '',
  utype: 'b',
  kind: 'userid',
};
const ruleForm = ref({ ...initialForm });
const modalForm = ref<any>(null);
const userQuery = reactive({
  loading: false,
  disable: false,
  expire_time: '',
  type: '',
});
const lastTime = reactive({
  startTime: '',
  endTime: '',
});
const commonConfig = {
  labelWidth: 90,
};
const wrapperClass = 'pr-6';
// 动态生成表单 Schema
const schema = computed(() => {
  const baseFields: FormSchema[] = [
    {
      fieldName: 'userid',
      label: modalType.value === 'add' ? '用户ID' : '用户ID',
      component: 'Input',
      defaultValue: ruleForm.value.userid,
      componentProps: {
        disabled: modalType.value !== 'add',
        allowClear: modalType.value === 'add',
      },
      rules: modalType.value === 'add' ? 'required' : '',
    },
  ];

  if (modalType.value !== 'unseal') {
    baseFields.push({
      fieldName: 'forbid_type',
      label: '封禁类型',
      component: 'Select',
      defaultValue: ruleForm.value.forbid_type,
      componentProps: {
        options: forbidOptions.filter((item) => item.label !== '全部'),
        clearable: true,
        class: 'w-full',
      },
      rules: 'required',
    });
  }
  baseFields.push({
    fieldName: 'mark',
    label: getMarkLabel(),
    component: 'Textarea',
    defaultValue: ruleForm.value.mark,
    // componentProps: { rows: 4 },
    rules: 'required',
  });
  if (['add', 'ban', 'update'].includes(modalType.value)) {
    baseFields.push({
      fieldName: 'expire_time',
      label: '失效时间',
      component: 'DatePicker',
      defaultValue: ruleForm.value.expire_time,
      componentProps: {
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'x',
        disabledDate: disabledDate,
        disabledTime: disabledDateTime,
        class: 'w-full',
        presets: [
          { label: '1天后', value: getDateAfterNDays(1) },
          { label: '3天后', value: getDateAfterNDays(3) },
          { label: '7天后', value: getDateAfterNDays(7) },
          { label: '15天后', value: getDateAfterNDays(15) },
          { label: '30天后', value: getDateAfterNDays(30) },
          { label: '永久', value: '' },
        ],
      },
      help: '不选择失效时间则代表永久封禁',
    });
  }

  return baseFields;
});
const getMarkLabel = () => {
  const labels: Record<string, string> = {
    add: '封禁原因',
    ban: '封禁原因',
    update: '修改原因',
    unseal: '解禁原因',
  };
  return labels[modalType.value];
};
const modalTitle = computed(() => {
  if (modalType.value === 'add') {
    return '账号操作-封禁';
  } else if (modalType.value === 'ban') {
    return '账号操作-封禁';
  } else if (modalType.value === 'unseal') {
    return '账号操作-解禁';
  } else if (modalType.value === 'update') {
    return '账号操作-修改';
  }
});

const platform = computed(() => {
  return getItem('platform');
});
const isUpdateType = computed(() => {
  return modalType.value === 'update';
});
async function getList({
  page,
  pageSize,
  ...formValues
}: {
  page: number;
  pageSize: number;
  [key: string]: any;
}) {
  // 构建基础参数
  const baseParams = {
    page: page - 1,
    limit: pageSize,
    ...formValues,
  };
  console.log('formValues', baseParams);
  try {
    const { data } = await getBlackUsers(baseParams);
    return {
      items: data.data,
      total: data.count,
    };
  } catch (error) {
    return { items: [], total: 0 };
  }
}
function handleCreate() {
  open.value = true;
  modalType.value = 'add';
  ruleForm.value = { ...initialForm };
}
async function handleBan(row: any) {
  ruleForm.value = deepClone(row);
  console.log('封禁', row, ruleForm.value);
  open.value = true;
  modalType.value = 'ban';
  ruleForm.value.enable = 1;
  ruleForm.value.mark = '';
  ruleForm.value.expire_time = '';
  await modalForm.value?.formApi.setValues({ ...ruleForm.value });
}
function handleUnseal(row: any) {
  ruleForm.value = deepClone(row);
  open.value = true;
  modalType.value = 'unseal';
  ruleForm.value.enable = 0;
  ruleForm.value.mark = '';
  ruleForm.value.expire_time = '';
}
function handleEdit(row: any) {
  ruleForm.value = deepClone(row);
  open.value = true;
  modalType.value = 'update';
  ruleForm.value.expire_time =
    row.expire_time === -1 ? '' : row.expire_time * 1000;
  ruleForm.value.mark = '';
}
async function onSubmit() {
  const res = await modalForm.value.formApi.validate();
  if (!res.valid) return;
  const params = deepClone(ruleForm.value);
  params.platform = platform.value;
  params.expire_time = Math.round(params.expire_time / 1000);
  params.userid = Number(params.userid);
  params.forbid_type = Number(params.forbid_type);
  const data = {
    user_id: params.user_id,
    index_id: String(params.userid),
    userid: params.userid,
    enable: params.enable,
    mark: params.mark,
    platform: params.platform,
    // expire_time为空时表示永久封禁,传入-1
    expire_time: params.expire_time || -1,
    effect_time: isUpdateType
      ? params.effect_time
      : Math.floor(params.expire_time / 1000),
    utype: params.utype,
    kind: params.kind,
    forbid_type: params.forbid_type,
  };
  console.log('确定', data);
  const options = {
    successMsg: '保存成功',
    onSuccess: () => {
      rosterTable.value?.refreshTable();
    },
  };
  if (modalType.value === 'add') {
    await handleApiCall(addBlackUser, data, options);
  } else {
    console.log('不是新增');
    await handleApiCall(updateBlackUser, { ...data }, options);
  }

  modalForm.value.formApi.resetForm();

  open.value = false;
  userQuery.disable = false;
}
function handleModalValuesChange(values: any) {
  ruleForm.value = { ...ruleForm.value, ...values };
}
function resetState() {
  userQuery.loading = false;
  userQuery.disable = false;
  userQuery.type = '';

  modalType.value = '';
  lastTime.startTime = '';
  lastTime.endTime = '';
}
watch(modalType, (newVal) => {
  console.log('modalType变化', newVal);
  formKey.value++; // 通过 key 变化强制重新渲染
});
watch(open, async (newVal) => {
  if (!newVal) {
    await modalForm.value?.formApi.resetForm();
    resetState();
  }
});
</script>
<template>
  <div>
    <roster
      ref="rosterTable"
      :search-options="searchOptions"
      :columns="columns"
      :query-method="getList"
      roster-type="black"
      @on-create="handleCreate"
      @on-ban="handleBan"
      @on-lift="handleUnseal"
      @on-edit="handleEdit"
    >

    </roster>
    <a-modal v-model:open="open" :title="modalTitle" @ok="onSubmit('ruleForm')">
      <standard-form
        :key="formKey"
        ref="modalForm"
        :schema="schema"
        :showDefaultActions="false"
        :common-config="commonConfig"
        :wrapper-class="wrapperClass"
        :handle-values-change="handleModalValuesChange"
      ></standard-form>
    </a-modal>
  </div>
</template>
