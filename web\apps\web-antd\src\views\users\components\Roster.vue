<script setup lang="ts">
import type { VxeGridProps } from "#/adapter/vxe-table";
import { onMounted, ref, reactive, computed } from "vue";
import TableTemplate from "#/components/table/index.vue";
import { useBizInfo } from "#/composables/common/use-biz";
import { formatTimestampToDate, getCurrentTimestamp } from "#/utils/time";

interface Props {
  rosterType: string; //名单类型
  searchOptions: Record<string, any>;
  columns: VxeGridProps["columns"];
  queryMethod?: (params: any) => Promise<any>;
}
const props = withDefaults(defineProps<Props>(), {
  searchOptions: () => ({}),
  columns: () => [],
  queryMethod: () => Promise.resolve({}),
  rosterType: "",
});

const emit = defineEmits(["onCreate", "onEdit", "onDelete", "onBan", "onLift"]);

// const userStore = useUserStore();
// const userId = ref(userStore.userid);
const rosterTable = ref<any>(null);
const { bizList, fetchData: loadData } = useBizInfo();
onMounted(async () => {
  await loadData();
});
const getDetailByAliasPlatform = (alias_platform: string) => {
  if (!alias_platform) return "-";
  if (!bizList.value) return "-";
  return (
    bizList.value.find((platform: any) => platform.value === alias_platform)
      ?.label || "-"
  );
};

const titleOptions = {
  help: "",
  title: "",
};
const paginationOptions = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1,
});

function handleCreate() {
  emit("onCreate");
}
function handleEdit(row: any) {
  emit("onEdit", row);
}
function handleDelete(row: any) {
  emit("onDelete", row);
}

//账号是否正常
function enableStatus(enable: Number, expire_time: any) {
  return (
    enable === 0 || (expire_time < Math.floor(Date.now() / 1000) && expire_time !== -1)
  );
}
function refreshTable() {
  rosterTable.value?.refreshTable();
}
const enableNormalLabel = computed(() => {
  if (props.rosterType === "black") return "正常";
  else return "开启";
});
function handleBan(row: any) {
  emit("onBan", row);
}
function handleLift(row: any) {
  emit("onLift", row);
}
const toolbarOptions = {
  //删除导出功能
  // export: true,
};
function formatDuration(row: any) {
  const start = formatTimestampToDate(row.effect_time)
  console.log("start",start);
  
  if (row.expire_time === -1) {
    return [
      {
        time: start,
        event: '永久封禁',
        color: 'red',
      },
    ] 
  }
  const end = formatTimestampToDate(row.expire_time * 1000)
  return [
    {
      time: start,
      event: '开始封禁',
      color: 'red',
    },
    {
      time: end,
      event: '结束封禁',
      color: 'green',
    },
  ] 
}
const Colorclass = ({ row }: any) => {
  // 黑名单不应用样式
  if(props.rosterType === 'black') {
    return '';
  }
  if(props.rosterType === 'yellow') {
    if (enableStatus(row.enable, row.expire_time)&& (row.expire_time > getCurrentTimestamp() || row.expire_time === -1)) {
      return 'row-warning';
    }
    return '';
  }
  console.log("颜色主题", row);

  // 检查 utype 是否为红名单或白名单
  // if (row.utype === 'r' || row.utype === 'w') {
    if (row.enable !== 1) {
      return 'row-disable';
    }
    if(row.expire_time && row.expire_time < getCurrentTimestamp()){
      return 'row-warning';
    }
  // }

  return '';
};
defineExpose({
  refreshTable,
});
</script>
<template>
  <table-template ref="rosterTable" :search-options="props.searchOptions" :columns="props.columns"
    :title-options="titleOptions" :query-method="props.queryMethod" :pagination-options="paginationOptions"
    :toolbar-options="toolbarOptions" :row-class-name="Colorclass">
    <template #toolbar-left>
      <a-button type="primary" @click="handleCreate">新增</a-button>
    </template>

    <template #kind="{ row }">
      <span>
        {{ row.kind == "userid" ? "用户类" : row.kind == "fileid" ? "文件类" : "企业类" }}
      </span>
    </template>
    <template #forbidPeriod="{ row }">
      <a-timeline v-if="row.enable === 1" :pending="false">
        <a-timeline-item v-for="(item, idx) in formatDuration(row)" :key="idx" :color="item.color"
          :timestamp="item.time">
          {{ item.event }}
          <div>{{ item.time }}</div>
        </a-timeline-item>
      </a-timeline>
    </template>
    <template #platform="{ row }">
      <span> {{ getDetailByAliasPlatform(row.platform) }}</span>
    </template>
    <template #enable="{ row }">
      <template v-if="['black', 'yellow'].includes(props.rosterType)">
        <span v-if="enableStatus(row.enable, row.expire_time)">{{
          enableNormalLabel
          }}</span>
        <span v-else>封禁</span>
      </template>
      <template v-else-if="props.rosterType === 'redWhite'">
        <span v-if="row.enable === 1">启用</span>
        <span v-else>禁用</span>
      </template>
    </template>
    <template #utype="{ row }">
      {{
        { r: "红名单", a: "预警白名单", w: "白名单" }[row.utype] || ""
      }}
    </template>
    <template #operation="{ row }">
      <template v-if="['black', 'yellow'].includes(props.rosterType)">
        <a-button type="link" danger v-if="enableStatus(row.enable, row.expire_time)"
          @click="handleBan(row)">封禁</a-button>
        <a-button type="link" v-else @click="handleLift(row)">解禁</a-button>
        <a-button type="link" :disabled="enableStatus(row.enable, row.expire_time)"
          @click="handleEdit(row)">修改</a-button>
      </template>

      <template v-else-if="props.rosterType === 'redWhite'">
        <a-button type="link" @click="handleEdit(row)">编辑</a-button>
      </template>
      <template v-if="['yellow', 'redWhite'].includes(props.rosterType)">
        <a-popconfirm title="确认删除吗？" ok-text="确定" cancel-text="取消" @confirm="handleDelete(row)">
          <a-button type="link" danger>删除</a-button>
        </a-popconfirm>
      </template>
    </template>
  </table-template>
</template>
<style scoped>
:deep(.vxe-body--row.row-disable) {
  background-color: #fde2e2 !important;
}
:deep(.vxe-body--row.row-warning) {
  background-color: oldlace !important;
}
</style>