<script lang="ts" setup>
import { reactive, ref, watch } from 'vue';
import {
  queryQuestionList,
  deleteQuestions,
  createQuestionExcel,
} from '#/api/exam';

import TableTemplate from '#/components/table/index.vue';
import { message } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import QuestionSearch from './components/QuestionSearch.vue';
import { generatePresignedUrl } from '#/api/tool';
import Question from './components/question.vue';
import Topic from './exams-manage/topic.vue';
import { TYPES, DIFFICULTIES } from '#/constants/maps/exam';

const file = ref<any>(null);
const fileInput = ref<any>(null);
const questionTable = ref<any>(null);
const question = ref<any>({});
const isReadonly = ref(false);
const isEdit = ref(false);
const isTestManage = ref(true);
const isWarning = ref(false);
const isCreate = ref(false); // 试题录入
const isCreated = ref(false); // 试题是否加入题库
const editOpen = ref(false);
const importOpen = ref(false);
const drawerOpen = ref(false);
const queryConditions = reactive({
  curTypeIndex: 0,
  curDifficultyIndex: 0,
  isWrongBank: false,
  title: {
    msg: '',
    images: [],
  },
  tag: '',
  remarks: '',
});

const columns = ref<VxeGridProps['columns']>([
  {
    field: 'title',
    title: '题目',
    align: 'center',
    slots: {
      default: ({ row }) => {
        return row.title.msg;
      },
    },
  },
  {
    field: 'question_type',
    title: '题型',
    align: 'center',
    slots: {
      default: ({ row }) => {
        return TYPES[row.question_type];
      },
    },
  },
  {
    field: 'difficulty',
    title: '难度',
    align: 'center',
    slots: {
      default: ({ row }) => {
        return DIFFICULTIES[row.difficulty];
      },
    },
  },
  {
    field: 'tag',
    title: '标签',
    align: 'center',
  },
  {
    field: 'remarks',
    title: '备注',
    align: 'center',
  },
  {
    field: 'creator',
    title: '创建人',
    align: 'center',
  },
  {
    field: 'wrong_cnt',
    title: '错误次数',
    align: 'center',
    visible: queryConditions.isWrongBank,
  },
  {
    title: '操作',
    align: 'center',
    slots: { default: 'operation' },
  },
]);
const titleOptions = {
  help: '',
  title: '',
};
const paginationOptions = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1,
});
async function queryQuestionListData({
  page,
  pageSize,
}: {
  page: number;
  pageSize: number;
}) {
  // 构建基础参数
  const baseParams = {
    page,
    limit: pageSize,
  };
  try {
    const { data } = await queryQuestionList({
      question_type: queryConditions.curTypeIndex,
      difficulty: queryConditions.curDifficultyIndex,
      tag: queryConditions.tag,
      wrong_bank: queryConditions.isWrongBank,
      title: queryConditions.title,
      page: baseParams.page - 1,
      limit: baseParams.limit,
      remarks: queryConditions.remarks,
    });
    return {
      items: data.records,
      total: data.count,
    };
  } catch (error) {
    return { items: [], total: 0 };
  }
}
function changeType(value: number) {
  queryConditions.curTypeIndex = value;

  refresh();
}
function changeWrong(value: boolean) {
  queryConditions.isWrongBank = value;

  refresh();
}
function changeDifficulty(value: number) {
  queryConditions.curDifficultyIndex = value;

  refresh();
}
function changeTag(value: string) {
  queryConditions.tag = value;

  refresh();
}
function onSearch(value: any) {
  queryConditions.title.msg = value.title;
  queryConditions.remarks = value.remarks;
  refresh();
}
function getLabel(array: any[], value: number) {
  const item = array.find((item) => item.value === value);
  return item.label;
}
function handleEdit(row: any) {
  refresh();
  question.value = row;

  if (question.value.reference_num > 0) {
    isWarning.value = true;
    isReadonly.value = true;
  } else {
    isWarning.value = false;
    isReadonly.value = false;
  }
  editOpen.value = true;
}
async function handleDelete(row: any) {
  if (row.reference_num > 0) {
    message.warning('这道题已经用于试卷中，不可删除');
    return;
  }
  let idArray = [];
  idArray.push(row.id);
  const res = await deleteQuestions({ ids: idArray });
  if (res && res.result) {
    if (res.message === 'success') {
      message.success('删除成功');
    }
  }
  refresh();
}
function refresh() {
  if (questionTable.value) {
    questionTable.value.refreshTable();
  }
}
function clickImportBtn() {
  importOpen.value = true;
}

function importFile() {
  if (fileInput.value) fileInput.value.click();
}
const filePresignKey = ref(null);
async function handleExcelFiles(event: any) {
  file.value = event.target.files[0];

  const maxFileSize = 5 * 1024 * 1024; // 5MB
  if (file.value?.size > maxFileSize) {
    message.error('文件大小不能超过5MB');
    return;
  }

  const res = await generatePresignedUrl({
    file_name: file.value.name,
    expire: false,
  });
  if (res && res.result) {
    const presignUrl = res.data.url;
    filePresignKey.value = res.data.key;
    // 2. 上传文件到云存储
    try {
      const uploadResponse = await fetch(presignUrl, {
        method: 'PUT',
        headers: {
          'x-amz-acl': 'private',
          'Content-Type': 'application/octet-stream',
        },
        body: file.value, // 直接传文件，不用FormData
      });

      if (!uploadResponse.ok) {
        throw new Error('Failed to upload file');
      }
    } catch (error) {
      console.error('Upload failed:', error);
    }

    // const result = await createQuestionExcel({ key: presignKey });
    // if (result && result.result) {
    //   message.success('文件导入成功');
    //   refresh();
    // }
  }

  event.target.value = '';
}
function createQuestion() {
  isCreate.value = true;
  drawerOpen.value = true;
}

async function clickImportOkBtn() {
  if (!file.value) {
    message.warning('请选择要导入的文件');
    return;
  }
  const res = await createQuestionExcel({ key: filePresignKey.value });
  if (res && res.result) {
    message.success('文件导入成功');
    refresh();
    file.value = null;
    importOpen.value = false;
  } else {
    message.error(res.message);
  }
}
watch(isCreated, (newVal) => {
  if (newVal) refresh();
  isCreated.value = false;
});
watch(isTestManage, (newVal) => {
  if (newVal === false) {
    editOpen.value = false;
    isTestManage.value = true;
    if (isEdit.value) refresh();
  }
  isEdit.value = false;
});
</script>
<template>
  <div class="exam-management">
    <question-search
      @change-type="changeType"
      @change-wrong="changeWrong"
      @change-difficulty="changeDifficulty"
      @change-tag="changeTag"
      @on-search="onSearch"
    >
      <template #search-right>
        <div class="mt-6">
          <a-button
            type="primary"
            style="margin-right: 30px"
            @click="clickImportBtn"
            >导入</a-button
          >
          <a-button type="primary" @click="createQuestion">试题录入</a-button>
        </div>
      </template>
    </question-search>
    <table-template
      ref="questionTable"
      :columns="columns"
      :title-options="titleOptions"
      :pagination-options="paginationOptions"
      :query-method="queryQuestionListData"
    >
      <template #operation="{ row }">
        <a-button type="link" @click="handleEdit(row)">编辑</a-button>
        <a-popconfirm
          title="确定删除吗?"
          ok-text="确定"
          cancel-text="取消"
          @confirm="handleDelete(row)"
        >
          <a-button type="link" :danger="true">删除</a-button>
        </a-popconfirm>
      </template>
    </table-template>
    <a-modal
      v-model:open="editOpen"
      title=""
      width="80%"
      centered
      :footer="null"
    >
      <a-alert
        v-if="isWarning"
        message="这道题已经用于试卷中，不可更改"
        banner
        type="warning"
        show-icon
        class="mx-auto w-[60%]"
      >
      </a-alert>
      <question
        :questionCard="question"
        :readOnly="isReadonly"
        v-model:isTestManage="isTestManage"
        v-model:isEdit="isEdit"
      >
      </question>
    </a-modal>
    <a-modal
      v-model:open="importOpen"
      title="批量导入"
      width="30%"
      @ok="clickImportOkBtn"
      @cancel="importOpen = false"
    >
      <div>
        附件格式参考：<span class="text-[#006be6]"
          >https://365.kdocs.cn/l/co1uOj9g0S2C
        </span>
      </div>
      <a-button type="primary" class="my-2.5" @click="importFile"
        >点击上传</a-button
      >

      <div>只能上传csv或xlsx文件，且不超过5MB</div>
    </a-modal>
    <input
      type="file"
      ref="fileInput"
      class="hidden"
      accept=".xls,.xlsx,.csv"
      @change="handleExcelFiles"
    />
    <a-drawer
      title="试题录入"
      v-model:open="drawerOpen"
      width="70%"
      :maskClosable="false"
    >
      <topic v-model:isCreate="isCreate" v-model:isCreated="isCreated"></topic>
    </a-drawer>
  </div>
</template>
