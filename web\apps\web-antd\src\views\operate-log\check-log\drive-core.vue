<script setup lang="ts">
import { ref, computed, shallowRef, onMounted } from "vue";
import type { VxeGridProps } from "#/adapter/vxe-table";
import { storeToRefs } from "pinia";
import TableTemplate from "#/components/table/index.vue";
import SceneSelect from "#/components/select/scene.vue";
import FileLink from "#/components/file/index.vue";
import ForbidTypeSelect from "#/components/select/ForbidType.vue";
import StatusTag from "#/components/tag/index.vue";
import { mapToOptions } from "#/utils/transform";
import { useSupplier } from "#/composables/common/use-supplier";
import { useBizInfo } from "#/composables/common/use-biz";
import RecordFlagSelect from "#/components/select/product.vue";

import { findLabelByValue } from "#/utils/data";
import useForbidStore from "#/store/forbid";
const forbidStore = useForbidStore();
const { allForbidTags } = storeToRefs(forbidStore);
const { supplierData, fetchData: loadSupplierData } = useSupplier();
import { NO_LIMIT_NUM } from "#/constants/num";

import {
  formatTimestampToDate,
  formatSecondsToReadableTime,
  getLastNDaysRange,
  transformTimeFields,
} from "#/utils/time";
import {
  DOC_MANUAL_STATUS,
  DOC_MANUAL_COLOR,
  DOC_TYPE_MAP,
  OPERATE_ROLE_MAP,
  RECALL_MAP,
  MODEL_RESULT_MAP,
  TASK_TAG_MAP,
  NLP_MODEL_MAP,
  STATUS_MAP,
  ENTRY_MAP,
  HIT_TYPE_MAP,
  HOME_COMMENTS_ENTRY_MAP,
  HIT_POLICY_MAP,
  TEXT_FORBID_LABEL_MAP,
  DOCS_ENTRY_MAP,
} from "#/constants/maps";
import { z } from "zod";

import { getRecordLogs } from "#/api/operateLog";
import { usePasteField } from "#/composables/common/use-paste-field";
// 在组件中
const { clipData, handlePasteArray } = usePasteField();
interface QueryParams {
  page: number;
  limit: number;
  from: string;
  start_time?: number;
  end_time?: number;
  [key: string]: any;
}

const props = defineProps({
  biz: {
    // type: String,
    default: "drive_core",
  },
  bizConfig: {
    type: Object,
    default: () => ({}),
  },
});

// 业务类型判断计算属性
const bizTypes = computed(() => {
  const { bizType } = useBizInfo(props.biz);
  return bizType;
});

// 搜索表单架构
const searchSchemas = computed(() => {
  const bt = bizTypes.value.value;
  const isStandardDoc =
    bt.isDrive || bt.isDocer || bt.isForm || bt.isHomeTopic || bt.isAK;
  // 合并所有表单项
  return {
    "1": [
      {
        fieldName: "fname",
        component: "Input",
        label: bt.isWatermark ? "文档id" : "名称",
        dependencies: {
          if(values) {
            return bt.isDrive || bt.isHomeTopic || bt.isForm || bt.isAK;
          },
          triggerFields: [""],
        },
      },
      {
        fieldName: "userid",
        component: "Input",
        label: "userid",
        dependencies: {
          if(values) {
            return bt.isHomeComment || bt.isWatermark;
          },
          triggerFields: [""],
        },
      },
      {
        fieldName: "nickname",
        component: "Input",
        label: "文本",
        dependencies: {
          if(values) {
            return bt.isHomeComment || bt.isWatermark;
          },
          triggerFields: [""],
        },
      },
      {
        fieldName: "fileinx",
        component: "Input",
        label: "fileID",
        componentProps: {
          // onPaste: (e: ClipboardEvent) => handlePasteArray(e),
        },
      },
      {
        fieldName: "scene",
        component: shallowRef(SceneSelect),
        label: "场景",
        componentProps: {
          biz: props.biz,
        },
        dependencies: {
          if(values) {
            return isStandardDoc;
          },
          trigger(values, form) {
            if (!values.fileinx) {
              form.setFieldValue("scene", "");
            } else if (Array.isArray(clipData.value)) {
              form.setFieldValue("scene", clipData.value[2]);
            }
          },
          // 只有指定的字段改变时，才会触发
          triggerFields: ["fileinx"],
        },
      },
      {
        fieldName: "extra_id",
        component: "Input",
        label: "附件id",
        dependencies: {
          if(values) {
            return isStandardDoc;
          },
          trigger(values, form) {
            if (!values.fileinx) {
              form.setFieldValue("extra_id", ""); //重置用
            } else if (Array.isArray(clipData.value)) {
              form.setFieldValue("extra_id", clipData.value[2]);
            }
          },
          // 只有指定的字段改变时，才会触发
          triggerFields: ["fileinx"],
        },
      },
      {
        fieldName: "company",
        component: "Input",
        label: "企业id",
        dependencies: {
          if(values) {
            return bt.isHomeComment;
          },
          triggerFields: [""],
        },
      },
      {
        fieldName: "reviewer",
        component: "Input",
        label: "操作人",
      },
      {
        fieldName: "entry",
        component: "Select",
        label: "页面入口",
        componentProps: () => ({
          options: bt.isHomeComment
            ? mapToOptions(HOME_COMMENTS_ENTRY_MAP)
            : [{ label: "全部", value: "" }, ...mapToOptions(DOCS_ENTRY_MAP)],
        }),
      },
      {
        fieldName: "status",
        component: "Select",
        label: "状态",
        componentProps: {
          options: mapToOptions(STATUS_MAP),
        },
        colProps: { span: 6 },
      },
      {
        fieldName: "reviewer_role",
        component: "Select",
        label: "操作角色",
        defaultValue: "manual",
        componentProps: {
          options: mapToOptions(OPERATE_ROLE_MAP),
        },
        colProps: { span: 6 },
      },
      {
        fieldName: "nlp_model",
        component: "Select",
        label: "生效模型",
        componentProps: {
          options: mapToOptions(NLP_MODEL_MAP),
        },
        dependencies: {
          if(values) {
            return bt.isDrive;
          },
          triggerFields: [""],
        },
      },
      {
        fieldName: "nlp_status",
        component: "Select",
        label: "模型结果",
        dependencies: {
          if(values) {
            return bt.isDrive;
          },
          triggerFields: [""],
        },
        componentProps: {
          options: mapToOptions(MODEL_RESULT_MAP),
        },
      },
      {
        fieldName: "hits",
        component: "Select",
        label: "召回环节",
        dependencies: {
          if(values) {
            return !bt.isHomeComment;
          },
          triggerFields: [""],
        },
        componentProps: {
          options: mapToOptions(RECALL_MAP),
          mode: "multiple",
        },
      },
      {
        fieldName: "task_tag",
        component: "Select",
        label: "审核标签",
        dependencies: {
          if(values) {
            return !bt.isHomeComment;
          },
          triggerFields: [""],
        },
        componentProps: {
          options: mapToOptions(TASK_TAG_MAP),
        },
      },
      {
        fieldName: "types",
        component: shallowRef(ForbidTypeSelect),
        dependencies: {
          if(values) {
            return !bt.isHomeComment;
          },
          triggerFields: [""],
        },
        componentProps: { version: "v2", isCascader: true, multiple: true },
        label: "标签2.0",
      },

      {
        fieldName: "type",
        component: "Select",
        label: "类型",
        dependencies: {
          if(values) {
            return bt.isHomeComment;
          },
          triggerFields: [""],
        },
        componentProps: {
          options: [{ label: "全部", value: "" }, ...mapToOptions(TEXT_FORBID_LABEL_MAP)],
        },
      },
      {
        fieldName: "record_flag",
        component: shallowRef(RecordFlagSelect),
        label: "文档类型",
        dependencies: {
          if(values) {
            return bt.isDrive;
          },
          triggerFields: [""],
        },
        componentProps: {
          productLineType: "record_flag",
          modelPropName: "value",
        },
      },
      {
        fieldName: "time",
        component: "RangePicker",
        label: "操作时间",
        defaultValue: getLastNDaysRange(7, 0, "X"),
        class: "col-span-2",
        componentProps: {
          showTime: true,
          valueFormat: "X",
        },
      },
    ],
  };
});

// 表格列配置
const columns = computed<VxeGridProps["columns"]>(() => {
  const bt = bizTypes.value.value;
  // 基础列定义
  const baseColumns = [
    {
      type: "seq",
      title: "序号",
      width: 60,
      fixed: "left",
      visible: true,
    },
    {
      title: "状态",
      width: 80,
      fixed: "left",
      visible: true,
      slots: { default: "status" },
    },
    {
      field: "fileinx",
      title: bt.isHomeComment ? "评论id" : "fileID",
      width: 160,
      fixed: "left",
    },
    {
      field: "scene",
      title: "场景",
      minWidth: 120,
    },
    {
      field: "extra_id",
      title: "附件id",
      minWidth: 120,
    },
    {
      title: "附件名称",
      visible: bt.isHomeComment,
      minWidth: 120,
    },
    {
      title: "名称",
      width: 300,
      slots: { default: "fname" },
      visible: true,
    },
    {
      field: "type",
      title: "类型",
      minWidth: 120,
      visible: !bt.isHomeComment,
      formatter: ({ cellValue }) =>
        findLabelByValue(allForbidTags.value, cellValue, "type", "name"),
    },
    {
      field: "type",
      title: "类型",
      minWidth: 120,
      visible: bt.isHomeTopic,
      formatter: ({ cellValue }) => TEXT_FORBID_LABEL_MAP[cellValue],
    },
  ];

  // 文档特有列
  const documentColumns = [
    {
      field: "doc_type",
      title: "文档类型",
      minWidth: 120,
      visible: bt.isDrive,
      customRender: ({ record }: { record: any }) => {
        if (record.record_flag_desc && Array.isArray(record.record_flag_desc)) {
          return record.record_flag_desc.join(", ");
        }
        return "-";
      },
    },
    {
      field: "file_level",
      title: "文档等级",
      minWidth: 120,
      visible: bt.isDrive,
      formatter: ({ record }: { record: any }) => {
        return record?.task_extra_json?.ex_status?.fl || 0;
      },
    },
  ];

  // 标签和召回列
  const tagColumns = [
    {
      field: "types",
      title: "标签2.0",
      minWidth: 200,
      visible: !bt.isKdocsComment && !bt.isWpsAccount && !bt.isHomeComment && !bt.isAI,
      customRender: ({ record }: { record: any }) => {
        if (record.types && Array.isArray(record.types)) {
          return record.types.join(", ");
        }
        return "-";
      },
    },
    {
      field: "hits",
      title: "召回环节",
      width: 200,
      formatter: ({ row }: { row: any }) => {
        const mapResults = row.hits?.map((key) => RECALL_MAP[key]) || [];
        const result = mapResults.join("、");
        return result;
      },
    },
    {
      field: "model_status",
      title: "模型结果",
      minWidth: 150,
      visible: bt.isDrive,
      slots: { default: "model_status" },
    },
  ];

  // 用户统计列
  const userColumns = [
    {
      field: "report_id",
      title: "上报者ID",
      width: 120,
    },
    {
      field: "userid",
      title: "UserID",
      width: 120,
      // visible: bt.isDrive ,
    },
    {
      field: "pv_count",
      title: "pv",
      width: 120,
      // visible: bt.isDrive,
    },
    {
      field: "uv_count",
      title: "uv",
      width: 120,
      // visible: bt.isDrive,
    },
    {
      field: "gpv_count",
      title: "gpv",
      width: 120,
      // visible: bt.isDrive,
    },
  ];

  // 发布相关列
  const publishColumns = [
    {
      field: "link_ctime",
      title: "发布时间",
      width: 160,
      visible: bt.isDrive || bt.isDocer,
      formatter: ({ cellValue }: { cellValue: any }) => formatTimestampToDate(cellValue),
    },
    {
      field: "supplier",
      title: "供应商",
      width: 100,
      visible: bt.isDrive || bt.isDocer,
      formatter: ({ cellValue }: { cellValue: any }) =>
        findLabelByValue(supplierData.value, cellValue, "supplier", "mark"),
    },
  ];

  // 审核操作列
  const auditColumns = [
    {
      field: "real_name",
      title: "操作人",
      width: 130,
    },
    {
      field: "status",
      title: "预览状态",
      width: 100,
      visible: bt.isDrive || bt.isDocer,
      formatter: ({ row }: { row: any }) => HIT_TYPE_MAP[row.manaccess] || row.status,
    },
    {
      field: "entry",
      title: "审批入口",
      width: 120,
      visible: bt.isDrive || bt.isDocer,
      formatter: ({ cellValue }: { cellValue: any }) => ENTRY_MAP[cellValue] || cellValue,
    },
    {
      title: "审核标签",
      width: 120,
      visible: bt.isDrive || bt.isDocer,
      slots: { default: "task_tag" },
    },
    {
      field: "policy_flag",
      title: "命中策略",
      width: 120,
      visible: true,
      formatter: ({ cellValue }: { cellValue: any }) =>
        HIT_POLICY_MAP[cellValue] || cellValue,
    },
  ];

  // 时间相关列
  const timeColumns = [
    {
      field: "pull_create_time",
      title: "首次入列时间",
      width: 160,
      visible: bt.isDrive || bt.isDocer || bt.isHomeTopic,
      tooltip: "第一次机审推队列的时间",
      formatter: ({ cellValue }: { cellValue: any }) => {
        return formatTimestampToDate(cellValue);
      },
    },
    {
      field: "modify_time",
      title: "入列时间",
      width: 160,
      visible: bt.isDrive || bt.isDocer || bt.isHomeTopic,
      tooltip: "最新机审推队列的时间点",
      formatter: ({ cellValue }: { cellValue: any }) => {
        return formatTimestampToDate(cellValue);
      },
    },
    {
      field: "create_time",
      title: "机审时间",
      width: 160,
      visible: bt.isKdocsComment,
      formatter: ({ cellValue }: { cellValue: any }) => formatTimestampToDate(cellValue),
    },
    {
      title: "拉单时间",
      width: 160,
      visible: bt.isKdocsComment,
      formatter: ({ row }: { row: any }) =>
        formatTimestampToDate(row.create_time + parseInt(row.delay_time || "0")),
    },
    {
      field: "pull_time",
      title: "拉单时间",
      width: 160,
      visible: bt.isDrive || bt.isDocer || bt.isHomeTopic,
      tooltip: "审核员第一次将任务拉到自己名下的时间点，切换页面后重新拉单等操作不会更新",
      formatter: ({ cellValue }: { cellValue: any }) => formatTimestampToDate(cellValue),
    },
    {
      field: "delay_time",
      title: "拉单延迟",
      width: 100,
      visible: bt.isDrive || bt.isDocer || bt.isHomeTopic,
      tooltip: "拉单时间-拉单前最新入列时间",
      formatter: ({ cellValue }: { cellValue: any }) =>
        formatSecondsToReadableTime(cellValue),
    },
    {
      field: "manual_cost",
      title: "操作耗时",
      width: 100,
      // visible: bt.isDrive || bt.isDocer || bt.isKdocsComment,
      tooltip: "操作时间-拉单时间",
      formatter: ({ cellValue }: { cellValue: any }) =>
        formatSecondsToReadableTime(cellValue),
    },
    {
      field: "operation_time",
      title: "操作时间",
      width: 160,
      fixed: "right",
      visible: true,
      tooltip: "审核员点击操作的时间点",
      formatter: ({ cellValue }: { cellValue: any }) => formatTimestampToDate(cellValue),
    },
  ];

  // 合并所有列并过滤掉不可见的列
  return [
    ...baseColumns,
    ...documentColumns,
    ...tagColumns,
    ...userColumns,
    ...publishColumns,
    ...auditColumns,
    ...timeColumns,
  ].filter((column) => column.visible !== false);
});

// 分页配置
const paginationOptions = ref({
  pageSize: 20,
  currentPage: 1,
  layouts: ["PrevPage", "NextPage"],
});

// 查询数据方法
const queryData = async ({ page, pageSize, ...formValues }: any) => {
  const baseParams: QueryParams = {
    page: page - 1,
    size: pageSize,
    from: props.biz.value,
    ...transformTimeFields(formValues, [["time", ["start_time", "end_time"]]]),
  };
  baseParams.fileinx = baseParams.fileinx?.replace(/\s/g, "");
  try {
    let data = await getRecordLogs(baseParams);
    const items = data?.data?.logs || [];
    return { items, total: items.length == pageSize ? NO_LIMIT_NUM : 0 };
  } catch (error) {
    console.error("查询失败", error);
    return { items: [], total: 0 };
  }
};

// 搜索配置项
const searchOptions = computed(() => ({
  collapsed: false,
  schemas: searchSchemas.value,
  showCollapseButton: true,
  submitOnChange: false,
  wrapperClass: "grid-cols-6",
}));
onMounted(() => {
  loadSupplierData();
});
</script>

<template>
  <div>
    <table-template
      :key="props.biz.value"
      :search-options="searchOptions"
      :columns="columns"
      :pagination-options="paginationOptions"
      :query-method="queryData"
    >
      <template #fname="{ row }">
        <span v-if="row.pvless & 0x20">
          {{ row.fname }}
        </span>
        <file-link v-else :item="row" />
      </template>
      <template #task_tag="{ row }">
        <status-tag
          :tag-map="TASK_TAG_MAP"
          :status="row.task_tag"
          :color-map="{ 0: 'green' }"
        />
      </template>
      <template #model_status="{ row }">
        <div v-for="(item, index) in row.model_status">
          {{ `${NLP_MODEL_MAP[index]}:${MODEL_RESULT_MAP[item]}` }}
        </div>
      </template>
      <template #status="{ row }">
        <status-tag
          :tag-map="DOC_MANUAL_STATUS"
          :color-map="DOC_MANUAL_COLOR"
          :status="row.status"
        />
      </template>
    </table-template>
  </div>
</template>
