<script lang="ts" setup>
import {
  ref,
  onUnmounted,
  watch,
  computed,
  onMounted,
  useTemplateRef,
} from 'vue';
import KeywordsJump from '#/components/task/KeywordsJump.vue';
import { getUrlData } from '#/api/preview';
import { getKeywordsWordLabels } from '#/api/operateLog';
import type { Keyword } from '#/types/task';
import { useTaskStore } from '#/store/task';
import { IconifyIcon } from '@vben/icons';
import { useRoute } from 'vue-router';
import type { Label } from '#/types/task';
import { useKeywordSearch } from '#/composables/task/use-woh-keyword-search';
import { useWebOfficeSDK } from '#/composables/task/use-weboffice-sdk';
// PDF评论类型定义
interface PdfComment {
  id: string;
  data: string;
  [key: string]: any;
}

const route = useRoute();
// 类型定义
interface Props {
  data: {
    url: string;
    token: string;
    keywords: string[];
    keyword_url: string;
  };
  highlight?: boolean;
}

// props 定义
const props = withDefaults(defineProps<Props>(), {
  data: () => ({
    url: '',
    token: '',
    keywords: [],
    keyword_url: '',
  }),
  highlight: false,
});

// 状态管理
const taskStore = useTaskStore();
const webofficeBoxElement = useTemplateRef('webofficeBoxElement');
const isKeywordPanelVisible = ref(true);
const keywordList = ref<Keyword[]>([]);
const keywordsLabels = ref<Label[]>([]);
const pdfComments = ref<PdfComment[]>([]);
const pdfCommentsLoaded = ref(false);
const keywordLoaded = ref(false);

// 计算属性
const fileExtension = computed(() => {
  return taskStore.taskFileExtension || route.query.ext?.toString() || '';
});
// 事件
const emit = defineEmits(['open','error']);
// ！注意： instance 有可能是null，只有在ready后才能操作，如果需要操作instance，
// 需要使用whenReady或者异步方法await executeIfReady（适合需要等待返回值的情况），前者会将你的任务放入一个队列，等到Ready后执行，后者原理相同，但是执行后会调用resolve返回结果
// ！注意： 在unmounted时，destroy会自动被调用，会自动销毁，所以不需要手动销毁
// 确保initWeboffice只在onMounted时调用一次，需要更新时，使用resetup
const { instance, initWeboffice,resetup,whenReady,isReady } = useWebOfficeSDK(webofficeBoxElement,emit)
const { searchKeyword, clearHighlightTimers, fileType } = useKeywordSearch(instance,fileExtension);



// 计算属性
const isPdf = computed(() => fileType.value === 'pdf');


// 监听数据变化
watch(
  () => props.data,
  async () => {
    // 先销毁，再初始化
    await resetup({
      url: props.data.url,
      token: props.data.token,
    });
    await loadKeywordData();
  },
);

// 数据加载函数
const loadKeywordData = async () => {
  if (!props.data.keyword_url){ 
    keywordLoaded.value = true;
    return;
  }
  keywordLoaded.value = false;
  const resp = await getUrlData(props.data.keyword_url);
  keywordList.value = resp.highlight_words;
  await getKeywordsLabelData();
  keywordLoaded.value = true;
};

const initPdfComment = async () => {
  if (!instance.value) return;

  const appInstance = instance.value.Application;
  const totalPages = await appInstance.ActivePDF.PagesCount;

  for (let i = 1; i <= totalPages; i++) {
    const page = await appInstance.ActivePDF.PageCommentData(i);
    if (page.length > 0) {
      pdfComments.value.push(...page);
    }
  }

  pdfCommentsLoaded.value = true;
};

const getKeywordsLabelData = async () => {
  const labelsResponseData = await getKeywordsWordLabels();
  keywordsLabels.value = labelsResponseData.data.labels;
};

// 关键词搜索相关函数
const clickKeyword = (keyword: Keyword) => {
  taskStore.highlightClickTimes++;
  if (!isReady.value) return;
  searchKeyword(keyword);
};

// 生命周期钩子
onMounted(async () => {
 initWeboffice({
    url: props.data.url,
    token: props.data.token,
  });
  whenReady(() => {
    initPdfComment();
  });
  await loadKeywordData();
});

onUnmounted(async () => {
  taskStore.highlightClickTimes = 0;
  // 清除所有高亮定时器，防止内存泄漏
  clearHighlightTimers();
});
</script>

<template>
  <div class="flex">
    <div v-if="highlight" class="border-t">
      <div v-if="isKeywordPanelVisible" class="relative h-full">
        <iconify-icon
          @click="isKeywordPanelVisible = !isKeywordPanelVisible"
          class="absolute right-1 top-3 cursor-pointer"
          icon="ant-design:left-outlined"
        />
        <keywords-jump
          :is-loading="!keywordLoaded"
          :keywords="keywordList"
          class="h-full w-[150px]"
          title="关键词列表"
          :labels="keywordsLabels"
          @click="clickKeyword"
        />
      </div>
      <a-button
        v-else
        @click="isKeywordPanelVisible = !isKeywordPanelVisible"
        class="absolute left-0 top-1 rounded-l-none border-l-0 p-0 px-1"
      >
        ≡
      </a-button>
    </div>
    <div class="flex h-full w-full border" ref="webofficeBoxElement"></div>
    <div v-if="isPdf" class="flex h-full w-[200px] flex-col border-t">
      <div class="shrink-0 border-b p-2 font-bold">批注</div>
      <div class="flex-1 overflow-y-auto">
        <div
          class="m-1 cursor-pointer rounded-md border p-2"
          v-for="comment in pdfComments"
          :key="comment.id"
        >
          <a-tooltip :title="comment.data">
            <div class="truncate">
              {{ comment.data }}
            </div>
          </a-tooltip>
        </div>
        <div v-if="pdfCommentsLoaded" class="py-1 text-center text-blue-500">
          加载完毕
        </div>
      </div>
    </div>
  </div>
</template>
