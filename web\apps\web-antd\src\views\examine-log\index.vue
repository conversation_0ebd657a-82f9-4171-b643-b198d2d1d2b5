<!-- src/views/examine-log/ExamineLog.vue -->
<script lang="ts" setup>
import { computed, onMounted, provide, ref } from "vue";

import BizTabs from "#/components/task/BizTabs.vue";
import useFetchBizOptions from "#/composables/task/use-fetch-biz-options";

import ExamineLogTable from "./components/driver-core-checklog.vue";

// 状态与引用
const isLoading = ref(true);
const curCheckLogConfig = ref({
  biz: "drive_core",
});

const { BizOptions, FetchBizOptions } = useFetchBizOptions();
provide("BizOptions", BizOptions);

const bizTabsRef = ref<any>(null);
const currentBiz = ref("");

const updateBiz = (biz: any) => {
  if (typeof biz === "object" && biz !== null) {
    curCheckLogConfig.value = biz;
  } else {
    curCheckLogConfig.value.biz = biz;
  }
};

const getCurOption = (data: any) => {
  updateBiz(data || { biz: currentBiz.value });
};

const tableProps = computed(() => ({
  bizConfig: curCheckLogConfig.value,
  isLoading: isLoading.value,
}));

// 页面加载时获取业务选项
onMounted(async () => {
  await FetchBizOptions(); // 加载所有业务选项数据
  isLoading.value = false;
});
</script>

<template>
  <div>
    <biz-tabs
      ref="bizTabsRef"
      v-model:value="currentBiz"
      class="pl-2"
      diabled-badge
      @send-cur-option="getCurOption"
    />
    <!-- 业务日志表格，使用 v-bind 绑定 props -->
    <examine-log-table v-bind="tableProps" />
  </div>
</template>
