<script setup lang="ts">
import TableTemplate from '#/components/table/index.vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { ref, reactive, shallowRef, computed } from 'vue';
import AiMarkSelect from '#/components/select/ai-mark.vue';
import { mapToOptions } from '#/utils/transform';
import { CV_IMG_MARK_MAP } from '#/constants/maps/ai-mark';
import { getLastNDaysRange } from '#/utils/time';
import ImageView from '#/components/image/index.vue';
import { formatTimestampToDate, transformTimeFields } from '#/utils/time';
import UpdateAimarkBtn from '#/components/UpdateAimarkBtn.vue';
import { getCVLogList } from '#/api/cv';
import { queryCustmizeLabel } from '#/api/ai-mark';

const formInline = ref({
  time: getLastNDaysRange(0, 0, 'X'),
});
const docTable: any = ref(null);
const aimarkLabelMap = ref([]);
const searchOptions = ref({
  collapsed: false,
  schemas: {
    '1': [
      {
        component: 'Input',
        fieldName: 'reviewer_name',
        label: '操作人',
      },
      {
        component: shallowRef(AiMarkSelect),
        componentProps: {
          placeholder: '请选择复审类型', // 占位符文本
          onAimarkLabel: getAimarkLabel,
        },
        fieldName: 'sample_type',
        label: '复审类型',
      },
      {
        component: 'Select',
        componentProps: {
          options: [
            {
              label: '全部',
              value: '',
            },
            {
              label: '白样本',
              value: 'ok',
            },
            {
              label: '黑样本',
              value: 'forbid',
            },
          ],
        },
        fieldName: 'status',
        label: '状态',
      },
      {
        component: 'Select',
        componentProps: {
          options: mapToOptions(CV_IMG_MARK_MAP),
        },
        fieldName: 'entry',
        label: '入口',
      },
      {
        component: 'RangePicker',
        componentProps: {
          showTime: true,
          format: 'YYYY-MM-DD HH:mm:ss',
          placeholder: ['开始日期', '结束日期'],
          valueFormat: 'X',
        },
        defaultValue: formInline.value.time,
        fieldName: 'time',
        label: '时间',
      },
    ],
  },
  showCollapseButton: false,
  submitOnChange: false,
});
const STATUS_MAP: any = {
  ok: '通过',
  forbid: '拒绝',
};
const columns = ref<VxeGridProps['columns']>([
  {
    type: 'seq',
    title: '序号',
    width: 80,
    align: 'center',
  },
  {
    title: '状态',
    field: 'status',
    align: 'center',
    slots: {
      default: ({ row }) => {
        return STATUS_MAP[row.status];
      },
    },
  },
  {
    title: '内容',
    align: 'center',
    slots: {
      default: 'content',
    },
  },
  {
    title: '操作人',
    field: 'reviewer_name',
    align: 'center',
  },
  {
    title: '复审类型',
    field: 'type',
    align: 'center',
    slots: {
      default: ({ row }) => {
        return aimarkLabelMap.value[row.type]
          ? aimarkLabelMap.value[row.type]
          : '';
      },
    },
  },
  {
    title: '入口',
    field: 'entry',
    align: 'center',
    slots: {
      default: ({ row }) => {
        return CV_IMG_MARK_MAP[row.entry] ? CV_IMG_MARK_MAP[row.entry] : '';
      },
    },
  },
  {
    title: '操作时间',
    field: 'modify_time',
    align: 'center',
    formatter: ({ cellValue }) => formatTimestampToDate(cellValue),
  },
  {
    title: '操作',
    align: 'center',
    slots: { default: 'action' },
  },
]);
const paginationOptions = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1,
});
const aimarkLabelOptions = ref([]);
getOptData();
async function getOptData() {
  let params = {
    enable: 1,
  };
  const res = await queryCustmizeLabel(params);
  if (res && res.result) {
    aimarkLabelOptions.value = res.data.map((item: any) => {
      return {
        label: item.title,
        value: item.type,
      };
    });
  }
}
async function getList({
  page,
  pageSize,
  ...formValues
}: {
  page: number;
  pageSize: number;
  [key: string]: any;
}) {
  let init: any = {
    page: page - 1,
    limit: pageSize,
    ...formValues,
  };
  if (init.time) {
    init = {
      ...transformTimeFields(init, [['time', ['start_time', 'end_time']]]),
    };
  }

  try {
    const res = await getCVLogList(init);
    if (res && res.result) {
      return {
        items: res.data.records,
        total: res.data.count,
      };
    }
  } catch (error) {
    return {
      items: [],
      total: 0,
    };
  }
}
function getAimarkLabel(val: any) {
  aimarkLabelOptions.value = val;
  aimarkLabelMap.value = val.reduce((obj: any, item: any) => {
    obj[item.value] = item.label;
    return obj;
  }, {});
}
function refresh() {
  docTable.value?.refreshTable();
}
</script>
<template>
  <table-template
    ref="docTable"
    :search-options="searchOptions"
    :columns="columns"
    :pagination-options="paginationOptions"
    :query-method="getList"
  >
    <template #content="{ row }">
      <image-view :src="row.content" class="w-25 h-25"> </image-view>
    </template>
    <template #action="{ row }">
      <update-aimark-btn
        :aimark-label-options="aimarkLabelOptions"
        :scope="row"
        :call-back="refresh"
        :iscv="true"
      ></update-aimark-btn>
    </template>
  </table-template>
</template>
