<script setup lang="ts">
import type { Keyword } from '#/types/task';

import { computed, onUnmounted, ref, watch } from 'vue';

import { IconifyIcon } from '@vben/icons';

import { message } from 'ant-design-vue';

import { getTextPull } from '#/api/ai-mark';
import { getKeywordsWordLabels } from '#/api/operateLog';
import { translateText } from '#/api/tools';
import MarkLabel from '#/components/ai-mark/label-mark.vue';
import Field from '#/components/common/Field.vue';
import CheckTabList from '#/components/task/CheckTabList.vue';
import KeywordsJump from '#/components/task/KeywordsJump.vue';
import ViewBoard from '#/components/task/ViewBoard.vue';
import { useDocumentManager } from '#/composables/ai-mark/use-document-manager';
import { useMarkSubmit } from '#/composables/ai-mark/use-mark-submit';
import { NLP_POST_MODEL_MAP, NLP_PRE_MODEL_MAP } from '#/constants/maps/nlp';
import { useTaskStore } from '#/store/task';

const props = withDefaults(
  defineProps<{
    api?: (data: any) => Promise<any>;
    autoCommit?: boolean;
    enableKeywords?: boolean;
    showLevel?: boolean;
  }>(),
  {
    autoCommit: false,
    api: getTextPull,
    enableKeywords: false,
    showLevel: false,
  },
);

const { submit } = useMarkSubmit();

const entry = defineModel<string>('entry', { required: true });
const taskStore = useTaskStore();
const selectedItem = ref<any | null>(null);
// 关键词显示控制
const toggle = ref(true);
const keywords = ref<Keyword[]>([]);
const keywordsLabels = ref([]);
const showTranslation = ref(false);
const translatedContent = ref('');

// 使用文档管理hook
const {
  documents: data,
  currentIndex: index,
  isLoading,
  loadDocuments: loadData,
  focusDocument: focusIndex,
  getCurrentDocument,
  updateDocumentStatus,
  nextUnprocessedDocument: nextFocus,
  clear: clearDocuments,
} = useDocumentManager(
  entry,
  computed(() => 'Marking'),
  false,
  props.api,
);

// 文档类型定义
interface DocumentItem {
  id?: string;
  content?: string;
  scene?: string;
  external_json?: {
    [key: string]: any;
    forbid_models?: string[];
    keywords?: Keyword[];
    msg_id?: string;
    pass_post_models?: string[];
    used_post_models?: string[];
  };
  [key: string]: any;
}

// 模型映射函数
const modelMap = (models?: string[]) => {
  if (!models || models.length === 0) return '-';
  return models
    .map((key: string) => {
      if (key.includes('post')) {
        return NLP_POST_MODEL_MAP[key] || key;
      }
      return NLP_PRE_MODEL_MAP[key] || key;
    })
    .join(',');
};

// 计算属性获取当前文档
const curItem = computed<DocumentItem>(() => {
  return getCurrentDocument() || {};
});

// 获取关键词标签数据
const getKeywordsLabelData = async () => {
  const labelsResponseData = await getKeywordsWordLabels();
  keywordsLabels.value = labelsResponseData.data.labels;
};
const handleMouseEnter = (item: any) => {
  selectedItem.value = item;
};
const handleMouseLeave = () => {
  selectedItem.value = null;
};
// 监听entry变化，重置为初始未加载状态
watch(
  () => entry.value,
  () => {
    // 清空数据
    clearDocuments();
    // 重置任务计数器
    taskStore.resetCounter();
    // 清空关键词
    keywords.value = [];
    // 重新获取标签数据
    getKeywordsLabelData();
    // 重置翻译状态
    showTranslation.value = false;
    translatedContent.value = '';
  },
);

// 文档选择变更处理
const handleChange = async (taskItem: any) => {
  console.log('taskItem', taskItem);
  console.log('curItem', curItem.value);
  await focusIndex(taskItem.index);
  // 获取当前文档关键词
  const doc = getCurrentDocument();
  keywords.value =
    doc && doc.external_json?.keywords ? doc.external_json?.keywords : [];

  // 重置翻译状态
  showTranslation.value = false;
  translatedContent.value = '';
};

// 处理文档完成的通用逻辑
const handleDocumentCompletion = () => {
  taskStore.incrementFinished();

  // 检查是否所有项目都已处理
  const hasAllResults = data.value.every((item) => !!item.result);
  if (hasAllResults) {
    // 全部处理完成，清空列表
    clearDocuments();
  } else {
    // 自动跳转到下一个未处理的项目
    nextFocus();
  }
};
// 重构后的handleCommit函数
const handleCommit = async () => {
  try {
    const resp = await submit(curItem.value, entry.value);
    if (resp.data.failure !== 0) {
      message.error('标记失败');
      return false;
    }
    if (!props.autoCommit) {
      const doc = getCurrentDocument();
      if (!doc) return;
      updateDocumentStatus(doc.index, {
        buttonType: 'ok',
        forbidText: '已提交',
        type: 3,
        result: true,
      });
      handleDocumentCompletion();
    }
    return true;
  } catch (error: any) {
    console.error('提交失败:', error.message);
    message.error(error.message);
    return false;
  }
};

// 处理标签标记
const handleLabelMark = async (label: any) => {
  if (selectedItem.value) {
    selectedItem.value.label = label;
    selectedItem.value.type = label.type;
  }
  if (props.autoCommit) {
    const doc = getCurrentDocument();
    if (!doc) return;

    // 根据标签类型设置不同的状态
    let buttonType = 'warning';
    if (label.type === 1 || label.type === 4 || label.type === 6) {
      buttonType = 'forbid';
    } else if (label.type === 3) {
      buttonType = 'ok';
    }
    updateDocumentStatus(doc.index, {
      buttonType,
      forbidText: label.title,
      type: label.type,
      result: true,
    });
    const resp = await handleCommit();
    if (resp) {
      handleDocumentCompletion();
    }
  }
};

// 点击关键词处理函数
const clickKeyword = (keyword: Keyword) => {
  console.log('点击关键词:', keyword);
  // 这里可以实现关键词点击的高亮或定位逻辑
};

// 翻译内容
const toggleTranslation = async () => {
  if (!showTranslation.value && !translatedContent.value) {
    try {
      // 显示翻译中状态
      translatedContent.value = '翻译中...';
      showTranslation.value = true;
      const originalContent = curItem.value.content || '';
      // 调用实际的翻译API
      const response = await translateText({
        content: originalContent,
        to: 'zh',
        service: 'aigc_translate',
      });

      // 从API响应中获取翻译结果
      translatedContent.value = response.data.content || '翻译失败';
    } catch (error) {
      console.error('翻译请求失败:', error);
      translatedContent.value = '翻译失败，请重试';
    } finally {
      showTranslation.value = false;
    }
  }

  showTranslation.value = !showTranslation.value;
};

// 组件卸载时清理资源
onUnmounted(() => {
  clearDocuments();
});

// 组件初始化时获取标签数据
getKeywordsLabelData();
</script>

<template>
  <div class="h-full max-h-[calc(100vh-100px)] w-full">
    <view-board
      :disabled-fields="['forbid', 'preview', 'date', 'checkLog', 'supplier']"
      empty-text="复审未加载"
    />

    <div class="flex h-[calc(100%-45px)] w-full items-center justify-center">
      <a-button
        v-if="data.length === 0 && !isLoading"
        type="link"
        @click="loadData"
      >
        加载复审~
      </a-button>

      <div
        v-else
        class="relative flex h-full w-full flex-col"
        v-loading="isLoading"
      >
        <!-- 上方内容区域：包含左侧关键词、中间内容和右侧人工操作结果 -->
        <div class="m-2 flex h-[calc(100%-60px)] w-full border">
          <!-- 左侧关键词列表 -->
          <div
            v-if="toggle && enableKeywords"
            class="relative z-10 h-full max-h-full w-[200px] shrink-0 overflow-auto border-r border-t"
          >
            <iconify-icon
              class="absolute right-1 top-3 cursor-pointer"
              icon="ant-design:left-outlined"
              @click="toggle = !toggle"
            />
            <keywords-jump
              :is-loading="isLoading"
              :keywords="keywords"
              :labels="keywordsLabels"
              title="关键词列表"
              @click="clickKeyword"
            />
            <div class="mt-2 border-t pt-2">
              <div class="mb-2 text-center font-medium">NLP列表</div>
              <div class="px-2">
                <div v-if="curItem.external_json" class="mb-1">
                  <field
                    :value="modelMap(curItem.external_json.forbid_models)"
                    label="前置模型命中"
                  />
                </div>
                <div v-if="curItem.external_json" class="mb-1">
                  <field
                    :value="modelMap(curItem.external_json.used_post_models)"
                    label="后置模型命中"
                  />
                </div>
                <div v-if="curItem.external_json" class="mb-1">
                  <field
                    :value="modelMap(curItem.external_json.pass_post_models)"
                    label="后置模型放行"
                  />
                </div>
              </div>
            </div>
          </div>
          <a-button
            v-if="!toggle && enableKeywords"
            class="absolute left-0 top-3 z-10 rounded-l-none border-l-0 p-0 px-1"
            @click="toggle = !toggle"
          >
            ≡
          </a-button>

          <!-- 中间内容展示区域 -->
          <div class="flex flex-1 flex-col items-center overflow-auto">
            <slot
              :content="curItem.content"
              :handle-mouse-enter="handleMouseEnter"
              :handle-mouse-leave="handleMouseLeave"
              :is-loading="isLoading"
              :item="curItem"
              :msg-id="curItem.external_json?.msg_id"
              :scene="curItem.scene"
              :selected-item="selectedItem"
              :show-translation="showTranslation"
              :toggle-translation="toggleTranslation"
              :translated-content="translatedContent"
            ></slot>
          </div>

          <!-- 右侧审核区域 -->
          <div class="flex max-w-[300px] flex-col items-center">
            <a-card
              class="h-full w-full overflow-auto !rounded-none"
              size="small"
            >
              <template #title>
                <div class="text-xl font-medium">人工操作结果</div>
              </template>

              <!-- 使用动态标签组件 -->
              <mark-label
                :disabled="curItem.result"
                :entry="entry"
                :show-level="showLevel"
                :type="props.autoCommit ? 'button' : 'tag'"
                @mark="handleLabelMark"
              />

              <div
                v-if="props.autoCommit"
                class="mt-4 text-center text-red-500"
              >
                <!-- <p>点击即确认，请谨慎操作！</p> -->
              </div>
              <div v-else class="x mt-4 border-t pt-2 text-center text-red-500">
                <a-button class="w-full" type="primary" @click="handleCommit">
                  提交
                </a-button>
              </div>
            </a-card>
          </div>
        </div>

        <!-- 下方Tab切换区域 -->
        <div class="mb-2 w-full px-4">
          <check-tab-list
            v-model:index="index"
            :options="data"
            @change="handleChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>
