<script lang="ts" setup>
import { ApiComponent } from '@vben/common-ui';
import { Select } from 'ant-design-vue';
import { defineProps, defineEmits, watch, ref } from 'vue';
import { getTaskConfig } from '#/api/platform';


const props = defineProps({
  biz: {
    type: [String, Number],
    default: '',
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  immediate: {
    type: Boolean,
    default: false,
  },
});


const modelValue = defineModel<string | number | any[]|undefined>();
const innerValue = ref<string | number | any[]>(modelValue.value || props.multiple ? [] : "");
watch(modelValue, (newVal) => {
  console.log('newVal', newVal);
  if(props.multiple && Array.isArray(newVal)){
    innerValue.value = newVal;
  }else{
    innerValue.value = newVal || "";
  }
}, { immediate: true });
async function fetchApi() {
  console.log('props.biz', props.biz,modelValue.value,innerValue.value);
  if (!props.biz) return []; // 新增校验
   
  try {
    const res = await getTaskConfig({ biz: props.biz });
    const configs = res.data.configs as Array<{
      task_desc: string;
      task_name: string;
    }>;
    return generateOptions(configs);
  } catch (error) {
    console.error('获取队列配置失败:', error);
    return [];
  }
}

function generateOptions(
  configs: Array<{ task_desc: string; task_name: string }>,
) {
  return configs.map((item) => ({
    label: `${item.task_desc}(${item.task_name})`,
    value: item.task_name,
    disabled: false,
  }));
}

function handleChange(value: any) {
  console.log('handleChange', value);
  modelValue.value = value;
}
</script>

<template>
  <api-component
    v-model:model-value="innerValue"
    :key="biz"
    :api="fetchApi"
    :component="Select"
    model-prop-name="value"
    :immediate="immediate"
    :mode="multiple ? 'multiple' : undefined"
    @change="handleChange"
    :disabled="disabled"
    allowClear
    showSearch
  />
</template>
