<script setup lang="ts">
import { ref, watch, nextTick } from 'vue';
import { FORBID_FIELD_CONFIGS } from '#/constants/options';

/**
 * 违规类型选择组件
 * @description 提供随机抽取和按比例抽取两种模式
 * @example
 * <forbid-type v-model:config="config" @change="handleChange" />
 */

// 定义配置接口
interface ForbidTypeConfig {
  type: number; // 1: 随机抽取, 2: 按比例抽取
  randomPercent: number; // 随机抽取的比例
  percentages: Record<string, number>; // 存储各字段百分比值
}

// 定义组件属性
defineProps({
  /** 默认选中的类型值 */
  defaultType: {
    type: Number,
    default: 1
  },
  /** 是否作为表单字段 */
  isFormField: {
    type: Boolean,
    default: false
  }
});

// 定义双向绑定的配置对象
const config = defineModel<ForbidTypeConfig|string|number>('modelValue', {
  default: () => ({
    type: 1,
    randomPercent: 0,
    percentages: FORBID_FIELD_CONFIGS.reduce((acc, { field }) => {
      acc[field] = 0;
      return acc;
    }, {} as Record<string, number>)
  })
});

// 创建默认配置函数
const createDefaultConfig = (): ForbidTypeConfig => ({
  type: 1,
  randomPercent: 0,
  percentages: FORBID_FIELD_CONFIGS.reduce((acc, { field }) => {
    acc[field] = 0;
    return acc;
  }, {} as Record<string, number>)
});

// 内部配置状态，用于隔离表单系统的直接操作
const innerConfig = ref<ForbidTypeConfig>(createDefaultConfig());

// 检查是否为有效的配置对象
const isValidConfigObject = (value: any): value is ForbidTypeConfig => {
  return (
    typeof value === 'object' && 
    value !== null && 
    typeof value.type === 'number' &&
    (value.type === 1 || value.type === 2) &&
    typeof value.randomPercent === 'number' &&
    typeof value.percentages === 'object' &&
    value.percentages !== null
  );
};

// 初始化时从modelValue接收数据
const initFromModelValue = () => {
  if (isValidConfigObject(config.value)) {
    innerConfig.value = { ...config.value };
    console.log('ForbidType组件 - 从modelValue初始化:', innerConfig.value);
  } else {
    innerConfig.value = createDefaultConfig();
    console.log('ForbidType组件 - 使用默认配置:', innerConfig.value);
  }
};

// 组件挂载时初始化
initFromModelValue();

// 监听modelValue变化，区分有效对象数据和表单系统干扰数据
watch(config, (newValue) => {
  console.log('ForbidType组件 - modelValue变化:', newValue, '类型:', typeof newValue);
  
  if (isValidConfigObject(newValue)) {
    // 是有效的配置对象，同步到innerConfig
    console.log('ForbidType组件 - 检测到有效配置对象，同步到内部状态');
    innerConfig.value = { ...newValue };
  } else if (typeof newValue === 'string' || typeof newValue === 'number') {
    // 是字符串或数字，说明是表单系统的输入框绑定值，忽略
    console.log('ForbidType组件 - 检测到表单系统绑定值，忽略同步:', newValue);
  } else {
    // 其他情况，可能是null或undefined，使用默认配置
    console.log('ForbidType组件 - 检测到无效值，重置为默认配置');
    innerConfig.value = createDefaultConfig();
  }
  // innerConfig.value = createDefaultConfig();
}, { deep: true });

// 定义事件
const emit = defineEmits(['change', 'update:modelValue']);

// 处理类型变更 - 在输入完成后同步内部状态到modelValue
const handleTypeChange = () => {
  nextTick(() => {
    console.log('ForbidType组件 - 类型变更:', innerConfig.value);
    // 深拷贝避免引用问题
    const newValue = JSON.parse(JSON.stringify(innerConfig.value));
    config.value = newValue;
    emit('change', innerConfig.value);
    // 触发表单验证
    emit('update:modelValue', newValue);
  });
};

// 处理百分比变更 - 在输入完成后同步内部状态到modelValue
const handlePercentChange = () => {
  nextTick(() => {
    console.log('ForbidType组件 - 百分比变更:', innerConfig.value);
    // 深拷贝避免引用问题
    const newValue = JSON.parse(JSON.stringify(innerConfig.value));
    config.value = newValue;
    console.log('ForbidType组件 - 百分比变更1:', config.value);
    emit('change', innerConfig.value);
    // 触发表单验证
    emit('update:modelValue', newValue);
  });
};

// 验证数字输入
const validateNumberInput = (event: KeyboardEvent) => {
  return /[\d\.]/.test(String.fromCharCode(event.keyCode));
};
</script>

<template>
  <div class="flex flex-col gap-4" :class="{ '-mt-[5px]': innerConfig.type === 1 }">
    <!-- 使用 a-form-item-rest 包装内部控件，避免被父表单收集 -->
    <a-form-item-rest>
      <div class="flex items-center">
        <div class="flex-shrink-0 mr-4">
          <a-radio-group v-model:value="innerConfig.type" @change="handleTypeChange">
            <a-radio :value="1">随机抽取</a-radio>
            <a-radio :value="2">按比例抽取</a-radio>
          </a-radio-group>
        </div>

        <div v-if="innerConfig.type === 1" class="flex items-center">
          <span class="whitespace-nowrap mr-2">比例：</span>
          <a-input-number
            v-model:value="innerConfig.randomPercent"
            :min="0"
            :max="100"
            :precision="1"
            :controls="false"
            class="w-20"
            @keypress="validateNumberInput"
            @change="handlePercentChange"
          />
          <span class="ml-1">%</span>
        </div>
      </div>
    </a-form-item-rest>

    <a-form-item-rest>
      <div v-if="innerConfig.type === 2" class="grid grid-cols-2 gap-4">
        <div v-for="item in FORBID_FIELD_CONFIGS" :key="item.field" class="flex items-center">
          <span class="whitespace-nowrap mr-2">{{ item.label }}：</span>
          <a-input-number
            v-model:value="innerConfig.percentages[item.field]"
            :min="0"
            :max="100"
            :precision="1"
            :controls="false"
            class="flex-1"
            @keypress="validateNumberInput"
            @change="handlePercentChange"
          />
          <span class="ml-1">%</span>
        </div>
      </div>
    </a-form-item-rest>
  </div>
</template>
