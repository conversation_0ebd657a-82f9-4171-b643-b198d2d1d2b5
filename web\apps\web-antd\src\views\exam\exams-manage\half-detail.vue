<script setup>
import { ref, reactive, computed, watch, h, onMounted } from 'vue';
import { getItem, setItem, removeItem } from '#/utils/save';
import StandardForm from '#/components/form/index.vue';
import { CloseOutlined, ConsoleSqlOutlined } from '@ant-design/icons-vue';
import { Cascader, message } from 'ant-design-vue';
import {
  queryCart,
  updateCart,
  createPaper,
  queryPaperList,
  updatePaper,
  queryQuestionList,
  queryPaperDetail,
} from '#/api/exam';
import dayjs from 'dayjs';
import TableTemplate from '#/components/table/index.vue';
import { getAccountBriefList } from '#/api/account';
import Question from '../components/question.vue';
import { useRouter } from 'vue-router';
import { useApiHandler } from '#/composables/common/use-api-handler';
import { useSupplier } from '#/composables/common/use-supplier';
import { TYPES } from '#/constants/maps/exam';
import { useUserStore } from '#/store';
import { formatTimestampToDate } from '#/utils/time';

const { loadings, handleApiCall } = useApiHandler();
const { supplierData, fetchData } = useSupplier();

const props = defineProps({
  visible: {
    type: Boolean,
    required: true,
  },
  examId: {
    type: Number,
  },
  paperId: {
    type: Number,
  },

  questionToCartNum: {
    type: Number,
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
  isCopy: {
    type: Boolean,
    default: false,
  },
  isDelete: {
    type: Boolean,
    default: false,
  },
  isCart: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits([
  'update:questionToCartNum',
  'update:visible',
  'update:isEdit',
  'update:isCopy',
  'update:isDelete',
]);

const userStore = useUserStore();
const router = useRouter();
const open = ref(props.visible);
const createLoading = ref(false);
const updateLoading = ref(false);
const infoLoading = ref(false);
const typeLoading = ref(false);
const question = ref({});
const readOnly = ref(true);
const modalOpen = ref(false);
const examId = ref(Number(props.examId));

const num = ref(props.questionToCartNum || 0);

const userId = ref(Number(getItem('user_id')));

const loading = ref(false);
const paperInfo = ref({});
const resitList = ref([]);
const questionsInCart = ref([]);
const dateValues = ref([]);
const examScore = ref(null);
const typeOptions = [
  {
    value: 1,
    label: '初试',
  },
  {
    value: 2,
    label: '补考',
  },
];

const examinees = ref([]);
const testPeople = ref([]);
const modelVisible = ref(false);

const accountList = ref([]); // 确保初始化为空数组

const types = ref([]);
types.value = Object.entries(TYPES)
  .filter(([key]) => key !== '0')
  .map(([key, value]) => ({
    name: `${value}题`,
    question_type: parseInt(key),
    typeScore: '10',
    cartData: [],
  }));
const infoForm = ref(null);
const schema = [
  {
    component: 'Select',
    componentProps: {
      options: typeOptions,
      placeholder: '请选择',
      style: { width: '100%' },
      disabled: props.isEdit,
    },
    fieldName: 'paper_type',
    label: '笔试类型：',
    labelClass: 'mr-5',
    rules: 'selectRequired',
  },
  {
    component: 'Input',
    fieldName: 'title',
    label: '笔试标题：',
    labelClass: 'mr-5',
    rules: 'required',
  },
  {
    component: 'InputNumber',
    componentProps: ({ values = {} }) => ({
      placeholder: '以分钟为单位',
      style: { width: '100%' },
      min: 0,
      // 当 props.isEdit 为 true，或者当前时间已经超过有效期结束（values.validity[1]）时，禁用
      disabled:
        props.isEdit ||
        (values.validity && Array.isArray(values.validity) && values.validity[1] &&
         dayjs().isAfter(dayjs(+values.validity[1]), 'millisecond')),
    }),
    fieldName: 'duration',
    label: '笔试时长：',
    labelClass: 'mr-5',
    rules: 'required',
    dependencies: {
      triggerFields: ['duration', 'validity'],
      trigger(values = {}, formApi) {
        if (values.validity) {
          updateDateValues(values.validity, values.duration);
        }
      },
    },
  },
  {
    component: 'RangePicker',
    componentProps: {
      showTime: {
        format: 'HH:mm:ss', defaultValue: [
          dayjs('00:00:00', 'HH:mm:ss'),
          dayjs('00:00:00', 'HH:mm:ss'),
        ],
      },   // 显示时分
      format: 'YYYY-MM-DD HH:mm:ss',      // 显示格式
      valueFormat: 'x',                // 绑定值的格式：时间戳
      placeholder: ['开始时间', '结束时间'],
      style: { width: '100%' },

      disabledDate: (current) => {
        // 禁止选择今天之前的日期
        // 注意：current 是 moment 对象
        return current && current < dayjs().startOf('day');
      },
    },
    fieldName: 'validity',
    label: '笔试有效期：',
    rules: 'required',
    dependencies: {
      triggerFields: ['duration', 'validity'],
      trigger(values = {}, formApi) {
        if (values.validity) {
          if (values.duration) {
            updateDateValues(values.validity, values.duration);
          }
        }
      },
    },
  },
  {
    component: 'Input',
    componentProps: {
      disabled: true,
    },
    fieldName: 'creator',
    label: '笔试创建人：',
    rules: 'required',
  },
  {
    component: 'Cascader',
    componentProps: ({ values = {} }) => ({
      placeholder: '请选择',
      style: { width: '100%' },
      multiple: true,
      options: accountList.value || [], // 确保 options 存在且为数组
      showCheckedStrategy: Cascader.SHOW_CHILD,
      maxTagCount: 'responsive',
      showSearch: true,
      // 当 props.isEdit 为 true，或者当前时间已经超过有效期结束（values.validity[1]）时，禁用
      disabled:
        props.isEdit ||
        (values.validity && Array.isArray(values.validity) && values.validity[1] &&
         dayjs().isAfter(dayjs(+values.validity[1]), 'millisecond')),
    }),
    fieldName: 'examinees',
    label: '笔试人员：',
    labelClass: 'mr-5',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      options: resitList,
      placeholder: '请选择',
      style: { width: '100%' },
    },
    fieldName: 'resit_id',
    label: '补考场次：',
    labelClass: 'mr-5',
    formItemClass: 'w-full',
    dependencies: {
      if(values) {
        return values.paper_type === 2;
      },
      triggerFields: ['paper_type'],
    },
    rules: 'selectRequired',
  },
  {
    component: 'Input',
    fieldName: 'resit_score',
    label: '补考分数：',
    labelClass: 'mr-5',

    dependencies: {
      if(values) {
        return values.paper_type === 2;
      },
      triggerFields: ['paper_type'],
    },
    rules: 'required',
  },
];
const wrapperClass = 'grid grid-cols-[1fr_1.5fr] gap-4';
const commonConfig = {
  labelClass: 'w-auto',
  formItemClass: 'p-0 w-full ',
};

const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 60,
    align: 'center',
  },
  {
    title: '题目名称',
    align: 'center',
    dataIndex: 'title',
    ellipsis: true,
  },
  {
    title: '分数',
    dataIndex: 'score',
    align: 'center',
  },
  {
    title: '操作',
    align: 'center',
    dataIndex: 'operation',
  },
];
const pagerConfig = {
  enabled: false,
};
const totalScore = computed(() => {
  let sum = 0;
  types.value.forEach((type) => {
    type.cartData.forEach((question) => {
      if (isNaN(Number(question.score))) {
        question.score = 0;
      }
      sum += Number(question.score); // 转换为数字
    });
  });

  return sum;
});
const totalNum = computed(() => {
  let sum = 0;
  types.value.forEach((type) => {
    sum += type.cartData.length;
  });
  return sum;
});
const scoreOptions = computed(() => {
  return Array.from({ length: 100 }, (_, index) => ({
    label: index + 1,
    value: index + 1,
  }));
});
watch(
  () => props.visible,
  (newVal) => {
    open.value = newVal;
    if (newVal) {
      initData();
      queryPaperListData();
    }
  },
);
watch(
  () => props.questionToCartNum,
  (newVal) => {
    num.value = newVal;
  },
);

watch(open, () => {
  if (!open.value) emit('update:visible', open.value);
});

onMounted(() => {
  initData();
  queryPaperListData();
});
function setCreator() {
  paperInfo.value.creator = userStore.realname
    ? userStore.realname
    : userStore.username;
}

watch(questionsInCart, (newVal) => {
  if (props.paperId) {
    setItem(`questionsInCart-Copy${props.paperId}`, newVal);
  }
});
async function initData() {
  typeLoading.value = true;
  await getAccountBriefListData(); //参与人员列表

  if (props.isEdit) {
    //编辑

    await queryPaperInfo();
  } else if (!props.isCart && props.paperId) {
    //复制
    await queryPaperInfo();
    setCreator();
    infoForm.value?.formApi.setFieldValue('creator', paperInfo.value.creator);

    const localValue = getItem(`questionsInCart-Copy${props.paperId}`);

    if (localValue) {
      questionsInCart.value = localValue;
      updateTypeData(questionsInCart.value);
      typeLoading.value = false;
      return;
    }

    await updateCart({ content: paperInfo.value.content.question });
    emit('update:isDelete', true); //实际上是对页面进行一下刷新
    num.value = paperInfo.value.content.question.length;
    emit('update:questionToCartNum', num.value);

    await queryCartQuestions();
    setItem(`questionsInCart-Copy${props.paperId}`, questionsInCart.value);
  } else if (props.isCart) {
    //购物车

    if (props.paperId) {
      //从复制页面跳转过来，然后叉掉半屏弹窗到创建试题页面，然后再从创建试题页面到半屏弹窗页面
      await queryPaperInfo();
    }
    setCreator();
    infoForm.value?.formApi.setFieldValue('creator', paperInfo.value.creator);
    await queryCartQuestions();
    if (questionsInCart.value) {
      updateTypeData(questionsInCart.value);
    }
  }

  typeLoading.value = false;
}
async function getAccountBriefListData() {
  try {
    await fetchData();

    // 确保 supplierData 存在且为数组
    if (!supplierData.value || !Array.isArray(supplierData.value)) {
      accountList.value = [];
      return;
    }

    accountList.value = supplierData.value.map((item) => {
      return {
        value: item.supplier,
        label: item.mark,
        children: [], // 确保 children 始终是数组
      };
    });

    //参与人员列表
    const res = await getAccountBriefList({
      page: 0,
      limit: 1000,
    });

    if (res && res.result && res.data && res.data.data && Array.isArray(res.data.data)) {
      accountList.value.forEach((item) => {
        // 确保 children 数组存在
        if (!Array.isArray(item.children)) {
          item.children = [];
        }

        res.data.data.forEach((data) => {
          if (data.supplier === item.value) {
            item.children.push({
              value: data.id,
              label: data.real_name ? data.real_name : data.uname,
            });
          }
        });
      });
    }
  } catch (error) {
    console.error('获取账户列表失败:', error);
    accountList.value = []; // 出错时设置为空数组
  }
}

async function queryPaperInfo() {
  loading.value = true;
  const res = await queryPaperDetail({ id: Number(props.paperId) });
  if (res && res.result) {
    paperInfo.value = res.data;
    paperInfo.value.duration = paperInfo.value.duration / 60;
    handlePaperInfo();
    updateTypeData(paperInfo.value.content.question);
  }
  loading.value = false;
}

async function queryPaperListData() {
  //补考场次列表
  examId.value = Number(props.examId);
  loading.value = true;
  const res = await queryPaperList({
    exam_id: examId.value,
    page: 0,
    limit: 10,
  });
  if (res && res.result) {
    resitList.value = res.data.records.map((record) => {
      return {
        value: record.id,
        label: record.title,
      };
    });
    loading.value = false;
  }
}

async function queryCartQuestions() {
  loading.value = true; // 开始加载数据
  const res = await queryCart();
  loading.value = false;
  if (res && res.result) {
    if (res.data.content) questionsInCart.value = res.data.content;
    questionsInCart.value.forEach((item) => (item.score = 10));
  }
}

function handlePaperInfo() {
  examScore.value = paperInfo.value.score;

  paperInfo.value.examinees = paperInfo.value.examinees || [];

  if (paperInfo.value.resit_id === 0) {
    paperInfo.value.resit_id = null;
  }
  if (paperInfo.value.resit_score === 0) {
    paperInfo.value.resit_score = null;
  }
  dateValues.value = [
    paperInfo.value.start_time * 1000,
    paperInfo.value.end_time * 1000,
  ];

  infoForm.value?.formApi.setFieldValue('validity', dateValues.value);
  infoForm.value?.formApi.setValues(paperInfo.value);
}
function updateTypeData(questions) {
  types.value.forEach((type) => {
    type.cartData = [];
  });
  questions.forEach((question) => {
    const type = types.value.find(
      (typeItem) => typeItem.question_type === question.question_type,
    );

    if (question.score === 0)
      question.score = 10; //新创建的试卷每个题目默认10分
    else if (question.score !== 10) type.typeScore = '';
    if (type) {
      type.cartData.push(question);
    }
  });
}
function closeDetailDrawer() {
  emit('update:visible', false);
  emit('update:isEdit', false);
  emit('update:isCopy', false);
}
function warning() {
  message.warning('请将考试信息填写完整');
}
function hasScoreZero() {
  return types.value?.some((type) =>
    type.cartData.some((item) => item.score === '0' || item.score === ''),
  );
}
function validatePaperInfo() {
  if (paperInfo.value.paper_type === 2) {
    const hasRetake = /补考/.test(paperInfo.value.title);
    if (!hasRetake) paperInfo.value.title += '(补考)';
  }

  if (
    paperInfo.value.end_time - paperInfo.value.start_time <
    paperInfo.value.duration * 60
  ) {
    message.warning('试卷有效期时间范围需不小于笔试时长');
    return false;
  }

  if (!props.isEdit) {
    if (!examScore.value) {
      examScore.value = totalScore.value;
    }

    if (totalScore.value !== Number(examScore.value)) {
      message.warning('当前试卷总分与设定的卷面分值不一致');
      return false;
    }

    if (hasScoreZero()) {
      message.warning('请设置试题的分数');
      return false;
    }
  }

  return true;
}
async function createPaperTest() {
  const validForm = await infoForm.value?.formApi.validate();
  const isValid = validatePaperInfo();
  if (!validForm.valid || !isValid) {
    return;
  }

  const contentArray = { question: [] };

  types.value.forEach((type) => {
    type.cartData.forEach((item) => {
      item.score = Number(item.score);
    });
    contentArray.question.push(...type.cartData);
  });

  const { paper_type, duration, title, start_time, end_time } = paperInfo.value;
  const createPaperParams = {
    exam_id: examId.value,
    paper_type,
    duration: duration * 60,
    title,
    content: contentArray,
    start_time,
    end_time,
    score: examScore.value,
  };

  if (paperInfo.value.paper_type === 1) {
    createPaperParams.examinees = paperInfo.value.examinees;
  } else if (paperInfo.value.paper_type === 2) {
    createPaperParams.resit_id = paperInfo.value.resit_id;
    createPaperParams.resit_score = Number(paperInfo.value.resit_score);
  }

  await handleApiCall(createPaper, createPaperParams, {
    loadingKey: 'create',
    onSuccess: () => {
      closeHalfScreen();
      goToExamListPage();
      const localValue = getItem(`questionsInCart-Copy${props.paperId}`);
      if (localValue) removeItem(`questionsInCart-Copy${props.paperId}`);
    },
  });
}

async function updatePaperTest() {
  const validForm = await infoForm.value?.formApi.validate();
  const isValid = validatePaperInfo();
  if (!validForm.valid || !isValid) {
    return;
  }
  const { title, examinees, start_time, end_time } = paperInfo.value;
  await handleApiCall(
    updatePaper,
    {
      id: Number(props.paperId),
      title,
      examinees,
      start_time,
      end_time,
    },
    {
      loadingKey: 'update',
      onSuccess: () => {
        closeHalfScreen();
        // goToExamListPage();
      },
    },
  );
}
function closeHalfScreen() {
  emit('update:visible', false);
  emit('update:isEdit', false);
  emit('update:isCopy', false);
}
function goToExamListPage() {
  router.push({
    path: '/exam/exam_manage/exam_list',
    query: { exam_id: examId },
  });
}

async function clearAll() {
  num.value = 0;
  emit('update:questionToCartNum', num.value);
  types.value.forEach((item) => {
    item.cartData = [];
  });

  if (!props.isEdit && !props.isCopy) {
    questionsInCart.value = [];
    await updateCartData();
    emit('update:isDelete', true);
  }
}

async function deleteCurQues(index, id) {
  types.value[index].cartData = types.value[index].cartData.filter(
    (item) => item.question_id !== id,
  );

  if (!props.isEdit) {
    num.value -= 1;
    emit('update:questionToCartNum', num.value);
    questionsInCart.value = questionsInCart.value.filter(
      (item) => item.question_id !== id,
    );

    await updateCartData();
    emit('update:isDelete', true);
  }
}

async function updateCartData() {
  loading.value = true; // 开始加载数据
  const res = await updateCart({
    content: questionsInCart.value,
  });
  loading.value = false;
}

async function updateTypeScore(typeScore, type) {
  const typeItem = types.value.find((t) => t.name === type.name);

  if (typeItem) {
    typeItem.cartData.forEach((item) => (item.score = typeScore));
  }
}

function handleValuesChange(values) {
  Object.assign(paperInfo.value, values);
  if (values.validity) {
    paperInfo.value.start_time = Math.floor(values.validity[0] / 1000);
    paperInfo.value.end_time = Math.floor(values.validity[1] / 1000);
  }
}

function updateDateValues(values, duration) {
  if (values[1] - values[0] < duration * 60 * 1000) {
    const startTime = Number(values[0]);
    const endTime = startTime + duration * 60 * 1000;
    values[1] = endTime;
    infoForm.value?.formApi.setFieldValue('validity', values);
  }
}
async function clickInfoBtn(id) {
  if (props.isEdit || props.isCopy) {
    question.value = paperInfo.value.questions.find((item) => item.id === id);
  } else {
    //购物车
    await queryQuestionData(id);
  }
  modelVisible.value = true;
}

async function queryQuestionData(id) {
  infoLoading.value = true; // 开始加载数据
  const res = await queryQuestionList({
    id: id,
    page: 0,
    limit: 10,
  });

  if (res && res.result) {
    question.value = res.data.records[0];
  }

  infoLoading.value = false;
}
</script>
<template>
  <div class="detail-drawer">
    <a-drawer
      title="试卷栏"
      v-model:open="open"
      width="50%"
      :maskClosable="false"
      :bodyStyle="{ overflow: 'hidden' }"
    >
      <template #extra>
        <a-button
          v-if="props.isEdit"
          type="primary"
          @click="updatePaperTest"
          :loading="loadings.update"
          >更新试卷</a-button
        >
        <a-button
          v-else
          type="primary"
          @click="createPaperTest"
          :loading="loadings.create"
          >创建笔试</a-button
        >
      </template>

      <div class="font-semibold">考试信息</div>

      <standard-form
        ref="infoForm"
        class="mx-6 pt-0"
        :schema="schema"
        :showDefaultActions="false"
        :wrapper-class="wrapperClass"
        :common-config="commonConfig"
        :handle-values-change="handleValuesChange"
      ></standard-form>

      <div class="my-6 font-semibold">试题信息</div>
      <div style="height: 75%">
        <div class="mx-6 flex items-center justify-between pb-6">
          <div class="flex items-center text-[#ff3860]">
            <div>卷面分值：</div>
            <a-input-number
              size="small"
              v-model:value="examScore"
              :disabled="props.isEdit"
              style="width: 70px"
              :min="0"
            />
            <span class="ml-3"
              >当前分数：{{ totalScore + '分（共' + totalNum + '题）' }}</span
            >
          </div>
          <a-button danger @click="clearAll" :disabled="props.isEdit"
            >一键清空</a-button
          >
        </div>
        <div v-if="typeLoading" class="pt-3 text-center">
          <a-spin :spin="typeLoading" tip="信息加载中..."></a-spin>
        </div>

        <div v-else style="height: 90%" class="overflow-y-auto">
          <template v-for="(type, cardIndex) in types" :key="cardIndex">
            <a-card
              v-if="type.cartData.length"
              :title="type.name"
              :headStyle="{ fontSize: '14px', fontWeight: '500' }"
              class="mb-4 ml-6 mr-2"
            >
              <template #extra>
                <div v-if="!props.isEdit">
                  <span>题型分数：</span>
                  <a-select
                    size="small"
                    class="w-16"
                    v-model:value="type.typeScore"
                    :options="scoreOptions"
                    :filter-option="true"
                    show-search
                    @change="updateTypeScore(type.typeScore, type)"
                  ></a-select>
                </div>
              </template>
              <a-table
                :borderd="false"
                :data-source="type.cartData"
                :columns="columns"
                :pagination="false"
              >
                <template #bodyCell="{ column, text, record, index }">
                  <template v-if="column.dataIndex === 'index'">
                    {{ index + 1 }}
                  </template>
                  <template v-if="column.dataIndex === 'title'">
                    <a-tooltip placement="topLeft" :title="record.title.msg">
                      <span>
                        {{ record.title.msg }}
                      </span>
                    </a-tooltip>
                  </template>
                  <template v-if="column.dataIndex === 'score'">
                    <a-input
                      class="w-12"
                      size="small"
                      v-model:value="record.score"
                      :disabled="isEdit"
                    ></a-input>
                  </template>
                  <template v-if="column.dataIndex === 'operation'">
                    <a-popconfirm
                      title="确定删除吗?"
                      ok-text="确定"
                      cancel-text="取消"
                      @confirm="deleteCurQues(cardIndex, record.question_id)"
                    >
                      <a-button type="link" :danger="true">删除</a-button>
                    </a-popconfirm>
                    <a-button
                      type="link"
                      :loading="infoLoading"
                      @click="clickInfoBtn(record.question_id)"
                    >
                      详情
                    </a-button>
                  </template>
                </template>
              </a-table>
            </a-card>
          </template>
        </div>
      </div>
    </a-drawer>
    <a-modal
      title=""
      v-model:open="modelVisible"
      width="70%"
      centered
      :footer="null"
      style="min-width: 1200px"
    >
      <Question
        :questionCard="question"
        :readOnly="true"
        :isShowInfo="true"
      ></Question>
    </a-modal>
  </div>
</template>
