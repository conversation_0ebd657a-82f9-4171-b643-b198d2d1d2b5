<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { Tooltip as ATooltip } from 'ant-design-vue';
import { IconifyIcon } from '@vben/icons';

// 定义图片显示模式枚举
enum Mode {
  ORIGINAL = 'original',
  FIT = 'fit',
}
const modeIcons = {
  [Mode.FIT]: 'ant-design:fullscreen-exit-outlined',
  [Mode.ORIGINAL]: 'ant-design:fullscreen-outlined'
};

const props = withDefaults(
  defineProps<{
    imgSrc: string;
    offsetX?: number;
    offsetY?: number;
    currentIndex?: number;
    totalImages?: number;
    imageList?: string[];
    showOverlay?: boolean;
    overlayColor?: string;
  }>(),
  {
    offsetX: 0,
    offsetY: 0,
    currentIndex: undefined,
    totalImages: undefined,
    imageList: undefined,
    showOverlay: false,
    overlayColor: 'rgba(0, 0, 0, 0.8)',
  },
);

// ==================== 定义 emits ====================
const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'afterTransform', payload: {
    curWidth: number;
    curHeight: number;
    deg: number;
    scaleX: number;
    scaleY: number;
    offsetX: number;
    offsetY: number;
    imgWidth: number;
    imgHeight: number;
  }): void;
  (e: 'containerRectChange', size: { width: number; height: number }): void;
  (e: 'imgLoad', size: { width: number; height: number }): void;
  (e: 'prevImage'): void;
  (e: 'nextImage'): void;
}>();

// 容器引用
const containerRef = ref<HTMLElement | null>(null);

// 图片原始尺寸
const imageWidth = ref(0);
const imageHeight = ref(0);
const loading = ref(false);

// 变换状态（旋转、翻转、缩放、偏移）
const transform = ref({
  scale: 1,
  deg: 0,
  flipX: 1,
  flipY: 1,
  offsetX: 0,
  offsetY: 0,
  enableTransition: false,
});

// 当前显示模式
const mode = ref(Mode.FIT);

// =================== 计算属性 ===================
/**
 * 计算属性：获取当前应该显示的图片URL
 * 优先使用imageList中对应索引的图片，如果不存在则使用imgSrc
 */
const currentImageSrc = computed(() => {
  if (props.currentIndex !== undefined && props.imageList && props.imageList[props.currentIndex]) {
    return props.imageList[props.currentIndex];
  }
  return props.imgSrc;
});

const transformStyle = computed(() => {
  const t = transform.value;
  return {
    transform: `
      translate(${t.offsetX}px, ${t.offsetY}px)
      rotate(${t.deg}deg)
      scale(${t.scale})
      scaleX(${t.flipX})
      scaleY(${t.flipY})
    `,
    transition: t.enableTransition ? 'transform 0.3s' : '',
  };
});

/**
 * 计算属性：是否可以切换到上一张图片
 * @returns {boolean} 是否可以切换到上一张
 */
const canGoPrev = computed(() => {
  return props.currentIndex !== undefined && props.currentIndex > 0;
});

/**
 * 计算属性：是否可以切换到下一张图片
 * @returns {boolean} 是否可以切换到下一张
 */
const canGoNext = computed(() => {
  return props.currentIndex !== undefined && 
         props.totalImages !== undefined && 
         props.currentIndex < props.totalImages - 1;
});

/**
 * 计算属性：是否显示多图切换功能
 * @returns {boolean} 是否显示左右箭头
 */
const showImageNavigation = computed(() => {
  return props.totalImages !== undefined && props.totalImages > 1;
});

/**
 * 计算属性：根据蒙层颜色动态设置按钮颜色类名
 * @returns {string} 按钮颜色类名
 */
const buttonColorClass = computed(() => {
  // 如果使用白色蒙层，按钮使用深色；否则使用白色
  if (props.showOverlay && props.overlayColor && props.overlayColor.includes('255, 255, 255')) {
    return 'text-gray-800';
  }
  return 'text-white';
});

// ============== 设备事件监听 ==============
let _keyDownHandler: ((e: KeyboardEvent) => void) | null = null;
let _mouseWheelHandler: ((e: WheelEvent) => void) | null = null;

const deviceSupportInstall = () => {
  _keyDownHandler = rafThrottle((e: KeyboardEvent) => {
    const keyCode = e.keyCode;
    switch (keyCode) {
      case 27: // ESC
        emit('close');
        break;
      case 32: // Space
        toggleMode();
        break;
      case 38: // Up
        handleActions('zoomIn');
        break;
      case 40: // Down
        handleActions('zoomOut');
        break;
      case 37: // Left - 上一张图片
        if (showImageNavigation.value) {
          handlePrevImage();
        }
        break;
      case 39: // Right - 下一张图片
        if (showImageNavigation.value) {
          handleNextImage();
        }
        break;
    }
  });
  _mouseWheelHandler = rafThrottle((e: WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY;
    const currentScale = transform.value.scale;
    const newScale = currentScale * (1 - delta * ZOOM_RATE);
    transform.value.scale = Math.min(MAX_SCALE, Math.max(MIN_SCALE, newScale));
    emitAfterTransform();
  });

  document.addEventListener('keydown', _keyDownHandler);
  document.addEventListener('wheel', _mouseWheelHandler);
};

const deviceSupportUninstall = () => {
  if (_keyDownHandler) document.removeEventListener('keydown', _keyDownHandler);
  if (_mouseWheelHandler) document.removeEventListener('wheel', _mouseWheelHandler);
};

watch(
  () => [props.offsetX, props.offsetY],
  ([newOffsetX, newOffsetY]) => {
    transform.value.offsetX = newOffsetX ?? 0;
    transform.value.offsetY = newOffsetY ?? 0;
  }
);

// 监听当前图片源变化，当图片切换时重新加载图片
watch(
  () => currentImageSrc.value,
  (newImgSrc, oldImgSrc) => {
    console.log('img-core watch - 图片源变化:', { newImgSrc, oldImgSrc, currentIndex: props.currentIndex });
    if (newImgSrc && newImgSrc !== oldImgSrc) {
      console.log('img-core watch - 开始加载新图片:', newImgSrc);
      // 预加载新图片
      const img = new Image();
      img.src = newImgSrc;
      img.onload = () => {
        console.log('img-core watch - 新图片加载完成:', { width: img.width, height: img.height });
        imageWidth.value = img.width;
        imageHeight.value = img.height;
        // 重置变换状态（但保持缩放模式）
        reset();
        imgResize();
        emitAfterTransform();
        emit('imgLoad', { width: img.width, height: img.height });
      };
    }
  }
);

onMounted(() => {
  deviceSupportInstall();

  // 1) 监听容器实际大小，实时通知父组件
  if (containerRef.value) {
    const resizeObserver = new ResizeObserver(entries => {
      if (!entries.length) return;
      const { width, height } = entries[0]!.contentRect;
      emit('containerRectChange', { width, height });
    });
    resizeObserver.observe(containerRef.value);
  }

  // 2) 预加载图片，拿到图片原始宽高
  const img = new Image();
  img.src = currentImageSrc.value || props.imgSrc;
  img.onload = () => {
    imageWidth.value = img.width;
    imageHeight.value = img.height;
    console.log('%c[ImageViewer] img onload ->', 'color: #2196F3;', {
    realWidth: img.width,
    realHeight: img.height,
  });
    imgResize(); // 让图片自适应 or 原图大小
    // 触发一次 afterTransform，告诉父组件初始尺寸
    emitAfterTransform();
    // 暴露图片加载完成事件
    emit('imgLoad', { width: img.width, height: img.height });
  };
});
onUnmounted(() => {
  deviceSupportUninstall();
});

// =================== 方法 ===================
// 翻转
function imgScale(axis: 'x' | 'y') {
  if (loading.value) return;
  if (axis === 'x') {
    transform.value.flipX = transform.value.flipX === 1 ? -1 : 1;
  } else {
    transform.value.flipY = transform.value.flipY === 1 ? -1 : 1;
  }
  emitAfterTransform();
}

// 切换模式
function toggleMode() {
  if (loading.value) return;
  const modeKeys = Object.keys(Mode).filter(k => typeof Mode[k as keyof typeof Mode] === 'string');
  const modeValues = modeKeys.map(k => Mode[k as keyof typeof Mode]);
  const currentIndex = modeValues.indexOf(mode.value);
  const nextIndex = (currentIndex + 1) % modeValues.length;
  mode.value = modeValues[nextIndex] as Mode;
  reset();      // 重置
  imgResize();  // 重新计算缩放
  emitAfterTransform();
}

// 处理操作
function handleActions(action: string) {
  if (loading.value) return;
  const { rotateDeg } = { rotateDeg: 90 };
  switch (action) {
    case 'zoomOut':
      transform.value.scale = Math.max(MIN_SCALE, transform.value.scale * 0.95);
      break;
    case 'zoomIn':
      transform.value.scale = Math.min(MAX_SCALE, transform.value.scale * 1.05);
      break;
    case 'clocelise':
      transform.value.deg += rotateDeg;
      break;
    case 'anticlocelise':
      transform.value.deg -= rotateDeg;
      break;
  }
  transform.value.enableTransition = true;
  setTimeout(() => { transform.value.enableTransition = false; }, 300);

  emitAfterTransform();
}

// 重置
function reset() {
  transform.value.scale = 1;
  transform.value.deg = 0;
  transform.value.flipX = 1;
  transform.value.flipY = 1;
  transform.value.offsetX = 0;
  transform.value.offsetY = 0;
}

// 根据模式调整大小
function imgResize() {
  const realWidth = imageWidth.value;
  const realHeight = imageHeight.value;
  const windowW = document.documentElement.clientWidth;
  const windowH = document.documentElement.clientHeight;
  if (mode.value === Mode.FIT) {
    // 自适应屏幕
    const scaleX = windowW / realWidth;
    const scaleY = windowH / realHeight;
    transform.value.scale = Math.min(scaleX, scaleY);
  } else {
    transform.value.scale = 1;
  }
}

// 关闭
function handleClick() {
  emit('close');
}

// 滚轮
function handleWheel(event: WheelEvent) {
  if (loading.value) return;
  if (event.deltaY < 0) {
    handleActions('zoomIn');
  } else {
    handleActions('zoomOut');
  }
}

// 拖拽
function handleMouseDown(e: MouseEvent) {
  if (loading.value || e.button !== 0) return;
  e.preventDefault();
  const startX = e.pageX;
  const startY = e.pageY;
  const initOffsetX = transform.value.offsetX;
  const initOffsetY = transform.value.offsetY;

  const moveHandler = rafThrottle((ev: MouseEvent) => {
    ev.preventDefault();
    transform.value.offsetX = initOffsetX + (ev.pageX - startX);
    transform.value.offsetY = initOffsetY + (ev.pageY - startY);
    emitAfterTransform();
  });

  const upHandler = () => {
    document.removeEventListener('mousemove', moveHandler);
    document.removeEventListener('mouseup', upHandler);
    document.removeEventListener('mouseleave', upHandler);
  };

  document.addEventListener('mousemove', moveHandler);
  document.addEventListener('mouseup', upHandler);
  document.addEventListener('mouseleave', upHandler);
}

// 触摸拖拽
function handleTouchStart(e: TouchEvent) {
  if (loading.value || !e.touches.length) return;
  e.preventDefault();
  
  const touch = e.touches[0]!; // 非空断言，因为上面已经检查长度
  const startX = touch.pageX;
  const startY = touch.pageY;
  const initOffsetX = transform.value.offsetX;
  const initOffsetY = transform.value.offsetY;

  const moveHandler = rafThrottle((ev: TouchEvent) => {
    if (!ev.touches.length) return;
    ev.preventDefault();
    
    const touch = ev.touches[0]!; // 非空断言，因为上面已经检查长度
    transform.value.offsetX = initOffsetX + (touch.pageX - startX);
    transform.value.offsetY = initOffsetY + (touch.pageY - startY);
    emitAfterTransform();
  });

  const endHandler = () => {
    document.removeEventListener('touchmove', moveHandler);
    document.removeEventListener('touchend', endHandler);
    document.removeEventListener('touchcancel', endHandler);
  };

  document.addEventListener('touchmove', moveHandler, { passive: false });
  document.addEventListener('touchend', endHandler);
  document.addEventListener('touchcancel', endHandler);
}

// 通知父组件当前实际渲染尺寸 + 变换状态
function emitAfterTransform() {
  const t = transform.value;
  // 考虑旋转时，宽高可能交换
  const isRotated = t.deg % 180 !== 0;
  const baseWidth = isRotated ? imageHeight.value : imageWidth.value;
  const baseHeight = isRotated ? imageWidth.value : imageHeight.value;
  const curWidth = baseWidth * t.scale * Math.abs(t.flipX);
  const curHeight = baseHeight * t.scale * Math.abs(t.flipY);

  emit('afterTransform', {
    curWidth,
    curHeight,
    deg: t.deg,
    scaleX: t.flipX,
    scaleY: t.flipY,
    offsetX: t.offsetX,
    offsetY: t.offsetY,
    imgWidth: imageWidth.value,
    imgHeight: imageHeight.value,
  });
}

// rafThrottle
function rafThrottle(fn: Function) {
  let locked = false;
  return (...args: any[]) => {
    if (locked) return;
    locked = true;
    window.requestAnimationFrame(() => {
      fn.apply(undefined, args);
      locked = false;
    });
  };
}

const ZOOM_RATE = 0.001; // 缩放速率
const MIN_SCALE = 0.2;
const MAX_SCALE = 12;

/**
 * 切换到上一张图片
 */
const handlePrevImage = () => {
  console.log('img-core - handlePrevImage 被调用, canGoPrev:', canGoPrev.value);
  if (canGoPrev.value) {
    emit('prevImage');
  }
};

/**
 * 切换到下一张图片
 */
const handleNextImage = () => {
  console.log('img-core - handleNextImage 被调用, canGoNext:', canGoNext.value);
  if (canGoNext.value) {
    emit('nextImage');
  }
};
</script>

<template>
  <transition name="viewer-fade">
    <div 
      :style="{ backgroundColor: props.showOverlay ? props.overlayColor : 'rgba(0, 0, 0, 0.5)' }"
      class="fixed inset-0 z-[999] flex justify-center items-center select-none overflow-hidden" 
      ref="containerRef" 
      tabindex="-1"
    >
      <!-- 操作栏 -->
      <div class="absolute bottom-10 flex justify-center items-center z-50 bg-white/10 px-2 py-2 rounded-full">
        <!-- 翻转、缩放、旋转等操作按钮 -->
        <span @click="imgScale('x')" class="cursor-pointer">
          <a-tooltip placement="top">
            <template #title>左右翻转</template>
            <IconifyIcon icon="ant-design:hourglass-outlined" :rotate="45" class="w-6 h-6" />
          </a-tooltip>
        </span>
        <span @click="imgScale('y')" class="cursor-pointer ml-2.5">
          <a-tooltip placement="top">
            <template #title>上下翻转</template>
            <IconifyIcon icon="ant-design:hourglass-outlined" class="w-6 h-6" />
          </a-tooltip>
        </span>
        <span class="mx-1">
          <a-tooltip placement="top">
            <template #title>缩小</template>
            <IconifyIcon icon="ant-design:minus-circle-outlined" class="w-6 h-6 cursor-pointer"
              @click="handleActions('zoomOut')" />
          </a-tooltip>
        </span>
        <span class="mx-1">
          <a-tooltip placement="top">
            <template #title>放大</template>
            <IconifyIcon icon="ant-design:plus-circle-outlined" class="w-6 h-6 cursor-pointer"
              @click="handleActions('zoomIn')" />
          </a-tooltip>
        </span>
        <span class="mx-1">
          <a-tooltip placement="top">
            <template #title>屏幕自适应/原图大小</template>
            <IconifyIcon :icon="modeIcons[mode]" class="w-6 h-6 cursor-pointer" @click="toggleMode" />
          </a-tooltip>
        </span>
        <span class="mx-1">
          <a-tooltip placement="top">
            <template #title>逆时针</template>
            <IconifyIcon icon="ant-design:undo-outlined" class="w-6 h-6 cursor-pointer"
              @click="handleActions('anticlocelise')" />
          </a-tooltip>
        </span>
        <span class="mx-1">
          <a-tooltip placement="top">
            <template #title>顺时针</template>
            <IconifyIcon icon="ant-design:redo-outlined" :rotate="180" class="w-6 h-6 cursor-pointer"
              @click="handleActions('clocelise')" />
          </a-tooltip>
        </span>
      </div>
      <!-- 关闭按钮 -->
      <a-tooltip placement="top">
        <template #title>关闭</template>
        <IconifyIcon icon="ant-design:close-circle-outlined" 
                    :class="`absolute top-12 right-5 z-50 cursor-pointer ${buttonColorClass} text-5xl`"
                    @click="handleClick" />
      </a-tooltip>

      <!-- 左箭头 - 上一张图片 -->
      <div 
        v-if="showImageNavigation" 
        class="absolute left-5 top-1/2 z-50 -translate-y-1/2"
      >
        <a-tooltip placement="right">
          <template #title>上一张图片 (←)</template>
          <IconifyIcon 
            icon="ant-design:left-circle-outlined" 
            :class="[
              `cursor-pointer ${buttonColorClass} text-5xl transition-opacity`,
              canGoPrev ? 'hover:text-blue-400' : 'opacity-30 cursor-not-allowed'
            ]"
            @click="handlePrevImage" 
          />
        </a-tooltip>
      </div>

      <!-- 右箭头 - 下一张图片 -->
      <div 
        v-if="showImageNavigation" 
        class="absolute right-5 top-1/2 z-50 -translate-y-1/2"
      >
        <a-tooltip placement="left">
          <template #title>下一张图片 (→)</template>
          <IconifyIcon 
            icon="ant-design:right-circle-outlined" 
            :class="[
              `cursor-pointer ${buttonColorClass} text-5xl transition-opacity`,
              canGoNext ? 'hover:text-blue-400' : 'opacity-30 cursor-not-allowed'
            ]"
            @click="handleNextImage" 
          />
        </a-tooltip>
      </div>

      <!-- 图片计数信息 -->
      <div 
        v-if="showImageNavigation" 
        class="absolute top-12 left-1/2 z-50 -translate-x-1/2 bg-black/50 rounded-full px-4 py-2 text-white text-sm"
      >
        {{ (currentIndex || 0) + 1 }} / {{ totalImages }}
      </div>

      <!-- 图片容器 -->
      <div class="flex justify-center items-center h-full w-full touch-none" @wheel="handleWheel">
        <img
          :src="currentImageSrc"
          :style="transformStyle"
          class="max-w-full max-h-full block mx-auto touch-none select-none [-webkit-user-drag:none]"
          @mousedown="handleMouseDown"
          @touchstart="handleTouchStart"
        />
      </div>
    </div>
  </transition>
</template>
