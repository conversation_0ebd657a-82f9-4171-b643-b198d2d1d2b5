<script setup lang="ts">
import { ref, watch } from 'vue';

/**
 * 样本状态选择组件
 * @description 提供随机抽取和按比例抽取两种模式
 * @example
 * <sample-status v-model:config="config" @change="handleChange" />
 */

// 定义配置接口
interface SampleStatusConfig {
  status: number;
  randomPercent: number;
  okPercent: number;
  forbidPercent: number;
}

// 定义组件属性
defineProps({
  /** 默认选中的状态值 */
  defaultStatus: {
    type: Number,
    default: 1
  },
  /** 是否作为表单字段（如果是，则发出更多的事件以支持表单集成） */
  isFormField: {
    type: Boolean,
    default: false
  }
});

// 定义双向绑定的配置对象
const config = defineModel<SampleStatusConfig|number>('modelValue', {
  default: () => ({
    status: 1,
    randomPercent: 0,
    okPercent: 0,
    forbidPercent: 0
  })
});
const innerConfig = ref<SampleStatusConfig>(config.value as SampleStatusConfig);

// 初始化状态值
watch(config, (newValue) => {
  console.log('newValue', newValue);
  if (typeof newValue === 'object') {
    innerConfig.value = newValue as SampleStatusConfig;
  }
   
});

// 定义事件
const emit = defineEmits(['change', 'update:modelValue']);

// 处理状态变更
const handleStatusChange = () => {
  console.log('config.value', config.value);
  config.value = innerConfig.value;
  emit('change', innerConfig.value);
  // 触发表单验证
  emit('update:modelValue', innerConfig.value);
};

// 处理百分比变更
const handlePercentChange = () => {
  config.value = innerConfig.value;
  emit('change', innerConfig.value);
  // 触发表单验证
  emit('update:modelValue', innerConfig.value);
};

// 验证数字输入 (仅允许数字和小数点)
const validateNumberInput = (event: KeyboardEvent) => {
  return /[\d\.]/.test(String.fromCharCode(event.keyCode));
};
</script>

<template>
  <div class="flex flex-nowrap items-center gap-4 sm:flex-row sm:items-center xs:flex-col xs:items-start">
    <!-- 使用 a-form-item-rest 包装内部控件，避免被父表单收集 -->
    <a-form-item-rest>
      <div class="flex-shrink-0">
          <a-radio-group v-model:value="innerConfig.status" @change="handleStatusChange">
              <a-radio :value="1">随机抽取</a-radio>
              <a-radio :value="2">按比例抽取</a-radio>
          </a-radio-group>
      </div>
    </a-form-item-rest>

    <a-form-item-rest>
      <div class="flex flex-nowrap items-center gap-2 xs:mt-2">
        <template v-if="innerConfig.status === 1">
          <span class="whitespace-nowrap mr-1">比例：</span>
          <a-input-number
            v-model:value="innerConfig.randomPercent"
            :min="0"
            :max="100"
            :precision="1"
            :controls="false"
            size="small"
            class="w-20"
            @keypress="validateNumberInput"
            @change="handlePercentChange"
          />
          <span class="ml-1">%</span>
        </template>

        <template v-if="innerConfig.status === 2">
          <span class="whitespace-nowrap mr-1">通过：</span>
          <a-input-number
            v-model:value="innerConfig.okPercent"
            :min="0"
            :max="100"
            :precision="1"
            :controls="false"
            size="small"
            class="w-20"
            @keypress="validateNumberInput"
            @change="handlePercentChange"
          />
          <span class="ml-1">%</span>

          <span class="whitespace-nowrap ml-2 mr-1">拒绝：</span>
          <a-input-number
            v-model:value="innerConfig.forbidPercent"
            :min="0"
            :max="100"
            :precision="1"
            :controls="false"
            size="small"
            class="w-20"
            @keypress="validateNumberInput"
            @change="handlePercentChange"
          />
          <span class="ml-1">%</span>
        </template>
      </div>
    </a-form-item-rest>
  </div>
</template>
