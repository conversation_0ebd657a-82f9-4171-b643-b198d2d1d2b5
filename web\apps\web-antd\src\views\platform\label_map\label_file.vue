<script lang="ts" setup>
import { ref, reactive, onMounted, computed, toRaw } from 'vue';
import { message } from 'ant-design-vue';
import { Modal } from 'ant-design-vue';
import TableTemplate from '#/components/table/index.vue';
import { useVbenModal } from '@vben/common-ui';
import FormModal from '#/components/modal/form-modal.vue';
import { useApiHandler } from '#/composables/common/use-api-handler';
import { LABEL_MAP } from '#/constants/maps/label-map';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { mapToOptions } from '#/utils/transform';
import { MODEL_MAP } from '#/constants/maps/tools';
import {
  addSubLabel,
  deleteSubLabel,
  updateSubLabel,
  querySubLabel,
  batchCreateSubLabel,
} from '#/api/platform';
import { useUserStore } from '#/store/user';
// 获取用户ID（假设从某个地方获取，例如 Vuex 或全局状态）
const userid = useUserStore().userid;

const { loadings, handleApiCall } = useApiHandler();
const tableTemplateRef = ref<any>(null);

// 表单和分页状态
const formInline = ref({
  company: '',
  label: '',
  subLabel: '',
});
const paginationOptions = reactive({
  pageSize: 20,
  currentPage: 1,
  layouts: ['Total', 'PrevPage', 'NextPage'],
});

const allForbidOptions = ref<any[]>([]);
const deleteConfirmVisible = ref(false);
const deleteRowId = ref<string | null>(null);
// 表格配置
const columns: VxeGridProps['columns'] = [
  {
    field: 'company',
    title: '供应商',
    width: 200,
    align: 'center',
    formatter: ({ cellValue }) => MODEL_MAP[cellValue] || cellValue,
  },
  { field: 'level', title: '级别', align: 'center' },
  { field: 'sub_label', title: '供应商标签', align: 'center' },
  {
    field: 'label',
    title: 'KCS标签',
    align: 'center',
    formatter: ({ cellValue }) => LABEL_MAP[cellValue] || cellValue,
  },
  { field: 'label_desc', title: '标签描述', align: 'center' },
  { title: '操作', align: 'center', slots: { default: 'operation' } },
];

// 查询配置
const searchOptions = {
  collapsed: false,
  showCollapseButton: false,
  submitOnChange: false,
  wrapperClass: 'grid-cols-4 gap-3',
  schemas: {
    '1': [
      {
        component: 'Select',
        fieldName: 'label',
        label: 'KCS标签',
        componentProps: {
          clearable: true,
          options: mapToOptions(LABEL_MAP),
          onAllForbidType: (val: any[]) => (allForbidOptions.value = val),
          onForbidType: (val: string) => (formInline.value.label = val),
        },
      },
      {
        component: 'Select',
        fieldName: 'company',
        label: '供应商',
        componentProps: {
          placeholder: '请选择',
          clearable: true,
          options: mapToOptions(MODEL_MAP),
        },
      },
      {
        component: 'Input',
        fieldName: 'subLabel',
        label: '供应商标签',
        componentProps: {
          placeholder: '请输入供应商标签',
        },
      },
    ],
  },
};

// 查询方法
const queryMethod = async ({
  page,
  limit,
  ...formValues
}: {
  page: number;
  limit: number;
  [key: string]: any;
}) => {
  try {
    const response = await querySubLabel({
      page,
      limit: 20,
      ...formValues,
    });
    return {
      items: response.data.records || [],
      total: response.data.total || 0,
    };
  } catch (error) {
    console.error('查询失败:', error);
    return { items: [], total: 0 };
  }
};

// 新增/编辑弹窗
const [RegisterModal, registerModalApi] = useVbenModal({
  connectedComponent: FormModal,
});
const form = ref({
  company: '',
  level: '',
  sub_label: '',
  label: '',
  label_desc: '',
});
const modalConfig = computed(() => ({
  title: type.value === 'add' ? '新增' : '编辑',
  fullscreenButton: false,
  footer: true,
}));
const wrapperClass = 'grid grid-cols-2 gap-2';
const type = ref<'add' | 'edit'>('add');
const formConfig = {
  commonConfig: {
    labelWidth: 80,
  },

  schema: [
    {
      fieldName: 'company',
      label: '供应商',
      component: 'Select',
      class: 'col-span-2',
      componentProps: {
        placeholder: '请选择供应商',
        options: mapToOptions(MODEL_MAP),
        class: 'w-full',
      },
      rules: 'required',
    },
    {
      fieldName: 'level',
      label: '级别',
      class: 'col-span-2',
      component: 'Select',
      componentProps: {
        placeholder: '请选择级别',
        class: 'w-full',
        options: [
          { label: '一级', value: 1 },
          { label: '二级', value: 2 },
          { label: '三级', value: 3 },
        ],
      },
      dependencies: {
        componentProps(values: any) {
          if (values.company === 'tencent') {
            return {
              options: [
                { label: '一级', value: 1 },
                { label: '二级', value: 2 },
              ],
            };
          } else if (values.company === 'shumei')
            return {
              options: [
                { label: '一级', value: 1 },
                { label: '三级', value: 3 },
              ],
            };
          return {};
        },
        triggerFields: ['company'],
      },
      rules: 'required',
    },
    {
      fieldName: 'sub_label',
      label: '供应商标签',
      component: 'Input',
      componentProps: {
        placeholder: '请输入供应商标签',
        class: 'w-full',
      },
      rules: 'required',
    },
    {
      fieldName: 'label',
      label: 'KCS标签',
      component: 'Select',
      componentProps: {
        clearable: true,
        options: mapToOptions(LABEL_MAP),
        class: 'w-full',
      },
      rules: 'required',
    },
    {
      fieldName: 'label_desc',
      label: '标签描述',
      class: 'col-span-2',
      component: 'Input',
      componentProps: {
        placeholder: '请输入标签描述',
        class: 'w-full',
      },
    },
  ],
  showDefaultActions: false,
};

const paginationOptionsmodal = {
  enabled: false,
};
// 批量导入弹窗状态
const batchImportVisible = ref(false);
const hasUploadSublabel = ref(false);
const uploadResultTab = ref('first');
const parseData = ref({ success_list: [], fail_list: [] });
const importResultList = ref<any[]>([]);
const uploadRef = ref<any>(null);

// 导入结果弹窗状态
const confirmImportResult = ref(false);
const importResultColumns: VxeGridProps['columns'] = [
  { field: 'sheet', title: 'Sheet' },
  {
    field: 'company',
    title: '供应商',
    formatter: ({ cellValue }) => MODEL_MAP[cellValue] || cellValue,
  },
  { field: 'level', title: '级别' },
  { field: 'sub_label', title: '供应商标签' },
  {
    field: 'label',
    title: 'KCS标签',
    formatter: ({ cellValue }) => LABEL_MAP[cellValue] || cellValue,
  },
  { field: 'label_desc', title: '标签描述' },
];
const importResultColumns2: VxeGridProps['columns'] = [
  {
    field: 'company',
    title: '供应商',
    formatter: ({ cellValue }) => MODEL_MAP[cellValue] || cellValue,
  },
  {
    field: 'level',
    title: '级别',
  },
  {
    field: 'sub_label',
    title: '供应商标签',
  },
  {
    field: 'label',
    title: 'KCS标签',
    formatter: ({ cellValue }) => LABEL_MAP[cellValue] || cellValue, // 使用 forbidNameMatch 映射
  },
  {
    field: 'label_desc',
    title: '标签描述',
  },
  {
    field: 'success',
    title: '导入状态',
    slots: { default: 'success' },
  },
  {
    field: 'message',
    title: '失败原因',
  },
];
// 操作方法
const openRegisterDialog = (modalType: 'add' | 'edit', data?: any) => {
  type.value = modalType;
  if (modalType === 'edit' && data) {
    form.value = { ...data };
  } else {
    form.value = {
      company: '',
      level: '',
      sub_label: '',
      label: '',
      label_desc: '',
    };
  }
  registerModalApi.setData({ values: form.value });
  registerModalApi.open();
};

const handleSubmit = async (values: any) => {
  const params = { ...values };
  await handleApiCall(
    type.value === 'add' ? addSubLabel : updateSubLabel,
    params,
    {
      loadingKey: 'submit',
      onSuccess: () => {
        registerModalApi.close();
        tableTemplateRef.value?.refreshTable();
      },
    },
  );
};

const openBatchImport = () => {
  batchImportVisible.value = true;
};

const beforeUpload = (file: File) => {
  const fileType = file.name.split('.').pop()?.toLowerCase();
  const allowTypes = ['xls', 'xlsx'];
  const maxSize = 1 * 1024 * 1024;
  if (!allowTypes.includes(fileType!)) {
    message.error('仅支持上传xls和xlsx格式的文件');
    return false;
  }
  if (file.size > maxSize) {
    message.error('文件大小不能超过1M');
    return false;
  }
  return true;
};

const handleImportParseSuccess = (response: any) => {
  if (!response.result) {
    message.error(`文件解析失败: ${response.message}`);
    return;
  }

  hasUploadSublabel.value = true;

  parseData.value = {
    success_list: response.data?.success_list || [],
    fail_list: response.data?.fail_list || [],
  };
  console.log('parseData:', parseData.value); // 调试 parseData
  uploadResultTab.value =
    parseData.value.success_list.length > 0 ? 'first' : 'second';
};

const handleImportParseError = () => {
  message.error('文件解析失败，请重新上传');
};

const doImportSubLabel = async () => {
  if (parseData.value.success_list.length <= 0) {
    message.error('没有能导入的标签，请先上传文件');
    return;
  }
  try {
    const response = await batchCreateSubLabel(parseData.value.success_list);
    console.log(response);
    importResultList.value = response.data;
    batchImportVisible.value = false;
    hasUploadSublabel.value = false;
    confirmImportResult.value = true;
    tableTemplateRef.value?.refreshTable();
  } catch (error) {
    console.error('批量导入失败:', error);
    message.error('批量导入失败，请重试');
  }
};
onMounted(() => {
  tableTemplateRef.value?.refreshTable();
});
const openDeleteConfirm = (id: string) => {
  deleteRowId.value = id;
  Modal.confirm({
    title: '提示',
    content: '确定要删除此标签吗？',
    centered: true, // 确保弹窗居中
    okText: '确定',
    cancelText: '取消',
    onOk() {
      confirmDelete();
    },
    onCancel() {
      deleteRowId.value = null; // 清空 ID
    },
  });
};
const confirmDelete = async () => {
  if (deleteRowId.value) {
    await handleApiCall(
      deleteSubLabel,
      { id: deleteRowId.value },
      {
        loadingKey: 'delete',
        onSuccess: () => {
          tableTemplateRef.value?.refreshTable();
          deleteRowId.value = null; // 清空 ID
        },
      },
    );
  }
};
</script>
<template>
  <div>
    <TableTemplate
      ref="tableTemplateRef"
      :showHeader="true"
      :columns="columns"
      :pagination-options="paginationOptions"
      :query-method="queryMethod"
      :search-options="searchOptions"
    >
      <template #toolbar-left>
        <a-button
          type="primary"
          class="mr-2"
          @click="openRegisterDialog('add')"
        >
          新增
        </a-button>
        <a-button type="primary" @click="openBatchImport">导入</a-button>
      </template>
      <template #operation="{ row }">
        <a-button type="link" @click="openRegisterDialog('edit', row)">
          编辑
        </a-button>
        <a-button
          v-if="!row.children"
          type="link"
          :danger="true"
          :loading="loadings.delete"
          @click="openDeleteConfirm(row.id)"
        >
          删除
        </a-button>
      </template>
    </TableTemplate>

    <!-- 新增/编辑弹窗 -->
    <RegisterModal
      :form-config="formConfig"
      :wrapperClass="wrapperClass"
      :modal-config="modalConfig"
      :modal-class="'custom-modal'"
      @onConfirm="handleSubmit"
    />
    <a-modal
      v-model:open="deleteConfirmVisible"
      title="确认删除"
      :width="400"
      :body-style="{ padding: '20px' }"
    >
      <p>确定要删除此标签吗？</p>
      <template #footer>
        <a-button @click="deleteConfirmVisible = false">取消</a-button>
        <a-button
          type="primary"
          :loading="loadings.delete"
          @click="confirmDelete"
          >确定</a-button
        >
      </template>
    </a-modal>
    <!-- 批量导入弹窗 -->
    <a-modal
      v-model:open="batchImportVisible"
      title="批量导入"
      :width="800"
      :body-style="{ padding: '20px' }"
    >
      <template #footer>
        <a-button
          @click="
            batchImportVisible = false;
            hasUploadSublabel = false;
          "
        >
          取消
        </a-button>
        <a-button type="primary" @click="doImportSubLabel">导入</a-button>
      </template>
      <a-upload
        ref="uploadRef"
        :before-upload="beforeUpload"
        :action="`/manual/control/parse_upload_sub_label?user_id=${userid}`"
        :on-success="handleImportParseSuccess"
        :on-error="handleImportParseError"
        :showUploadList="false"
        :limit="1"
      >
        <a-button type="primary">点击上传</a-button>
        <div class="mt-[10px]">
          只能上传xls/xlsx文件，且不超过1M。解析完成后需要确认标签并点击导入按钮进行导入
        </div>
      </a-upload>
      <div v-if="hasUploadSublabel">
        <a-tabs v-model:activeKey="uploadResultTab">
          <a-tab-pane
            key="first"
            tab="解析成功列表"
            v-if="parseData.success_list && parseData.success_list.length > 0"
          >
            <TableTemplate
              :columns="importResultColumns"
              :maxHeight="300"
              :pagination-options="paginationOptionsmodal"
              :data="{
                items: toRaw(parseData.success_list),
                total: parseData.success_list.length,
              }"
              :autoContent="false"
            />
          </a-tab-pane>
          <a-tab-pane
            key="second"
            tab="解析失败列表"
            v-if="parseData.fail_list && parseData.fail_list.length > 0"
          >
            <TableTemplate
              :columns="[
                { field: 'sheet', title: 'Sheet' },
                { field: 'index', title: '行号' },
                { field: 'message', title: '失败原因' },
              ]"
              :data="{
                items: toRaw(parseData.fail_list),
                total: parseData.fail_list.length,
              }"
              :pagination-options="paginationOptionsmodal"
              :max-height="300"
              :autoContent="false"
            />
          </a-tab-pane>
        </a-tabs>
        <div
          v-if="
            (!parseData.success_list || parseData.success_list.length === 0) &&
            (!parseData.fail_list || parseData.fail_list.length === 0)
          "
          class="empty-state"
        >
          上传文件解析结果为空，请检查文件内容后重新上传。
        </div>
      </div>
    </a-modal>

    <!-- 导入结果弹窗 -->
    <a-modal
      v-model:open="confirmImportResult"
      title="确认导入结果"
      :width="800"
      :body-style="{ padding: '20px' }"
      @ok="confirmImportResult = false"
    >
      <table-template
        :columns="importResultColumns2"
        :pagination-options="paginationOptionsmodal"
        :data="{
          items: toRaw(importResultList),
          total: importResultList.length,
        }"
        :max-height="300"
        :autoContent="false"
      >
        <template #success="{ row }">
          <span :class="row.success ? 'text-green-500' : 'text-red-500'">
            {{ row.success ? '成功' : '失败' }}
          </span>
        </template>
      </table-template>
    </a-modal>
  </div>
</template>
