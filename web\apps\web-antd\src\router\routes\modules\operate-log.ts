// 操作日志
import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';

import CheckLog from '#/views/operate-log/check-log/index.vue';
import KeywordOp from '#/views/operate-log/keyword-op/index.vue';
import BlackTargetLog from '#/views/operate-log/black-target-log/index.vue';
import WhiteTargetLog from '#/views/operate-log/white-target-log/index.vue';
import Hypersensitive from '#/views/operate-log/hyper-sensitive/index.vue';
import WatchViolation from '#/views/operate-log/watch-violation/index.vue';
import Blacklist from '#/views/operate-log/blacklist/index.vue';
import KeywordRepairLog from '#/views/operate-log/keyword-repair-log/index.vue';
import HomeUserLog from '#/views/operate-log/home-user-log/index.vue';
import BlackConfirmLog from '#/views/operate-log/black-confirm-log/index.vue';
import CvMarkLog from '#/views/operate-log/cv-mark-log/index.vue';
import StreamTaskRecord from '#/views/operate-log/stream-task-record/index.vue';
import StreamDetailLog from '#/views/operate-log/stream-detail-log/index.vue';


const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'lucide:layout-dashboard',
      // order: -1,
      title: $t('page.operateLog.title'),
    },
    name: 'OperateLog',
    path: '/operateLog',
    children: [
      // 工单日志
      {
        name: 'checkLog',
        path: 'checkLog',
        component: CheckLog,
        meta: {
          affixTab: true,
          title: $t('page.operateLog.task.checkLog'),
        },
      },
      // 关键词操作日志
      {
        name: 'KeywordOp',
        path: 'keywordOp',
        component: KeywordOp,
        meta: {
          affixTab: true,
          title: $t('page.operateLog.task.keywordOp'),
        },
      },
      // 黑库命中记录
      {
        name: 'BlackTargetLog',
        path: 'blackTargetLog',
        component: BlackTargetLog,
        meta: {
          affixTab: true,
          title: $t('page.operateLog.task.blackTargetLog'),
        },
      },
      // 白库命中记录
      {
        name: 'WhiteTargetLog',
        path: 'whiteTargetLog',
        component: WhiteTargetLog,
        meta: {
          affixTab: true,
          title: $t('page.operateLog.task.whiteTargetLog'),
        },
      },
      // 高敏行为日志
      {
        name: 'Hypersensitive',
        path: 'hypersensitive',
        component: Hypersensitive,
        meta: {
          affixTab: true,
          title: $t('page.operateLog.task.hypersensitive'),
        },
      },
      // 监管违规日志
      {
        name: 'WatchViolation',
        path: 'watchViolation',
        component: WatchViolation,
        meta: {
          affixTab: true,
          title: $t('page.operateLog.task.watchViolation'),
        },
      },
      // 用户管理日志
      {
        name: 'Blacklist',
        path: 'blacklist',
        component: Blacklist,
        meta: {
          affixTab: true,
          title: $t('page.operateLog.task.blacklist'),
        },
      },
      // 关键词修复日志
      {
        name: 'KeywordRepairLog',
        path: 'keyword_repair_log',
        component: KeywordRepairLog,
        meta: {
          affixTab: true,
          title: $t('page.operateLog.task.keyword_repair_log'),
        },
      },
      // 网页一线账号日志
      {
        name: 'HomeUserLog',
        path: 'home_user_log',
        component: HomeUserLog,
        meta: {
          affixTab: true,
          title: $t('page.operateLog.task.home_user_log'),
        },
      },
      // 黑库确认日志
      {
        name: 'BlackConfirmLog',
        path: 'black_comfirm_log',
        // black_comfirm_log
        component: BlackConfirmLog,
        meta: {
          affixTab: true,
          title: $t('page.operateLog.task.black_confirm_log'),
        },
      },
      // CV标注日志
      {
        name: 'CvMarkLog',
        path: 'cv_mark_log',
        component: CvMarkLog,
        meta: {
          affixTab: true,
          title: $t('page.operateLog.task.cv_mark_log'),
        },
      },
      // // 视频流操作记录
      {
        name: 'StreamTaskRecord',
        path: 'stream_task_record',
        component: StreamTaskRecord,
        meta: {
          affixTab: true,
          title: $t('page.operateLog.task.stream_task_record'),
        },
      },
      // 
      {
        name: 'StreamDetailLog', //违规详情
        path: 'stream_detail_log',
        component: StreamDetailLog,
        meta: {
          hideInMenu: true,
          title: $t('page.operateLog.task.stream_detail_log'),
        },
      },
    ],
  },
];

export default routes;
