/**
 * 消息显示模式枚举
 * @enum {string}
 */
export enum MessageShowMode {
  /** 不管result是true还是false都输出message */
  All = 'All',
  /** 只在result为true时显示message */
  Success = 'Success', 
  /** 只在result为false时显示message */
  Fail = 'Fail',
  /** 不输出message */
  None = 'None'
}

/**
 * 成功消息类型枚举
 * @enum {string}
 */
export enum SuccessMessageType {
  /** 使用success样式显示成功消息 */
  Success = 'success',
  /** 使用info样式显示成功消息 */
  Info = 'info'
}

export interface ApiResponse<T = unknown> {
  /** 请求是否成功 */
  result: boolean;
  /** 返回的数据 */
  data?: T;
  inspect(inspector: (data: T) => void): void
}
export class ApiListResponse<T> implements ApiResponse<T[]> {
  constructor(
    public count: number,
    public data: T[],
    public result: boolean,
  ) {
  }
  inspect(inspector: (data: T[]) => void): void {
    if (!this.result) {
      return
    }
    inspector(this.data)
  }
}


export type ApiSuccessCallback = (res?:any) => void;
export type ApiFailureCallback = (error?: any) => void;

export interface ApiCallOptions {
  loadingKey?: string; // loading 标识
  successMsg?: string;
  errorMsg?: string;
  onSuccess?: ApiSuccessCallback;
  onError?: ApiFailureCallback;
}

export class ApiCommonResponse<T = unknown> implements ApiResponse<T> {
  constructor(
    public result: boolean,
    public data: T,
    public message: string,
    public status_code: number,
  ) {
  }
  inspect(inspector: (data: T) => void): void {
    if (!this.result) {
      return
    }
    inspector(this.data)
  }
  static create<T>(result: { result: boolean, data: T, message: string, status_code: number }): ApiCommonResponse<T> {
    return new ApiCommonResponse(result.result, result.data, result.message, result.status_code)
  }
}
// interface ApiCommonResponse<T = unknown> {
//   result: boolean;
//   data: T;
//   message: string;
//   status_code: number;
// }


// // types.ts
// export interface ApiResponse {
//   result: boolean;
//   // 可以根据实际响应结构添加更多字段
//   [key: string]: any;
// }


