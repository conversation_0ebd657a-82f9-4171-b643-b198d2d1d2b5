import type { RouteRecordRaw } from 'vue-router';
import { BasicLayout } from '#/layouts';

import { $t } from '#/locales';
import { useBadgeStore } from '#/store/badge';
import Misjudge from '#/views/quality-new/sample-library/index.vue';
// ai复审
const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ant-design:safety-outlined',
      keepAlive: true,
      order: 1000,
      badge: () => useBadgeStore().badgeParentData['/quality_new'],
      badgeVariants: 'destructive',
      title: $t('page.qualityNew.title'),
    },
    name: 'QualityNew',
    path: '/quality_new',
    children: [
      {
        path: 'quality_history',
        component: () => import('#/layouts/child-router.vue'),
        meta: {
          title: $t('page.qualityNew.qualityHistory.title'),
        },
        name: 'QualityHistory',
        children: [
          // 安徽
          {
            path: 'anhui',
            component: () => import('#/views/checklist/task/index.vue'),
            meta: {
              title: $t('page.qualityNew.anhui'),
            },
            name: 'QualityHistoryAnhui',
          },
          // 武汉
          {
            path: 'wuhan',
            component: () => import('#/views/checklist/task/index.vue'),
            meta: {
              title: $t('page.qualityNew.wuhan'),
            },
            name: 'QualityHistoryWuhan',
          },
          // 知道团队
          {
            path: 'knownsec',
            component: () => import('#/views/checklist/task/index.vue'),
            meta: {
              title: $t('page.qualityNew.knownsec'),
            },
            name: 'QualityHistoryKnownsec',
          },
          // 自定义团队
          {
            path: 'custom',
            component: () => import('#/views/checklist/task/index.vue'),
            meta: {
              title: $t('page.qualityNew.custom'),
            },
            name: 'QualityHistoryCustom',
          },
        ],
      },
      {
        path: 'quality_now',
        component: () => import('#/layouts/child-router.vue'),
        meta: {
          title: $t('page.qualityNew.qualityNow.title'),
        },
        name: 'QualityNow',
        children: [
          {
            path: 'anhui',
            component: () => import('#/views/checklist/task/index.vue'),
            meta: {
              title: $t('page.qualityNew.anhui'),
            },
            name: 'QualityNowAnhui',
          },
          {
            path: 'wuhan',
            component: () => import('#/views/checklist/task/index.vue'),
            meta: {
              title: $t('page.qualityNew.wuhan'),
            },
            name: 'QualityNowWuhan',
          },
          {
            path: 'knownsec',
            component: () => import('#/views/checklist/task/index.vue'),
            meta: {
              title: $t('page.qualityNew.knownsec'),
            },
            name: 'QualityNowKnownsec',
          },
        ],
      },
      {
        path: 'misjudge',
        component: Misjudge,
        meta: {
          title: $t('page.qualityNew.misjudge.title'),
        },
        name: 'Misjudge',
      },
      {
        path: 'sample_set',
        component: () => import('#/views/quality-new/sample-setting.vue'),
        meta: {
          title: $t('page.qualityNew.sampleSetting.title'),
        },
        name: 'SampleSet',
      },
    ],
  },
];

export default routes;
