<script lang="ts" setup>
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import TableTemplate from '#/components/table/index.vue';
import File from '#/components/file/index.vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getSandboxKeywordHitList } from '#/api/sandbox-effect';
import { LABEL_MAP, AI_LABEL_TYPE_MAP } from '#/constants/maps';

const route = useRoute();
const tableTemplateRef = ref<any>(null);
const open = ref();
const loading = ref(false);

// 分页配置
const paginationOptions = ref({
  pageSize: 10,
  currentPage: 1,
  layouts: ['PrevPage', 'JumpNumber', 'NextPage', 'Total'],
});

// 表格列配置
const columns: VxeGridProps['columns'] = [
  {
    field: 'status',
    title: '实际状态',
    width: 150,
    align: 'center',
    slots: {
      default: ({ row }) => LABEL_MAP[row.status] || '-',
    },
  },
  {
    field: 'ai_label',
    title: '沙盒状态',
    width: 150,
    align: 'center',
    slots: {
      default: ({ row }) => AI_LABEL_TYPE_MAP[row.ai_label] || '-',
    },
  },
  { field: 'biz', title: '业务编号', align: 'center' },
  { field: 'scene', title: '场景', align: 'center' },
  { field: 'fileinx', title: 'fileID', align: 'center' },
  { field: 'extra_id', title: '附件id', align: 'center' },
  {
    title: '文档名称',
    align: 'center',
    slots: {
      default: 'file',
    },
  },
  {
    field: 'words',
    title: '含有关键词',
    align: 'center',
    slots: {
      default: ({ row }) => row.words || '-',
    },
  },
];

// 查询方法
const queryMethod = async ({
  page,
  pageSize,
}: {
  currentPage: number;
  pageSize: number;
  [key: string]: any;
}) => {
  try {
    loading.value = true;
    const { id, biz, scene, sandbox_task_id, date_time, kind } = route.query;
    console.log(route.query);
    const params = {
      limit: pageSize,
      page: page - 1,
      id: Number(id),
      sandbox_task_id: Number(sandbox_task_id),
      kind: Number(kind),
      biz,
      task_scene: scene,
      date_time,
    };

    const response = await getSandboxKeywordHitList(params);
    const resultData = response?.result
      ? response.data
      : { records: [], count: 0 };

    const formattedList = resultData.records.map((item: any) => {
      const word_list = item.word_list
        ? item.word_list
            .map(
              (w: any) =>
                w.word + (w.decorate ? '+' + w.decorate.join(',') : ''),
            )
            .join('、')
        : '';
      open.value = true;
      return { ...item, words: word_list };
    });

    return {
      items: formattedList,
      total: resultData.count,
    };
  } catch (error) {
    console.error('查询失败:', error);
    return { items: [], total: 0 };
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <div v-show="open">
    <table-template
      ref="tableTemplateRef"
      :columns="columns"
      :query-method="queryMethod"
      :pagination-options="paginationOptions"
      :loading="loading"
    >
      <template #file="{ row }">
        <file :item="row" queue="taskGloble" />
      </template>
    </table-template>
  </div>
</template>
