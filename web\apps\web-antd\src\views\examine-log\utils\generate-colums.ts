import { computed } from 'vue';
import { parseRecordFlag } from '../utils/data-process';
import { formatTimestampToDate } from '#/utils/time';
import { findLabelByValue } from '#/utils/data';

export const generateColumns = (
  bizTypes: any,
  scene: any,
  currentBizType: any,
  allForbidTags: any,
  supplierData: any,
  getItem: any,
  isCommentText: any,
  isCommentImage: any,
) => {
  const dynamicColumns = computed(() => {
    const bt = bizTypes.value.value;
    const columns = [
      {
        type: 'seq',
        width: 70,
        field: 'num',
        title: '序号',
        slots: { default: 'seq' },
        fixed: 'left',
        visible: true,
      },
      {
        field: 'status',
        title: '状态',
        width: 80,
        slots: { default: 'status' },
        fixed: 'left',
        visible: true,
      },
      {
        title: '',
        width: 1,
        slots: { default: 'avatar' },
        visible: bt.isWpsAccount,
      },
      {
        field: 'fileid',
        title: bt.isWatermark ? '水印id' : bt.isComment ? '文本id' : 'fileID',
        width: 160,
        fixed: 'left',
        visible:
          !isCommentImage.value &&
          !bt.isDocer &&
          !bt.isDrive &&
          !bt.isKsformAudit &&
          !bt.isHomeTopic,
      },
      {
        field: 'fileinx',
        title: bt.isHomeComment ? '文本id' : 'fileID',
        slots: { default: 'fileinx' },
        width: 160,
        fixed: 'left',
        visible:
          bt.isHomeComment ||
          bt.isDrive ||
          bt.isKsformAudit ||
          bt.isHomeTopic ||
          bt.isDocerV2 ||
          isCommentImage.value,
      },

      {
        field: 'fileid',
        title: 'fileid',
        width: 160,
        visible: bt.isDocer,
        fixed: 'left',
      },
      {
        field: 'file_url',
        title: '送审头像',
        width: 150,
        slots: { default: 'avatar' },
        visible:
          bt.isWpsAccount &&
          ['avatar', 'avatar_async'].includes(scene.value || ''),
      },
      {
        field: 'content',
        title: '昵称',
        width: 150,
        visible:
          bt.isWpsAccount &&
          !['avatar', 'avatar_async'].includes(scene.value || ''),
      },
      {
        field: 'extra_id',
        title: '附件id',
        width: 120,
        visible:
          bt.isDrive ||
          bt.isKsformAudit ||
          bt.isHomeTopic ||
          bt.isHomeComment ||
          isCommentImage.value,
      },
      {
        field: 'scene',
        title: '场景',
        width: 100,
        slots: { default: 'scene' },
        visible:
          (bt.isDrive ||
            bt.isComment ||
            bt.isImageDruve ||
            bt.isHomeTopic ||
            bt.isKsformAudit) &&
          !bt.isWatermark,
      },
      {
        field: 'fname',
        title: bt.isDocer ? '文件名' : bt.isHomeComment ? '文本名称' : '名称',
        width: bt.isHomeComment ? 380 : 300,
        slots: { default: 'fname' },
        visible: !bt.isWpsAccount || bt.isHomeComment,
      },
      {
        title: '附件名称',
        width: 380,
        slots: { default: 'extra_name' },
        visible: bt.isHomeComment,
      },
      {
        field: 'content',
        title: bt.isWatermark ? '水印内容' : '文本内容',
        width: 250,
        visible: isCommentText.value,
        slots: { default: 'content' },
      },
      {
        field: 'record_flag',
        title: '文档类型',
        width: 120,
        formatter: ({ cellValue }) => parseRecordFlag(cellValue),
        visible: !isCommentText.value && !bt.isWpsAccount && !bt.isDocer,
      },
      {
        field: 'fileinx',
        title: '文档id',
        width: 270,
        visible: bt.isHomeComment,
      },
      {
        field: 'f_fileid',
        title: '文档id',
        width: 270,
        visible: !bt.isHomeComment && isCommentText.value,
      },
      {
        field: 'file_level',
        title: '文档等级',
        width: 100,
        slots: { default: ({ row }) => `${row.ex_status_obj.fl || 0}级` },
        visible: !bt.isDocer,
      },
      {
        field: 'type',
        title: '操作标签',
        width: 200,
        slots: { default: 'type' },
        visible: !bt.isDocer,
      },
      {
        field: 'creator_id',
        title: '初始ID',
        width: 110,
        visible: true,
        slots: { default: ({ row }) => row.creator_id || '' },
      },
      {
        field: 'userid',
        title: bt.isDocer ? '作者' : 'UserID',
        width: 110,

        visible: true,
      },
      {
        field: 'type',
        title: '类型',
        width: 120,
        formatter: ({ cellValue }) =>
          findLabelByValue(allForbidTags.value, cellValue, 'type', 'name'),
        visible: bt.isDocer,
      },
      {
        field: 'pv_count',
        title: 'PV',
        width: 80,
        visible: !bt.isKdocsComment,
        slots: { default: ({ row }) => row.pv_count || 0 },
      },
      {
        field: 'uv_count',
        title: 'UV',
        width: 80,
        visible: !bt.isKdocsComment,
        slots: { default: ({ row }) => row.uv_count || 0 },
      },
      {
        field: 'gpv_count',
        title: 'GPV',
        width: 80,
        visible: !bt.isKdocsComment,
        slots: { default: ({ row }) => row.gpv_count || 0 },
      },
      {
        field: 'link_ctime',
        title: '发布时间',
        width: 160,
        slots: { default: 'link_ctime' },
        visible: true,
      },
      {
        field: 'supplier',
        title: '操作团队',
        width: 100,
        slots: {
          default: ({ row }) =>
            supplierData.value.find((item) => item.supplier === row.supplier)
              ?.mark || '',
        },
        visible: !isCommentText.value && !bt.isDocer,
      },
      {
        field: 'company',
        title: '企业ID',
        width: 110,
        visible: !bt.isDocer,
      },
      {
        field: 'real_name',
        title: '操作人',
        width: 130,
        visible: true,
      },
      {
        field: 'modify_time',
        title: '操作时间',
        width: 160,
        formatter: ({ cellValue }) => formatTimestampToDate(cellValue),
        visible: true,
      },
      {
        field: 'actions',
        title: '操作',

        width:
          currentBizType.value.isDrive && (getItem('flag') & 0x100) === 0x100
            ? 270
            : 180,
        fixed: 'right',
        slots: { default: 'actions' },
        visible: true,
      },
    ];

    return columns.filter((column) => column.visible);
  });

  return dynamicColumns;
};
export const tabOptions = {
  enable: true,
  list: [
    { key: '2', title: '精确搜索' },
    { key: '1', title: '组合搜索' },
    { key: '3', title: '全文搜索' },
  ],
};
