/**
 * 根据 key 获取嵌套数据
 * @param data 数据对象
 * @param key 单个 key（可能是单层或多层，例如 "pre_black_result" 或 "words.w200"）
 * @returns 对应的数据值
 */
export const getNestedData = (data: Record<string, any>, key: any): any => {
  if (!key) return
  if (!key.includes(".")) {
    // 单层 key，直接返回 data[key]
    return data[key];
  }
  // 多层 key，使用 reduce 获取嵌套属性
  return key.split(".").reduce((obj, k) => (obj ? obj[k] : undefined), data);
};

/**
 * 根据给定的值查找对应的标签，支持树形结构数据，并返回完整的层级路径
 * @param {Array} arr - 要搜索的数组（可以是普通数组或树形结构）
 * @param {*} targetValue - 要查找的目标值
 * @param {string} [valueKey='value'] - 用于比较值的键名
 * @param {string} [labelKey='label'] - 要返回的标签键名
 * @param {*} [defaultValue=undefined] - 未找到时返回的默认值
 * @param {string} [childrenKey='children'] - 子节点的键名（用于树形结构）
 * @param {string} [separator='/'] - 路径分隔符
 * @param {boolean} [returnValuePath=false] - 是否返回值路径数组而不是标签路径字符串
 * @returns {string|Array|*} 找到的标签路径字符串、值路径数组或默认值
 */
export function findLabelByValue(
  arr: any[],
  targetValue: any,
  valueKey = 'value',
  labelKey = 'label',
  defaultValue = undefined,
  childrenKey = 'children', // 表示子节点的key
  separator = '/', // 用于拼接路径的分隔符
  returnValuePath = false // 是否返回值路径数组
) {
  // 参数校验：确保arr是数组
  if (!Array.isArray(arr)) {
    return defaultValue;
  }

  // 递归查找函数，携带当前路径
  const findRecursively = (nodes: any[], parentLabelPath = '', parentValuePath: any[] = []): string | any[] | undefined => {
    for (const node of nodes) {
      if (!node) continue;

      // 当前节点的完整标签路径（如果是根节点，则不拼接父路径）
      const currentLabelPath = parentLabelPath ? `${parentLabelPath}${separator}${node[labelKey]}` : node[labelKey];
      // 当前节点的值路径数组
      const currentValuePath = [...parentValuePath, node[valueKey]];

      // 如果找到目标值，根据参数返回标签路径或值路径
      if (node[valueKey] == targetValue) {
        return returnValuePath ? currentValuePath : currentLabelPath;
      }

      // 如果有子节点，递归查找
      if (node[childrenKey] && Array.isArray(node[childrenKey])) {
        const foundInChildren = findRecursively(node[childrenKey], currentLabelPath, currentValuePath);
        if (foundInChildren !== undefined) {
          return foundInChildren;
        }
      }
    }
    return undefined;
  };

  const result = findRecursively(arr);
  return result !== undefined ? result : defaultValue;
}


/**
 * 检查对象中所有属性值是否均为空值
 * 空值定义包括：null、undefined、空数组([])、空对象({})
 *
 * @param {Object} obj - 需要检查的对象
 * @returns {boolean} 返回检查结果：
 *                    true - 所有属性值均为空值 或 对象本身为空
 *                    false - 存在至少一个非空属性值

 */
export function isAllEmptyValues(obj: any) {
  // 类型安全检查
  if (typeof obj !== 'object' || obj === null) {
    throw new TypeError('Expected an object for parameter obj');
  }

  // 处理空对象的情况
  if (Object.keys(obj).length === 0) return true;

  // 遍历所有属性值进行检查
  return Object.values(obj).every(value => {
    // 1. 检查 null 和 undefined
    if (value === null || value === undefined) return true;

    // 2. 检查空数组
    if (Array.isArray(value) && value.length === 0) return true;

    // 3. 检查空对象
    if (
      typeof value === 'object' &&
      !Array.isArray(value) &&
      Object.keys(value).length === 0
    ) return true;

    // 4. 其他情况均视为非空值
    return false;
  });
}

interface TreeNode {
  [key: string]: any; // 通用树节点接口
  children?: TreeNode[];
}

type FilterTreeOptions = {
  field: string;          // 要过滤的字段名
  value: any;             // 要匹配的字段值
  filterFirstLevel?: boolean; // 是否过滤第一级 (默认false)
  keepChildrenIfParentMatch?: boolean; // 父节点匹配时是否保留所有子节点 (默认false)
};



export function filterTree(
  data: TreeNode[] | undefined,
  options: FilterTreeOptions
): TreeNode[] {
  // 防御性编程：处理空值
  if (!data) return [];

  const {
    field,
    value,
    filterFirstLevel = false,
    keepChildrenIfParentMatch = false
  } = options;

  // 处理第一级过滤
  const firstLevelNodes = filterFirstLevel
    ? data.filter(node => node[field] === value)
    : [...data]; // 创建浅拷贝

  return firstLevelNodes.map(node =>
    processTreeNode(node, field, value, keepChildrenIfParentMatch)
  );
}

function processTreeNode(
  node: TreeNode,
  field: string,
  value: any,
  keepChildrenIfParentMatch: boolean
): TreeNode {
  // 检查父节点是否匹配
  const isNodeMatch = node[field] === value;

  // 如果设置保留匹配父节点的所有子节点，且当前节点匹配
  if (keepChildrenIfParentMatch && isNodeMatch) {
    return {
      ...node,
      children: node.children?.map(child =>
        processTreeNode(child, field, value, true) // 子节点继承保留设置
      )
    };
  }

  // 常规处理：过滤子节点
  const filteredChildren = node.children
    ?.filter(child => child[field] === value)
    ?.map(child =>
      processTreeNode(child, field, value, keepChildrenIfParentMatch)
    );

  // 返回处理后的节点
  return {
    ...node,
    children: filteredChildren?.length ? filteredChildren : undefined // 空数组转为undefined
  };
}