// 文档状态
export const DOC_MANUAL_STATUS: { [key: string]: string } = {
  ok: '通过',
  forbid: '拒绝',
  verifying: '机审中',
  pass: '通过',
  manual: '怀疑',
  manual_verifying: '未操作',
};

export const DOC_MANUAL_COLOR: { [key: string]: string } = {
  ok: 'green',
  forbid: 'red',
  verifying: 'grey',
  pass: 'green',
  manual: 'orange',
  manual_verifying: 'grey',
};

export const OPERATE_MANUAL_STAUTS: { [key: string]: string } = {
  ok: '通过',
  forbid: '拒绝',
};

export const IMAGE_LABEL_MAP: { [key: string]: string } = {
  pass: '通过',
  forbid: '封禁',
  manual: '怀疑',
};
export const NLP_STATUS: { [key: string]: string } = {
  pass: '通过',
  forbid: '拒绝',
  manual: '怀疑',
};

export const PIPE_STATUS_TYPE: { [key: string]: string } = {
  forbid: '拦截',
  pass: '通过',
};

export const ALL: { [key: number]: string } = {
  0: '全部',
};

export const  BBS_OPT_TYPE : { [key: string]: string } = {
  '1': '封禁',
  '2': '正常',
};

// 状态映射
export const STATUS_TEXT_MAP: { [key: string]: string } = {
  pass: '通过',
  manual: '推人工',
  forbid: '封禁',
  forbid_coral: '封禁',
  forbid_manual: '推人工',
  default: '异常',
};

// 状态颜色映射
export const STATUS_COLOR_MAP: { [key: string]: string } = {
  pass: 'success',
  manual: 'warning',
  forbid: 'error',
  default: 'default',
};
export const DEVICE_TYPE: { [key: string]: string } = {
  ai: '机审',
  manual: '人审',
};

//
export const SUPPLIER_MAP: { [key: string]: string } = {
  shumei: '数美',
  cntv: '央视',
  self: '自建',
};

export const ENABLE_MAP: { [key: string]: string } = {
  '1': '启用',
  '0': '禁用',
};

export const QUALITY_STATUS_MAP: { [key: string]: string } = {
  ok: '通过',
  forbid: '拒绝',
};
export const AILABEL_MAP = (label: string) => {
  const mapping: Record<string, string> = {
    forbid: '封禁',
    forbid_manual: '先审后发',
    manual: '先发后审',
    pass: '通过',
  };
  return mapping[label] || label;
};

export const FILTER_STATUS_MAP: { [key: string]: string } = {
  ok: '通过',
  forbid: '拒绝',
  verifying: '机审中',
};
