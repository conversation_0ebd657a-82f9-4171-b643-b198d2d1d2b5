interface ApiCallOptions {
  onSuccess?: (presignKey: string) => void; // 接受一个字符串参数
  onError?: (error: Error) => void;
}

export function usePresignedUpload() {
  /**
   * 统一处理文件上传 - 参考原来的实现方式
   * @param apiFn 获取预签名的API函数
   * @param file 要上传的文件
   * @param options 配置选项
   */
  const handleUpload = async (
    apiFn: (params: any) => Promise<any>,
    file: File,
    options: ApiCallOptions = {},
  ): Promise<boolean> => {
    const { onSuccess, onError } = options;

    try {
      console.log('开始上传文件:', file.name);

      // 1. 获取预签名URL
      const presignRes = await apiFn({
        file_name: file.name,
        expire: false,
      });

      console.log('预签名响应:', presignRes);

      if (!presignRes?.result) {
        throw new Error('获取预签名地址失败');
      }

      let { url: presignUrl, key: presignKey } = presignRes.data;
      // 修复Mixed Content问题：将HTTP转换为HTTPS
      if (presignUrl.startsWith('http://')) {
        presignUrl = presignUrl.replace('http://', 'https://');
        console.log('已将HTTP URL转换为HTTPS');
      }

      console.log('预签名URL:', presignUrl);
      console.log('文件Key:', presignKey);

      // 2. 使用FormData上传（经验证这种方式更稳定）
      let uploadResponse;
      let uploadSuccess = false;

      try {
        console.log('使用FormData上传文件...');
        const formData = new FormData();
        formData.append('file', file);

        uploadResponse = await fetch(presignUrl, {
          method: 'PUT',
          headers: {
            'x-amz-acl': 'private',
            'Content-Type': 'application/octet-stream',
          },
          body: formData,
        });

        if (uploadResponse.ok) {
          console.log('FormData上传成功');
          uploadSuccess = true;
        } else {
          console.log('FormData上传失败，状态:', uploadResponse.status);
        }
      } catch (formDataError) {
        console.log('FormData上传出错:', formDataError instanceof Error ? formDataError.message : formDataError);
      }

      // 检查最终结果
      if (!uploadSuccess || !uploadResponse || !uploadResponse.ok) {
        let errorText = '';
        if (uploadResponse) {
          try {
            errorText = await uploadResponse.text();
          } catch (e) {
            errorText = '无法读取错误响应';
          }
        }
        console.error('所有上传方式都失败了，最后响应:', errorText);
        throw new Error(`文件上传到存储服务失败: ${uploadResponse?.status || '未知'} ${uploadResponse?.statusText || ''}`);
      }

      console.log('上传响应状态:', uploadResponse.status, uploadResponse.statusText);

      console.log('文件上传成功，调用成功回调');
      onSuccess?.(presignKey);
      return true;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('未知错误');

      console.error('文件上传流程错误:', error);
      onError?.(error);
      return false;
    }
  };

  return {
    handleUpload,
  };
}
