import { requestClient } from '#/api/request';

const commonHead = '/manual';
export function getAccountBriefList(data: any) {
  //获取笔试人员信息
  return requestClient.get(`${commonHead}/account/get_account_brief_list`, {
    data,
  });
}
// 保存颜色
export function saveTheme(data: any) {
  return requestClient.post(`${commonHead}/account/update_custom_info`, data);
}

// 保存高亮关键词
export function saveHighlightKeyword(data: any) {
  return requestClient.post(`${commonHead}/account/update_keyword`,  data );
}
// 定时请求刷新签名
export function refreshSign(data: any) {
  return requestClient.post(`${commonHead}/trace/refresh_sign`, data);
}

// 获取未读通知提醒
export function getUncheckTip(data: any) {
  return requestClient.get(`${commonHead}/account/list_unread_message`, { data });
}

// 设置消息已读
export function setMsgRead(data: any) {
  return requestClient.post(`${commonHead}/account/read_message`, data);
}
