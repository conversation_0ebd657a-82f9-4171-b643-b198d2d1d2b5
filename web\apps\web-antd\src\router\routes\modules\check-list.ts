import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';
import { useBadgeStore } from '#/store/badge';
const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'lucide:layout-dashboard',
      order: -1,
      title: $t('page.checkList.title'),
      badge: () => useBadgeStore().badgeParentData['/checklist'],
      badgeVariants: 'destructive',
    },
    name: 'CheckList',
    path: '/checklist',
    children: [
      {
        name: 'Task',
        path: 'task',
        component: () => import('#/views/checklist/task/index.vue'),
        meta: {
          badge: () => useBadgeStore().badgeData['/checklist/task'],
          badgeVariants: 'destructive',
          title: $t('page.checkList.task.default'),
        },
      },
      {
        name: 'Log',
        path: 'task/drivecore_log',
        component: () => import('#/views/checklist/task/log.vue'),
        meta: {
          hideInMenu: true, // 隐藏在菜单中
          title: $t('page.checkList.task.log'),
        },
      },
      {
        name: 'NicknameLog',
        path: 'task/nickname_log',
        component: () => import('#/views/checklist/task/nickname-log.vue'),
        meta: {
          hideInMenu: true, // 隐藏在菜单中
          title: $t('page.checkList.task.log'),
        },
      },
      {
        name: 'CommentLog',
        path: 'task/comment_log',
        component: () => import('#/views/checklist/task/comment-log.vue'),
        meta: {
          hideInMenu: true, // 隐藏在菜单中
          title: $t('page.checkList.task.log'),
        },
      },
      {
        name: 'BlockTask',
        path: 'block',
        component: () => import('#/views/checklist/task/index.vue'),
        meta: {
          title: $t('page.checkList.task.block'),
          badgeVariants: 'destructive',
          badge: () => useBadgeStore().badgeData['/checklist/block'],
        },
      },
      {
        name: 'UrgentTask',
        path: 'urgent',
        component: () => import('#/views/checklist/task/index.vue'),
        meta: {
          title: $t('page.checkList.task.urgent'),
          badge: () => useBadgeStore().badgeData['/checklist/urgent'],
          badgeVariants: 'destructive',
        },
      },
      {
        name: 'HandoverTask',
        path: 'handOver',
        component: () => import('#/views/checklist/task/index.vue'),
        meta: {
          title: $t('page.checkList.task.handover'),
          badge: () => useBadgeStore().badgeData['/checklist/handOver'],
          badgeVariants: 'destructive',
        },
      },
      {
        name: 'PVcheckTask',
        path: 'pvCheck',
        component: () => import('#/views/checklist/task/index.vue'),
        meta: {
          title: $t('page.checkList.task.pvcheck'),
          badge: () => useBadgeStore().badgeData['/checklist/pvCheck'],
          badgeVariants: 'destructive',
        },
      },
      {
        name: 'Floor',
        path: 'floor',
        component: () => import('#/views/checklist/task/index.vue'),
        meta: {
          title: $t('page.checkList.task.floor'),
          badge: () => useBadgeStore().badgeData['/checklist/floor'],
          badgeVariants: 'destructive',
        },
      },
      {
        name: 'AccountSafe',
        path: 'accountSafe',
        component: () => import('#/views/checklist/account/index.vue'),
        meta: {
          title: $t('page.checkList.task.accountSafe'),
          badge: () => useBadgeStore().badgeData['/checklist/accountSafe'],
          badgeVariants: 'destructive',
        },
      },
    ],
  },
  {
    component: BasicLayout,
    meta: {
      icon: 'ant-design:video-camera-outlined',
      order: -1,
      title: $t('page.mediaList.title'),
      badge: () => useBadgeStore().badgeParentData['/mediaList'],
      badgeVariants: 'destructive',
    },
    name: 'MediaList',
    path: '/mediaList',
    children: [
      {
        name: 'MediaTask',
        path: 'mediaTask',
        component: () => import('#/views/checklist/task/index.vue'),
        meta: {
          badge: () => useBadgeStore().badgeData['/mediaList/mediaTask'],
          badgeVariants: 'destructive',
          title: $t('page.mediaList.task.default'),
        },
      },
      {
        name: 'CannotJudgeMediaTask',
        path: 'cannotJudgeMediaTask',
        component: () => import('#/views/checklist/task/index.vue'),
        meta: {
          badge: () =>
            useBadgeStore().badgeData['/mediaList/cannotJudgeMediaTask'],
          badgeVariants: 'destructive',
          title: $t('page.mediaList.task.cannotJudgeMediaTask'),
        },
      },
      {
        name: 'EnsureMediaTask',
        path: 'ensureMediaTask',
        component: () => import('#/views/checklist/task/index.vue'),
        meta: {
          badge: () => useBadgeStore().badgeData['/mediaList/ensureMediaTask'],
          badgeVariants: 'destructive',
          title: $t('page.mediaList.task.ensureMediaTask'),
        },
      },
      {
        name: 'AppvTask',
        path: 'appvTask',
        component: () => import('#/views/checklist/task/index.vue'),
        meta: {
          badge: () => useBadgeStore().badgeData['/mediaList/appvTask'],
          badgeVariants: 'destructive',
          title: $t('page.mediaList.task.appvTask'),
        },
      },
      {
        name: 'InformMediaTask',
        path: 'mediaInformTask',
        component: () => import('#/views/checklist/task/index.vue'),
        meta: {
          badge: () => useBadgeStore().badgeData['/mediaList/mediaInformTask'],
          badgeVariants: 'destructive',
          title: $t('page.mediaList.task.mediaInformTask'),
        },
      },
      {
        name: 'AppealMediaTask',
        path: 'mediaAppealTask',
        component: () => import('#/views/checklist/task/index.vue'),
        meta: {
          badge: () => useBadgeStore().badgeData['/mediaList/mediaAppealTask'],
          badgeVariants: 'destructive',
          title: $t('page.mediaList.task.mediaAppealTask'),
        },
      },
      {
        name: 'ResureApPvTask',
        path: 'resureApPvTask',
        component: () => import('#/views/checklist/task/index.vue'),
        meta: {
          badge: () => useBadgeStore().badgeData['/mediaList/resureApPvTask'],
          badgeVariants: 'destructive',
          title: $t('page.mediaList.task.resureApPvTask'),
        },
      },
    ],
  },
  {
    component: BasicLayout,
    meta: {
      icon: 'ant-design:file-text-outlined',
      order: -1,
      title: $t('page.drivecoreRecheck.title'),
      badge: () => useBadgeStore().badgeParentData['/cy_recheck'],
      badgeVariants: 'destructive',
    },
    name: 'CyCheck',
    path: '/cy_recheck',
    children: [
      {
        name: 'CyEnsure',
        path: 'cy_ensure',
        component: () => import('#/views/checklist/task/index.vue'),
        meta: {
          badge: () => useBadgeStore().badgeData['/cy_recheck/cy_ensure'],
          badgeVariants: 'destructive',
          title: $t('page.drivecoreRecheck.task.cyEnsure'),
        },
      },
      {
        name: 'HfEnsure',
        path: 'hf_ensure',
        component: () => import('#/views/checklist/task/index.vue'),
        meta: {
          badge: () => useBadgeStore().badgeData['/cy_recheck/hf_ensure'],
          badgeVariants: 'destructive',
          title: $t('page.drivecoreRecheck.task.hfEnsure'),
        },
      },
      {
        name: 'DrivecoreEnsureList',
        path: 'ensure_list', // 待确认列表 这里要改
        component: () => import('#/views/ensure/drivecore-ensure.vue'),
        meta: {
          title: $t('page.drivecoreRecheck.task.ensureList'),
        },
      },
      {
        name: 'DrivecoreEnsureLog',
        path: 'ensure_list/ensure_log', // 已确认列表
        component: () => import('#/views/ensure/ensure-log.vue'),
        meta: {
          title: $t('page.drivecoreRecheck.task.ensureLog'),
          hideInMenu: true,
        },
      },
    ],
  },
  {
    component: BasicLayout,
    meta: {
      icon: 'ant-design:filter-outlined',
      order: -1,
      title: $t('page.recheck.title'),
      badge: () => useBadgeStore().badgeParentData['/recheck'],
      badgeVariants: 'destructive',
    },
    name: 'Recheck',
    path: '/recheck',
    children: [
      {
        name: 'FocusRecheck',
        path: 'focus_recheck',
        component: () => import('#/views/checklist/task/index.vue'),
        meta: {
          badge: () => useBadgeStore().badgeData['/recheck/focus_recheck'],
          badgeVariants: 'destructive',
          title: $t('page.recheck.task.focusRcheck'),
        },
      },
      // {
      //   name: 'Task',
      //   path: '/recheck/recheck_ensure',
      //   component: () => import('#/views/checklist/task/index.vue'),
      //   meta: {
      //     badge:() => useBadgeStore().badgeData['/checklist/task'],
      //     badgeVariants: 'destructive',
      //     title: $t('page.recheck.task.recheckEnsure'),
      //   },
      // },
      {
        name: 'PreviewIncapable',
        path: 'preview_incapable',
        component: () => import('#/views/checklist/task/index.vue'),
        meta: {
          badge: () => useBadgeStore().badgeData['/recheck/preview_incapable'],
          badgeVariants: 'destructive',
          title: $t('page.recheck.task.previewIncapable'),
        },
      },
      // {
      //   name: 'Task',
      //   path: '/recheck/incapable_checklist',
      //   component: () => import('#/views/checklist/task/index.vue'),
      //   meta: {
      //     badge:() => useBadgeStore().badgeData['/checklist/task'],
      //     badgeVariants: 'destructive',
      //     title: $t('page.checkList.recheck.incapableChecklist'),
      //   },
      // },
    ],
  },

  {
    component: BasicLayout,
    meta: {
      icon: 'ant-design:alert-outlined',
      order: -1,
      title: $t('page.ensure.title'),
      badge: () => useBadgeStore().badgeParentData['/ensure'],
      badgeVariants: 'destructive',
    },
    name: 'Ensure',
    path: '/ensure',
    children: [
      {
        name: 'EnsureSerious',
        path: 'ensure_serious',
        component: () => import('#/views/checklist/task/index.vue'),
        meta: {
          badge: () => useBadgeStore().badgeData['/ensure/ensure_serious'],
          badgeVariants: 'destructive',
          title: $t('page.ensure.task.ensureSerious'),
        },
      },
      {
        name: 'EnsureOther',
        path: 'ensure_other',
        component: () => import('#/views/checklist/task/index.vue'),
        meta: {
          badge: () => useBadgeStore().badgeData['/checklist/task'],
          badgeVariants: 'destructive',
          title: $t('page.ensure.task.ensureOther'),
        },
      },
      {
        name: 'PvEnsureDanger',
        path: 'pv_ensure_danger',
        component: () => import('#/views/checklist/task/index.vue'),
        meta: {
          badge: () => useBadgeStore().badgeData['/ensure/pv_ensure_danger'],
          badgeVariants: 'destructive',
          title: $t('page.ensure.task.pvEnsureDanger'),
        },
      },
      {
        name: 'PvEnsureNormal',
        path: 'pv_ensure_normal',
        component: () => import('#/views/checklist/task/index.vue'),
        meta: {
          badge: () => useBadgeStore().badgeData['/ensure/pv_ensure_normal'],
          badgeVariants: 'destructive',
          title: $t('page.ensure.task.pvEnsureNormal'),
        },
      },
      {
        name: 'EnsureList',
        path: 'ensure_list', //待确认列表
        component: () => import('#/views/ensure/ensure-list.vue'),
        meta: {
          title: $t('page.ensure.task.ensureList'),
        },
      },
    ],
  },
  {
    component: BasicLayout,
    meta: {
      icon: 'ant-design:bulb-outlined',
      order: -1,
      title: $t('page.warn_white_name.title'),
      badge: () => useBadgeStore().badgeData['/warn_white_name'],
      badgeVariants: 'destructive',
    },
    name: 'WarnWhiteName',
    path: '/warn_white_name',
    children: [
      {
        path: '',
        component: () => import('#/views/checklist/task/index.vue'),
        meta: {
          title: $t('page.warn_white_name.title'),
          hideInMenu: true, // 隐藏在菜单中
        },
      },
    ],
  },
  // 举报申诉
  {
    component: BasicLayout,
    meta: {
      icon: 'ant-design:whats-app-outlined',
      order: -1,
      title: $t('page.focus.title'),
      badge: () => useBadgeStore().badgeParentData['/focus'],
      badgeVariants: 'destructive',
    },
    name: 'Focus',
    path: '/focus',
    children: [
      {
        name: 'Informed',
        path: 'informed',
        component: () => import('#/views/checklist/task/index.vue'),
        meta: {
          title: $t('page.focus.task.informed'),
          badge: () => useBadgeStore().badgeData['/focus/informed'],
          badgeVariants: 'destructive',
        },
      },
      {
        name: 'Appeal',
        path: 'appeal',
        component: () => import('#/views/checklist/task/index.vue'),
        meta: {
          title: $t('page.focus.task.appeal'),
          badge: () => useBadgeStore().badgeData['/focus/appeal'],
          badgeVariants: 'destructive',
        },
      },
      {
        name: 'AppealHighPV',
        path: 'appealHighPV',
        component: () => import('#/views/checklist/task/index.vue'),
        meta: {
          title: $t('page.focus.task.appealHighPV'),
          badge: () => useBadgeStore().badgeData['/focus/appealHighPV'],
          badgeVariants: 'destructive',
        },
      },
    ],
  },
  // 舆情回捞
  {
    component: BasicLayout,
    meta: {
      icon: 'ant-design:clear-outlined',
      order: -1,
      title: $t('page.sentiment.title'),
      badge: () => useBadgeStore().badgeParentData['/sentiment'],
      badgeVariants: 'destructive',
    },
    name: 'Sentiment',
    path: '/sentiment',
    children: [
      {
        name: 'SentimentCheck',
        path: 'sentiment_check',
        component: () => import('#/views/checklist/task/index.vue'),
        meta: {
          badge: () => useBadgeStore().badgeData['/checklist/task'],
          badgeVariants: 'destructive',
          title: $t('page.sentiment.task.check'),
        },
      },
      // {
      //   name: 'Task',
      //   path: '/sentiment/back_pull', // 回捞列表
      //   component: () => import('#/views/checklist/task/index.vue'),
      //   meta: {
      //     badge:() => useBadgeStore().badgeData['/checklist/task'],
      //     badgeVariants: 'destructive',
      //     title: $t('page.checkList.task.default'),
      //   },
      // },
      // {
      //   name: 'Task',
      //   path: '/mediaList/text_sentiment',
      //   component: () => import('#/views/checklist/task/index.vue'),
      //   meta: {
      //     badge:() => useBadgeStore().badgeData['/checklist/task'],
      //     badgeVariants: 'destructive',
      //     title: $t('page.checkList.task.default'),
      //   },
      // },
    ],
  },
];

export default routes;
