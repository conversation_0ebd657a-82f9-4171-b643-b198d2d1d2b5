<script lang="ts" setup>
import { computed, ref, watch } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import { useQuery } from '@tanstack/vue-query';
import { Select } from 'ant-design-vue';

import { queryEntrance } from '#/api/platform';

const value = defineModel<any[] | string>();
const searchValue = ref<any[]>([]);

// 使用 useQuery 获取数据（只请求一次）
const { data: apiData, isSuccess } = useQuery({
  queryKey: ['entranceData'],
  queryFn: () => queryEntrance({ limit: -1, type: 2 }),
  select: (res) => res.data.data || [],
});
watch(
  () => value.value,
  (v) => {
    // 如果v是数组，则将v赋值给searchValue
    if (Array.isArray(v)) {
      searchValue.value = v;
    }
  },
);
// 添加接口
const addInterface = () => {
  const newValue = [...searchValue.value, { id: '' }];
  searchValue.value = newValue;
  handleApiChange()
};

// 移除接口
const removeInterface = (index: number) => {
  const newValue = searchValue.value.filter((_, i) => i !== index);
  searchValue.value = newValue;
  handleApiChange()
};

// 更新单个接口的值
const handleApiChange = () => {
  value.value = searchValue.value;
};
// 计算已选择的ID（除了当前项）
const selectedIds = computed(() => {
  if (Array.isArray(searchValue.value)) {
    return searchValue.value.map((item) => item.id).filter(Boolean);
  }
  return [];
});
// 本地数据获取函数（使用缓存的数据）
const fetchApi = () => {
  // 确保数据已加载成功
  if (isSuccess.value && apiData.value) {
    return Promise.resolve(
      apiData.value.map((item: any) => ({
        ...item,
        disabled: selectedIds.value.includes(item.id),
      })),
    );
  }
  return Promise.resolve(apiData.value || []);
};
const filterOptions = (input: string, option: any) => {
  return option.label.toLowerCase().includes(input.toLowerCase());
};
</script>

<template>
  <div class="flex w-full flex-col gap-4">
    <div class="flex w-full">
      <a-button type="primary" @click="addInterface"> +绑定权限 </a-button>
    </div>
    <div
      v-if="isSuccess"
      class="flex max-h-44 w-full flex-col gap-2 overflow-scroll"
    >
      <div
        v-for="(item, index) in searchValue"
        :key="item.id"
        class="interface-row mb-1 grid w-full grid-cols-12 items-center gap-2"
      >
        <div class="col-span-1 whitespace-nowrap text-left">
          <span class="font-weight-300 text-sm">接口：</span>
        </div>
        <div class="col-span-10">
          <api-component
            v-model:model-value="item.id"
            :api="fetchApi"
            :component="Select"
            :filter-option="filterOptions"
            :immediate="true"
            class="w-full"
            label-field="path"
            model-prop-name="value"
            show-search
            value-field="id"
            @change="handleApiChange"
          />
        </div>
        <div class="col-span-1 flex items-center justify-center">
          <iconify-icon
            class="text-red-500"
            icon="ant-design:minus-circle-outlined"
            @click="() => removeInterface(index)"
          />
        </div>
      </div>
    </div>
  </div>
</template>
