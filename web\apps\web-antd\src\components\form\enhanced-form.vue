<script lang="ts" setup>
import type { FormInstance } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';

import { computed, onMounted, provide, reactive, ref, unref, watch } from 'vue';

import {
  AutoComplete,
  Cascader,
  Checkbox,
  DatePicker,
  FormItem,
  Input,
  InputNumber,
  Mentions,
  Radio,
  Rate,
  Select,
  Slider,
  Switch,
  TimePicker,
  Transfer,
  TreeSelect,
  Upload,
} from 'ant-design-vue';
import dayjs from 'dayjs';

/**
 * 表单字段配置接口
 */
export interface EnhancedFormSchema {
  /** 字段名称 */
  fieldName: string;
  /** 字段标签 */
  label?: string;
  /** 是否必填 */
  required?: boolean;
  /** 验证规则 */
  rules?: Rule | Rule[];
  /** 组件类型或组件实例 */
  component: any | string;
  /** 组件属性 */
  componentProps?: Record<string, any>;
  /** 表单项类名 */
  formItemClass?: string;
  /** 标签类名 */
  labelClass?: string;
  /** 是否禁用自动事件绑定 */
  disableAutoBinding?: boolean;
  /**
   * 组件的model属性名，用于自定义v-model的属性名
   * 例如: 'value' - 使用v-model:value
   *      'checked' - 使用v-model:checked
   *      'open' - 使用v-model:open
   * 不设置时会根据组件类型自动判断
   *
   * 使用示例:
   * {
   *   fieldName: 'showDrawer',
   *   component: 'Drawer',
   *   modelPropName: 'open',  // 使用v-model:open
   *   // ...其他配置
   * }
   */
  modelPropName?: string;
  /** 字段依赖关系 */
  dependencies?: {
    /** 动态组件属性 */
    componentProps?: (values: Record<string, any>) => Record<string, any>;
    /** 禁用条件 */
    disabled?: (values: Record<string, any>) => boolean;
    /** 显示条件 */
    show?: (values: Record<string, any>) => boolean;
    /** 触发字段 */
    triggerFields?: string[];
  };
  /** 默认值 */
  defaultValue?: any;
  /** 帮助文本 */
  help?: string;
  /** 字段描述 */
  description?: string;
}

/**
 * 表单配置接口
 */
export interface EnhancedFormProps {
  /** 表单字段配置数组 */
  schema: EnhancedFormSchema[];
  /** 表单布局 */
  layout?: 'horizontal' | 'inline' | 'vertical';
  /** 标签宽度（像素），设置为 'auto' 时自动计算 */
  labelWidth?: 'auto' | number;
  /** 标签列配置 */
  labelCol?: { offset?: number; span?: number };
  /** 输入框列配置 */
  wrapperCol?: { offset?: number; span?: number };
  /** 是否启用标签对齐 */
  labelAlign?: boolean;
  /** 表单初始值 */
  initialValues?: Record<string, any>;
  /** 是否显示默认操作按钮 */
  showDefaultActions?: boolean;
  /** 提交按钮文本 */
  submitText?: string;
  /** 重置按钮文本 */
  resetText?: string;
  /** 表单容器类名 */
  formContainerClass?: string;
  /** 表单类名 */
  wrapperClass?: string;
  /** 操作区域类名 */
  actionWrapperClass?: string;
  /** 公共组件属性 */
  commonConfig?: {
    componentProps?: Record<string, any>;
    labelWidth?: number;
  };
}

const props = withDefaults(defineProps<EnhancedFormProps>(), {
  schema: () => [],
  layout: 'horizontal',
  labelWidth: 'auto',
  labelCol: () => ({ span: 6 }),
  wrapperCol: () => ({ span: 18 }),
  labelAlign: true,
  initialValues: () => ({}),
  showDefaultActions: true,
  submitText: '提交',
  resetText: '重置',
  formContainerClass: '',
  wrapperClass: '',
  actionWrapperClass: '',
  commonConfig: () => ({}),
});

const emits = defineEmits<{
  reset: [values: Record<string, any>];
  submit: [values: Record<string, any>];
  valuesChange: [
    changedValues: Record<string, any>,
    allValues: Record<string, any>,
  ];
}>();

// 表单实例引用
const formRef = ref<FormInstance>();
// 表单数据
const formModel = reactive<Record<string, any>>({});
// 加载状态
const loading = ref(false);

/**
 * 组件映射表
 * 将字符串组件名映射到实际的组件
 */
const componentMap = {
  Input,
  'Input.TextArea': Input.TextArea,
  'Input.Password': Input.Password,
  'Input.Search': Input.Search,
  Select,
  InputNumber,
  DatePicker,
  'DatePicker.RangePicker': DatePicker.RangePicker,
  'DatePicker.MonthPicker': DatePicker.MonthPicker,
  'DatePicker.WeekPicker': DatePicker.WeekPicker,
  'DatePicker.QuarterPicker': DatePicker.QuarterPicker,
  'DatePicker.YearPicker': DatePicker.YearPicker,
  TimePicker,
  'TimePicker.RangePicker': TimePicker.RangePicker,
  Switch,
  Checkbox,
  'Checkbox.Group': Checkbox.Group,
  Radio,
  'Radio.Group': Radio.Group,
  Rate,
  Slider,
  Transfer,
  TreeSelect,
  Upload,
  Cascader,
  Mentions,
  AutoComplete,
  RangePicker: DatePicker.RangePicker, // 兼容性别名，用于简化时间范围选择器的使用
};

/**
 * 计算最适合的标签宽度
 */
const computedLabelWidth = computed(() => {
  if (typeof props.labelWidth === 'number') {
    return props.labelWidth;
  }

  if (props.labelWidth === 'auto') {
    // 计算所有标签的最大长度
    const labelLengths = props.schema.map(
      (field) => (field.label || '').length,
    );
    const maxLabelLength =
      labelLengths.length > 0 ? Math.max(...labelLengths) : 0;

    // 更合理的计算：中文字符约14px，加上冒号和间距
    // 确保标签文字不会被遮挡
    const estimatedWidth = Math.max(maxLabelLength * 14 + 20, 60);

    // 放宽最大限制，确保长标签能完整显示
    return Math.min(estimatedWidth, 120);
  }

  return 120; // 默认宽度
});

/**
 * 初始化表单数据
 * 根据schema和initialValues设置表单的初始值，并处理RangePicker的数据格式转换
 */
const initFormData = () => {
  // 清空当前表单数据
  Object.keys(formModel).forEach((key) => {
    delete formModel[key];
  });

  // 处理初始值的数据格式转换
  const processedInitialValues = { ...props.initialValues };
  props.schema.forEach((field) => {
    if (
      Object.prototype.hasOwnProperty.call(
        processedInitialValues,
        field.fieldName,
      )
    ) {
      const fieldValue = processedInitialValues[field.fieldName];

      // 检查是否为RangePicker组件且设置了valueFormat: 'X'
      const isRangePicker =
        (typeof field.component === 'string' &&
          (field.component === 'RangePicker' ||
            field.component === 'DatePicker.RangePicker')) ||
        field.component === componentMap.RangePicker ||
        field.component === componentMap['DatePicker.RangePicker'];

      if (
        isRangePicker &&
        field.componentProps?.valueFormat === 'X' &&
        Array.isArray(fieldValue)
      ) {
        // 将数字时间戳转换为dayjs对象，适配RangePicker的value格式
        processedInitialValues[field.fieldName] = fieldValue.map(
          (timestamp, index) => {
            console.log(`初始化时间字段 ${field.fieldName}[${index}]:`, {
              原始值: timestamp,
              类型: typeof timestamp,
            });

            if (
              timestamp !== null &&
              timestamp !== undefined &&
              timestamp !== ''
            ) {
              const timestampNum = Number(timestamp);
              if (!Number.isNaN(timestampNum)) {
                // 检查是否为毫秒级时间戳，如果是则转换为秒级
                const finalTimestamp =
                  timestampNum > 1_000_000_000_000
                    ? Math.floor(timestampNum / 1000)
                    : timestampNum;

                console.log(
                  `${field.fieldName}[${index}] 最终秒级时间戳:`,
                  finalTimestamp,
                );
                return dayjs.unix(finalTimestamp);
              }
            }
            return null;
          },
        );
        console.log(`转换RangePicker初始值 ${field.fieldName}:`, {
          原始值: fieldValue,
          转换后: processedInitialValues[field.fieldName],
        });
      }
    }
  });

  // 设置处理后的初始值
  Object.assign(formModel, processedInitialValues);

  // 为schema中的字段设置默认值（也需要进行格式转换）
  props.schema.forEach((field) => {
    if (
      field.defaultValue !== undefined &&
      formModel[field.fieldName] === undefined
    ) {
      let defaultValue = field.defaultValue;

      // 检查是否为RangePicker组件且设置了valueFormat: 'X'
      const isRangePicker =
        (typeof field.component === 'string' &&
          (field.component === 'RangePicker' ||
            field.component === 'DatePicker.RangePicker')) ||
        field.component === componentMap.RangePicker ||
        field.component === componentMap['DatePicker.RangePicker'];

      if (
        isRangePicker &&
        field.componentProps?.valueFormat === 'X' &&
        Array.isArray(defaultValue)
      ) {
        // 将数字时间戳转换为dayjs对象，适配RangePicker的value格式
        defaultValue = defaultValue.map((timestamp, index) => {
          if (
            timestamp !== null &&
            timestamp !== undefined &&
            timestamp !== ''
          ) {
            const timestampNum = Number(timestamp);
            if (!Number.isNaN(timestampNum)) {
              // 检查是否为毫秒级时间戳，如果是则转换为秒级
              const finalTimestamp =
                timestampNum > 1_000_000_000_000
                  ? Math.floor(timestampNum / 1000)
                  : timestampNum;

              console.log(
                `默认值时间戳 ${field.fieldName}[${index}]:`,
                finalTimestamp,
              );
              return dayjs.unix(finalTimestamp);
            }
          }
          return null;
        });
        console.log(`转换RangePicker默认值 ${field.fieldName}:`, {
          原始值: field.defaultValue,
          转换后: defaultValue,
        });
      }

      formModel[field.fieldName] = defaultValue;
    }
  });
};

/**
 * 判断组件是否使用value绑定方式
 * 某些Ant Design Vue组件使用v-model:value而不是v-model
 */
const isValueBindingComponent = (component: any) => {
  // 检查组件是否为已知使用value绑定的组件类型
  if (typeof component === 'string') {
    // 使用value绑定的字符串组件名列表
    const valueBindingComponentNames = [
      'Select',
      'DatePicker',
      'RangePicker',
      'DatePicker.RangePicker',
      'TimePicker',
      'TimePicker.RangePicker',
      'TreeSelect',
      'Cascader',
      'Transfer',
      'Mentions',
    ];
    return valueBindingComponentNames.includes(component);
  }

  // 检查组件是否为已知使用value绑定的组件实例
  const valueBindingComponents = [
    componentMap.Select,
    componentMap.DatePicker,
    componentMap.RangePicker,
    componentMap['DatePicker.RangePicker'],
    componentMap.TimePicker,
    componentMap['TimePicker.RangePicker'],
    componentMap.TreeSelect,
    componentMap.Cascader,
    componentMap.Transfer,
    componentMap.Mentions,
  ];

  return valueBindingComponents.includes(component);
};

/**
 * 计算字段的显示状态和属性
 */
const computedFields = computed(() => {
  return props.schema.map((field) => {
    const { dependencies } = field;

    let isShow = true;
    let isDisabled = false;
    let dynamicProps = {};

    if (dependencies) {
      // 计算显示条件
      if (dependencies.show) {
        isShow = dependencies.show(formModel);
      }

      // 计算禁用条件
      if (dependencies.disabled) {
        isDisabled = dependencies.disabled(formModel);
      }

      // 计算动态属性
      if (dependencies.componentProps) {
        dynamicProps = dependencies.componentProps(formModel);
      }
    }

    // 解析组件：处理字符串组件名和ref包装的组件
    const resolvedComponent =
      typeof field.component === 'string'
        ? componentMap[field.component as keyof typeof componentMap] ||
          field.component
        : unref(field.component);

    // 确定组件的model属性名
    let modelProp = 'model-value'; // 默认使用model-value

    // 1. 首先检查是否有显式指定的modelPropName
    if (field.modelPropName) {
      modelProp = field.modelPropName;
    }
    // 2. 其次检查是否为已知使用value绑定的组件
    else if (
      isValueBindingComponent(field.component) ||
      isValueBindingComponent(resolvedComponent)
    ) {
      modelProp = 'value';
    }

    return {
      ...field,
      component: resolvedComponent,
      isShow,
      isDisabled,
      dynamicProps,
      modelProp, // 组件的model属性名
    };
  });
});

/**
 * 表单规则计算
 */
const formRules = computed(() => {
  const rules: Record<string, Rule[]> = {};

  computedFields.value.forEach((field) => {
    if (!field.isShow) return;

    const fieldRules: Rule[] = [];

    // 必填规则
    if (field.required) {
      // 根据组件类型生成合适的提示消息
      const getRequiredMessage = (field: any) => {
        const component = field.component;
        const componentName =
          typeof component === 'string' ? component : component?.name || '';

        // 选择类组件使用"请选择"
        const selectComponents = [
          'Select',
          'DatePicker',
          'RangePicker',
          'TimePicker',
          'TreeSelect',
          'Cascader',
          'Transfer',
          'Mentions',
          'Upload',
        ];

        // 检查是否为选择类组件
        const isSelectComponent = selectComponents.some(
          (name) =>
            componentName === name ||
            componentName.includes(name) ||
            (typeof component === 'string' && component.includes(name)),
        );

        return isSelectComponent
          ? `请选择${field.label}`
          : `请输入${field.label}`;
      };

      fieldRules.push({
        required: true,
        message: getRequiredMessage(field),
      });
    }

    // 自定义规则
    if (field.rules) {
      if (Array.isArray(field.rules)) {
        fieldRules.push(...field.rules);
      } else {
        fieldRules.push(field.rules);
      }
    }

    if (fieldRules.length > 0) {
      rules[field.fieldName] = fieldRules;
    }
  });

  return rules;
});

/**
 * 提供给子组件的表单上下文
 */
const formContext = {
  formModel,
  updateFieldValue: (fieldName: string, value: any) => {
    formModel[fieldName] = value;
    // 触发valuesChange事件
    emits('valuesChange', { [fieldName]: value }, { ...formModel });
  },
  getFieldValue: (fieldName: string) => {
    return formModel[fieldName];
  },
  getAllValues: () => {
    return { ...formModel };
  },
};

// 提供表单上下文给子组件
provide('enhanced-form-context', formContext);

/**
 * 处理表单值变化
 */
const handleValuesChange = (
  changedValues: Record<string, any>,
  allValues: Record<string, any>,
) => {
  emits('valuesChange', changedValues, allValues);
};

/**
 * 处理表单提交
 */
const handleSubmit = async () => {
  try {
    loading.value = true;

    // 验证表单
    const values = await formRef.value?.validateFields();

    if (values) {
      emits('submit', values);
    }
  } catch (error) {
    console.log('表单验证失败:', error);
  } finally {
    loading.value = false;
  }
};

/**
 * 处理表单重置
 */
const handleReset = () => {
  formRef.value?.resetFields();
  initFormData();
  emits('reset', { ...formModel });
};

/**
 * 验证表单
 */
const validate = async () => {
  return await formRef.value?.validateFields();
};

/**
 * 验证指定字段
 */
const validateFields = async (fields?: string[]) => {
  return await formRef.value?.validateFields(fields);
};

/**
 * 重置表单
 */
const resetForm = () => {
  handleReset();
};

/**
 * 设置字段值
 * 针对RangePicker组件进行数据格式转换
 */
const setFieldValue = (fieldName: string, value: any) => {
  // 查找对应的字段配置
  const field = props.schema.find((f) => f.fieldName === fieldName);

  if (field) {
    // 检查是否为RangePicker组件且设置了valueFormat: 'X'
    const isRangePicker =
      (typeof field.component === 'string' &&
        (field.component === 'RangePicker' ||
          field.component === 'DatePicker.RangePicker')) ||
      field.component === componentMap.RangePicker ||
      field.component === componentMap['DatePicker.RangePicker'];

    if (
      isRangePicker &&
      field.componentProps?.valueFormat === 'X' &&
      Array.isArray(value)
    ) {
      // 将数字时间戳转换为dayjs对象，适配RangePicker的value格式
      const convertedValue = value.map((timestamp) =>
        timestamp !== null && timestamp !== undefined
          ? dayjs.unix(Number(timestamp))
          : null,
      );
      formModel[fieldName] = convertedValue;
      console.log(`转换RangePicker字段 ${fieldName}:`, {
        原始值: value,
        转换后: convertedValue,
      });
      return;
    }
  }

  formModel[fieldName] = value;
};

/**
 * 设置多个字段值
 * 针对RangePicker组件进行数据格式转换
 */
const setFieldsValue = (values: Record<string, any>) => {
  // 创建处理后的值对象
  const processedValues = { ...values };

  // 遍历schema，找到RangePicker类型的字段并进行数据格式转换
  props.schema.forEach((field) => {
    if (processedValues.hasOwnProperty(field.fieldName)) {
      const fieldValue = processedValues[field.fieldName];

      // 检查是否为RangePicker组件且设置了valueFormat: 'X'
      const isRangePicker =
        (typeof field.component === 'string' &&
          (field.component === 'RangePicker' ||
            field.component === 'DatePicker.RangePicker')) ||
        field.component === componentMap.RangePicker ||
        field.component === componentMap['DatePicker.RangePicker'];

      if (
        isRangePicker &&
        field.componentProps?.valueFormat === 'X' &&
        Array.isArray(fieldValue)
      ) {
        // 将数字时间戳转换为dayjs对象，适配RangePicker的value格式
        processedValues[field.fieldName] = fieldValue.map(
          (timestamp, index) => {
            console.log(`设置时间字段 ${field.fieldName}[${index}]:`, {
              原始值: timestamp,
              类型: typeof timestamp,
              数值: Number(timestamp),
            });

            if (
              timestamp !== null &&
              timestamp !== undefined &&
              timestamp !== ''
            ) {
              const timestampNum = Number(timestamp);
              if (!Number.isNaN(timestampNum)) {
                // 检查是否为毫秒级时间戳（通常大于1000000000000）
                // 如果是毫秒级，转换为秒级
                const finalTimestamp =
                  timestampNum > 1_000_000_000_000
                    ? Math.floor(timestampNum / 1000)
                    : timestampNum;

                console.log(
                  `${field.fieldName}[${index}] 转换为秒级时间戳:`,
                  finalTimestamp,
                );
                const dayjsObj = dayjs.unix(finalTimestamp);
                console.log(
                  `${field.fieldName}[${index}] 转换为dayjs:`,
                  dayjsObj.format(),
                );
                return dayjsObj;
              }
            }
            return null;
          },
        );
        console.log(`转换RangePicker字段 ${field.fieldName}:`, {
          原始值: fieldValue,
          转换后: processedValues[field.fieldName],
        });
      }
    }
  });

  Object.assign(formModel, processedValues);
};

/**
 * 获取字段值
 * 针对RangePicker组件进行数据格式转换，确保返回正确的时间戳格式
 */
const getFieldValue = (fieldName: string) => {
  const fieldValue = formModel[fieldName];

  // 查找对应的字段配置
  const field = props.schema.find((f) => f.fieldName === fieldName);

  if (field) {
    // 检查是否为RangePicker组件且设置了valueFormat: 'X'
    const isRangePicker =
      (typeof field.component === 'string' &&
        (field.component === 'RangePicker' ||
          field.component === 'DatePicker.RangePicker')) ||
      field.component === componentMap.RangePicker ||
      field.component === componentMap['DatePicker.RangePicker'];

    if (
      isRangePicker &&
      field.componentProps?.valueFormat === 'X' &&
      Array.isArray(fieldValue)
    ) {
      // 将dayjs对象转换为字符串时间戳，确保输出格式正确
      return fieldValue.map((dayjsObj, index) => {
        console.log(`单字段处理时间 ${fieldName}[${index}]:`, {
          原始值: dayjsObj,
          类型: typeof dayjsObj,
          是否为dayjs: dayjsObj && typeof dayjsObj.unix === 'function',
        });

        if (dayjsObj && typeof dayjsObj.unix === 'function') {
          const unixTimestamp = dayjsObj.unix();
          console.log(`${fieldName}[${index}] unix时间戳:`, unixTimestamp);
          return String(unixTimestamp); // 返回字符串格式的秒级时间戳
        }

        // 如果不是dayjs对象，但可能是时间戳数字，直接返回
        if (typeof dayjsObj === 'number') {
          return String(dayjsObj);
        }

        // 如果是字符串时间戳，直接返回
        if (typeof dayjsObj === 'string' && !isNaN(Number(dayjsObj))) {
          return dayjsObj;
        }

        return dayjsObj; // 如果不是dayjs对象，直接返回
      });
    }
  }

  return fieldValue;
};

/**
 * 获取所有字段值
 * 针对RangePicker组件进行数据格式转换，确保返回正确的时间戳格式
 */
const getFieldsValue = () => {
  const values = { ...formModel };

  // 遍历schema，找到RangePicker类型的字段并进行数据格式转换
  props.schema.forEach((field) => {
    if (values.hasOwnProperty(field.fieldName)) {
      const fieldValue = values[field.fieldName];

      // 检查是否为RangePicker组件且设置了valueFormat: 'X'
      const isRangePicker =
        (typeof field.component === 'string' &&
          (field.component === 'RangePicker' ||
            field.component === 'DatePicker.RangePicker')) ||
        field.component === componentMap.RangePicker ||
        field.component === componentMap['DatePicker.RangePicker'];

      if (
        isRangePicker &&
        field.componentProps?.valueFormat === 'X' &&
        Array.isArray(fieldValue)
      ) {
        // 将dayjs对象转换为字符串时间戳，确保输出格式正确
        values[field.fieldName] = fieldValue.map((dayjsObj, index) => {
          console.log(`处理时间字段 ${field.fieldName}[${index}]:`, {
            原始值: dayjsObj,
            类型: typeof dayjsObj,
            是否为dayjs: dayjsObj && typeof dayjsObj.unix === 'function',
            unix方法存在: dayjsObj && typeof dayjsObj.unix,
            valueOf: dayjsObj && dayjsObj.valueOf ? dayjsObj.valueOf() : 'N/A',
          });

          if (dayjsObj && typeof dayjsObj.unix === 'function') {
            const unixTimestamp = dayjsObj.unix();
            console.log(
              `${field.fieldName}[${index}] unix时间戳:`,
              unixTimestamp,
            );
            return String(unixTimestamp); // 返回字符串格式的秒级时间戳
          }

          // 如果不是dayjs对象，但可能是时间戳数字，直接返回
          if (typeof dayjsObj === 'number') {
            console.log(
              `${field.fieldName}[${index}] 已经是数字时间戳:`,
              dayjsObj,
            );
            return String(dayjsObj);
          }

          // 如果是字符串时间戳，直接返回
          if (typeof dayjsObj === 'string' && !Number.isNaN(Number(dayjsObj))) {
            console.log(
              `${field.fieldName}[${index}] 已经是字符串时间戳:`,
              dayjsObj,
            );
            return dayjsObj;
          }

          console.log(
            `${field.fieldName}[${index}] 未识别的格式，直接返回:`,
            dayjsObj,
          );
          return dayjsObj; // 如果不是dayjs对象，直接返回
        });
        console.log(`转换RangePicker输出字段 ${field.fieldName}:`, {
          原始值: fieldValue,
          转换后: values[field.fieldName],
        });
      }
    }
  });

  return values;
};

/**
 * 清除验证状态
 */
const clearValidate = (fields?: string[]) => {
  formRef.value?.clearValidate(fields);
};

// 监听schema变化，重新初始化表单
watch(
  () => props.schema,
  () => {
    initFormData();
  },
  { deep: true },
);

// 监听initialValues变化
watch(
  () => props.initialValues,
  () => {
    initFormData();
  },
  { deep: true },
);

// 组件挂载时初始化表单数据
onMounted(() => {
  initFormData();
});

// 暴露表单API
const formApi = {
  validate,
  validateFields,
  resetForm,
  setFieldValue,
  setFieldsValue,
  getFieldValue,
  getFieldsValue,
  clearValidate,
  formRef,
};

defineExpose({
  formApi,
  ...formApi,
});
</script>

<template>
  <div
    :class="[formContainerClass, { 'label-aligned': labelAlign }]"
    class="enhanced-form-container"
  >
    <!-- 自定义插槽：表单前置内容 -->
    <slot name="before-form"></slot>

    <a-form
      ref="formRef"
      :class="wrapperClass"
      :label-col="labelCol"
      :layout="layout"
      :model="formModel"
      :rules="formRules"
      :wrapper-col="wrapperCol"
      @values-change="handleValuesChange"
    >
      <!-- 渲染表单字段 -->
      <template v-for="field in computedFields" :key="field.fieldName">
        <form-item
          v-if="field.isShow"
          :class="field.formItemClass"
          :help="field.help"
          :label="field.label"
          :label-class="field.labelClass"
          :name="field.fieldName"
        >
          <!-- 字段插槽，允许完全自定义字段内容 -->
          <slot
            :field="field"
            :form-context="formContext"
            :name="field.fieldName"
            :value="formModel[field.fieldName]"
          >
            <!-- 禁用自动事件绑定的组件渲染 -->
            <!-- 禁用自动事件绑定的组件 - 根据modelProp使用不同的绑定方式 -->
            <component
              :is="field.component"
              v-if="field.disableAutoBinding"
              v-bind="{
                [field.modelProp]: formModel[field.fieldName],
                ...commonConfig?.componentProps,
                ...field.componentProps,
                ...field.dynamicProps,
                disabled: field.isDisabled,
              }"
              @[`update:${field.modelProp}`]="
                (value: any) =>
                  formContext.updateFieldValue(field.fieldName, value)
              "
            />
            <!-- 启用自动事件绑定的组件渲染 -->
            <!-- 启用自动事件绑定的组件 - 根据modelProp使用不同的绑定方式 -->
            <!-- 标准v-model绑定 -->
            <component
              :is="field.component"
              v-else-if="field.modelProp === 'model-value'"
              v-model="formModel[field.fieldName]"
              v-bind="{
                ...commonConfig?.componentProps,
                ...field.componentProps,
                ...field.dynamicProps,
                disabled: field.isDisabled,
              }"
            />
            <!-- v-model:value绑定 -->
            <component
              :is="field.component"
              v-else-if="field.modelProp === 'value'"
              v-model:value="formModel[field.fieldName]"
              v-bind="{
                ...commonConfig?.componentProps,
                ...field.componentProps,
                ...field.dynamicProps,
                disabled: field.isDisabled,
              }"
            />
            <!-- v-model:checked绑定 -->
            <component
              :is="field.component"
              v-else-if="field.modelProp === 'checked'"
              v-model:checked="formModel[field.fieldName]"
              v-bind="{
                ...commonConfig?.componentProps,
                ...field.componentProps,
                ...field.dynamicProps,
                disabled: field.isDisabled,
              }"
            />
            <!-- v-model:open绑定 -->
            <component
              :is="field.component"
              v-else-if="field.modelProp === 'open'"
              v-model:open="formModel[field.fieldName]"
              v-bind="{
                ...commonConfig?.componentProps,
                ...field.componentProps,
                ...field.dynamicProps,
                disabled: field.isDisabled,
              }"
            />
            <!-- v-model:visible绑定 -->
            <component
              :is="field.component"
              v-else-if="field.modelProp === 'visible'"
              v-model:visible="formModel[field.fieldName]"
              v-bind="{
                ...commonConfig?.componentProps,
                ...field.componentProps,
                ...field.dynamicProps,
                disabled: field.isDisabled,
              }"
            />
            <!-- 其他自定义属性绑定 -->
            <component
              :is="field.component"
              v-else
              v-bind="{
                [field.modelProp]: formModel[field.fieldName],
                ...commonConfig?.componentProps,
                ...field.componentProps,
                ...field.dynamicProps,
                disabled: field.isDisabled,
              }"
              @[`update:${field.modelProp}`]="
                (value: any) => {
                  formModel[field.fieldName] = value;
                }
              "
            />
          </slot>

          <!-- 字段描述 -->
          <div v-if="field.description" class="mt-1 text-xs text-gray-500">
            {{ field.description }}
          </div>
        </form-item>
      </template>

      <!-- 操作按钮区域 -->
      <form-item v-if="showDefaultActions" :class="actionWrapperClass">
        <slot
          :handle-reset="handleReset"
          :handle-submit="handleSubmit"
          :loading="loading"
          name="actions"
        >
          <div class="flex gap-2">
            <a-button :loading="loading" type="primary" @click="handleSubmit">
              {{ submitText }}
            </a-button>
            <button @click="handleReset">
              {{ resetText }}
            </button>
          </div>
        </slot>
      </form-item>
    </a-form>

    <!-- 自定义插槽：表单后置内容 -->
    <slot name="after-form"></slot>
  </div>
</template>

<style scoped>
.enhanced-form-container {
  /* 表单容器样式 */
}

/* 表单字段动画 */
.form-item-enter-active,
.form-item-leave-active {
  transition: all 0.3s ease;
}

.form-item-enter-from,
.form-item-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 标签对齐样式 - 最高权重强制生效 */
.enhanced-form-container.label-aligned
  :deep(.ant-form .ant-form-item .ant-form-item-label) {
  text-align: right !important;
  width: v-bind('`${computedLabelWidth  }px`') !important;
  min-width: v-bind('`${computedLabelWidth  }px`') !important;
  max-width: v-bind('`${computedLabelWidth  }px`') !important;
  padding-right: 8px !important;
  flex: none !important;
  display: inline-block !important;
}

.enhanced-form-container.label-aligned
  :deep(.ant-form .ant-form-item .ant-form-item-label > label) {
  text-align: right !important;
  display: block !important;
}
</style>
