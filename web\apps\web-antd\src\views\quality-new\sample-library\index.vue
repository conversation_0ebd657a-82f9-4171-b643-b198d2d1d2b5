<script setup lang="ts">
import type { VxeGridProps } from '#/adapter/vxe-table';
import TableTemplate from '#/components/table/index.vue';
import{ref,onMounted,computed,shallowRef} from 'vue';
import { storeToRefs } from 'pinia';
import ForbidSelect from '#/components/select/ForbidType.vue';
import { message } from 'ant-design-vue';
import { mapToOptions } from '#/utils/transform';
import BizSelect from '#/components/select/biz.vue';
import { TIME_TYPE_LIST, MODULE_TYPE,QCOMPARE_LIST } from '#/constants/maps';
import {formatTimestampToDate, getLastNDaysRange,getTodayTimeRange,getCurrentTimestamp, transformTimeFields } from "#/utils/time";
import { EMPTY_OPTION } from "#/constants/options";
import { getQualitySamplesList } from '#/api/quality-new';
import {pushTask} from "#/api/task";
import {getProductList} from "#/api/platform";
import useForbidStore from '#/store/forbid';
import SceneSelect from '#/components/select/scene.vue';
import { usePasteField } from "#/composables/common/use-paste-field";
import SupplierSelect from '#/components/select/supplier.vue';
import FileLink from "#/components/file/index.vue";


const forbidStore = useForbidStore();
const { allForbidTags } = storeToRefs(forbidStore);
const { clipData} = usePasteField();

const PRODUCTLIST_OPTIONS = ref<{ label: string; value: string }[]>([]);
const ready = ref(false);

const props = defineProps({
  biz: {
    // type: String,
    default: "drive_core",
  },
  bizConfig: {
    type: Object,
    default: () => ({}),
  },
});


//方法：基于树形数据构建一个映射
const typePathMap = computed(() => {
  // 创建一个 Map 用于存储 type -> 路径字符串 的映射
  const pathMap = new Map<number, string>();
  // 递归函数dfs，用于深度优先搜索
  function dfs(node: any, path: string[]) {
    const currentPath = [...path, node.name];
    // 用 " / " 拼接
    if (node.type) {
      pathMap.set(node.type, currentPath.join(' / '));
    }
    // 如果当前节点有子节点，递归遍历子节点
    if (Array.isArray(node.children)) {
      for (const child of node.children) {
        dfs(child, currentPath);
      }
    }
  }
  // 遍历所有根节点开始递归
  for (const root of allForbidTags.value) {
    dfs(root, []);
  }
  // 返回构建好的 Map
  return pathMap;
});


onMounted(async () => {
  //使用组件访问得到的业务编号与原页面不符，原页面与其他页面的业务编号不统一
  try {
    const productListRes = await getProductList({ product_line_type: 'quality_platform' });
    const productList = productListRes?.data?.product_line?.[0]?.field_list || [];
    // 供 Select 使用
    PRODUCTLIST_OPTIONS.value = productList.map((item: any) => ({
      label: item.name,
      value: item.value,
    }));
  } catch (error) {
    message.error('获取业务编号数据失败');
  }
  ready.value = true;
});

const queryMethod = async ({
  page,
  pageSize,
  ...formValues
}: {
  page: number;
  pageSize: number;
  [key: string]: any;
}) => {
  
  // 添加分页参数
  formValues.page = page;
  formValues.limit = pageSize;

  // 添加分页参数
  const params = {
    ...transformTimeFields(formValues,[["time",["start_time","end_time"]]]),
    page,
    limit: pageSize,
  };

  const res = await getQualitySamplesList(params);
  return {
    items: res.data.results,
    total: res.data.count,
  };
};

const searchSchemas = computed(()=>{
  return{
    '1': [ 
      {
        component: "Select",
        fieldName: 'time_type',
        label: '时间类型',
        defaultValue:"sample_time",
        componentProps: {
        //   placeholder: '请选择',
          options: mapToOptions(TIME_TYPE_LIST),
        },
      },

      {
        component: "RangePicker",
        fieldName: 'time',
        label: '时间',
        class: "col-span-2",
        defaultValue: getLastNDaysRange(7, 0, "X"),
        componentProps: {
          valueFormat: "X",
          showTime: true,
        },
      },
      {
        component: "Select",
        fieldName: 'biz',
        label: '业务编号',
        componentProps: {
          placeholder: '请选择',
          options: PRODUCTLIST_OPTIONS,
        },
      },
      // {
      //   fieldName: "biz",
      //   component: shallowRef(BizSelect),
      //   label: "业务编号",
      //   componentProps: {
      //     biz: props.biz,
      //   },
      //   dependencies: {
      //     trigger(values, form) {
      //       if (!values.fileinx) {
      //         form.setFieldValue("biz", "");
      //       } else if (Array.isArray(clipData.value)) {
      //         form.setFieldValue("biz", clipData.value[2]);
      //       }
      //     },
      //     // 只有指定的字段改变时，才会触发
      //     triggerFields: ["fileinx"],
      //   },
      // },
      {
        component: shallowRef(SupplierSelect), // 这里选择框应该有点问题 需要优化
        fieldName: 'supplier',
        label: '质检团队',
        componentProps: {
          placeholder: '请选择',
        }
      },
      {
        component: "Select",
        fieldName: 'origin_entry',
        label: '工单来源',
        componentProps: {
          placeholder: '请选择',
        //   options: '',
        },
      },
      {
        component: "Select",
        fieldName: 'model_type',
        label: '时效归属',
        componentProps: {
          placeholder: '请选择',
          options: mapToOptions(MODULE_TYPE),
        },
      },
      {
        component: "Input",
        fieldName: 'reviewer_name',
        label: '质检人',
        componentProps: {
          placeholder: '',
        },
      },
      {
        component: "Input",
        fieldName: 'fileid',
        label: 'fileID',
        componentProps: {
          placeholder: '',
        },
      },
      {
        fieldName: "scene",
        component: shallowRef(SceneSelect),
        label: "场景",
        componentProps: {
          biz: props.biz,
        },
        dependencies: {
          trigger(values, form) {
            if (!values.fileinx) {
              form.setFieldValue("scene", "");
            } else if (Array.isArray(clipData.value)) {
              form.setFieldValue("scene", clipData.value[2]);
            }
          },
          // 只有指定的字段改变时，才会触发
          triggerFields: ["fileinx"],
        },
      },
      {
        component: "Input",
        fieldName: 'extra_ID',
        label: '附件id',
        componentProps: {
          placeholder: '附件id',
        },
      },
      {
        component: "Select",
        fieldName: 'check_flag',
        label: '质检结果对比',
        defaultValue: "",
        componentProps: {
          options:[...EMPTY_OPTION,...mapToOptions(QCOMPARE_LIST)]
        },
      },
      
    ],
  }
})

const searchOptions =  computed(() => ({
  collapsed: false,
  schemas: searchSchemas.value,
  showCollapseButton: false,
  submitOnChange: false,
  wrapperClass: 'grid-cols-5',
}));

const tableRef = ref();
const columns: VxeGridProps['columns'] = [
  {
    field: 'time_type',
    title: '时间',
    // formatter: 'formatDateTime',
    width:120,
    formatter: ({ cellValue }: { cellValue: any }) => {
      const time = cellValue || getCurrentTimestamp(); // 没有值就取当前时间
      return formatTimestampToDate(time); // 传给你自己的格式化函数
    },
  },
  {
    field: 'biz',
    title: '业务',
    width:120,
    formatter: ({ cellValue }) => {
    const matched = PRODUCTLIST_OPTIONS.value.find((item: any) => item.value === cellValue);
      return matched?.label || '';
    },
  },
  {
    field: 'org_entry_name',
    title: '来源',
    width:120,
  },
  {
    field: 'fileid',
    title: 'fileID',
    width:120,
  },
  {
    field: 'extra_id',
    title: '附件id',
    width:120,
  },
  {
    field: 'scene',
    title: '场景',
    width:120,
  },
  {
    // field: 'fname',
    title: '名称',
    width:300,
    slots: {
      default: "fname",
    },
  },
  {
    field: 'content',
    title: '文本内容',
    width:120,
  },
  {
    field: 'userid',
    title: 'userID',
    width:120,
  },
  {
    field: 'org_reviewer_name',
    title: '操作人',
    width:120,
  },
  {
    field: 'org_type',
    title: '审批结果',
    width:120,
    formatter: ({ cellValue }) => {
      if (cellValue === 0){
        return '通过'
      }
      return typePathMap.value.get(cellValue)??'-';
    },
  },
  {
    field: 'org_reviewer_time',
    title: '操作时间',
    width:120,
    // formatter: 'formatDateTime',
    formatter: ({ cellValue }) => formatTimestampToDate(cellValue),
  },
  {
    field: 'review_fileid',
    title: '质检fileid',
    width:120,
  },
  {
    field: 'review_extra_id',
    title: '质检附件id',
    width:120,
  },
  {
    field: 'review_scene',
    title: '质检场景',
    width:120,
  },
  {
    field: 'reviewer_name',
    title: '质检人',
    width:120,
  },
  {
    field: 'type',
    title: '质检结果',
    width:120,
    formatter: ({ cellValue,row }) => {
      const reviewer = row.reviewer_name;
      if(!reviewer){
        return ''
      }
      else{
        if (cellValue === 0){
          return '通过'
        }
        return typePathMap.value.get(cellValue)??'-';
      }
    },
  },
  {
    field: 'actions',
    title: '操作',
    fixed: 'right',
    width:120,
    slots:{
      default:'actions'
    }
  },
];

const toolbarOptions = {
  export: true,
};
const paginationOptions = {
  total: 50,
  pageSize: 20,
  currentPage: 1,
};
const exportOptions = {
  filename: '表格数据', // 导出的文件名
  type: 'csv', // 导出格式
  isHeader: true, // 包含表头
  original: true, // 导出源数据
  useLabel: true, // 使用列标签作为字段名
  message: true, // 显示导出消息提示
  encoding: 'utf8', // 文件编码
};

const handleCategoryChange = async (row: any, val: string[]) => {
  console.log('handleCategoryChange', row, val);
  
  const selected = val?.[val.length - 1]; // 获取选中的最后一个值（field）

  // 显示“操作”作为占位
  row.selectedCategory = val;

  let payload: any = {
    id: row.id,
    biz: row.review_biz,
    fileid: row.review_fileid,
    scene: row.review_scene,
    extra_id: row.review_extra_id,
    entry: "quality_sample",
    reviewer_id: row.reviewer_id,
    task_tag: 8,
    extra_json: row.review_extra_json,
    uuid: row.uuid,
    pull_create_time: row.pull_create_time,
    pull_modify_time: row.pull_modify_time,
  };

  if (selected === 'pass') {
    payload.type=0;
    payload.status='ok';
  } else {
    const typeNumber = selected; 
    payload.type = typeNumber;
    payload.status = 'forbid';
  }

  try {
    await pushTask(payload);
    tableRef.value?.queryTable(); // 就会触发 queryMethod 拉取数据
    message.success('更新状态成功');
  } catch (error) {
    // 处理错误
    message.error('更新状态失败');
  }
};

</script>

<template >

  <table-template
    v-if="ready"
    ref="tableRef"
    :columns="columns"
    :query-method="queryMethod"
    :toolbar-options="toolbarOptions"
    :export-options="exportOptions"
    :search-options="searchOptions"
    :pagination-options="paginationOptions"
  >
  <template #fname="{ row }">
    <!-- <div style=" width: 100%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
      <file-link :item="row" />
    </div> -->
      <file-link :item="row" />
  </template>
  
  <template #actions="{ row }"> 
    <forbid-select
      v-model="row.selectedType"
      :isGLobalList="true"
      :pass="true"
      :filterType="[3, 5]"
      :isCascader="true"
      class="ml-5 w-[70px]"
      :isClear="true"
      :callback="(value: any) => handleCategoryChange(row, value)"
    />
  </template>
   
  </table-template>
</template>
