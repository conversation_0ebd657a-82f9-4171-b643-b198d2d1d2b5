<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter/vxe-table';
import { ref, shallowRef } from 'vue';
import { message } from 'ant-design-vue';
import TableTemplate from '#/components/table/index.vue';
import {
  formatTimestampToDate,
  getCurrentTimestamp,
  transformTimeFields,
} from '#/utils/time';
import ForbidSelect from '#/components/select/ForbidType.vue';
import { storeToRefs } from 'pinia';
import useForbidStore from '#/store/forbid';
import FilePreview from '#/components/file/index.vue';
import StatusTag from '#/components/tag/index.vue';
import { EXAMINE_COLOR_MAP } from '#/constants/maps/examine-log';
import { mapToOptions } from '#/utils/transform';
import { getEnsureListService } from '#/api/task';
import { ORG_MAP } from '#/constants/maps';
import { FILE_DELAY_STATUS_MAP } from '#/constants/maps/sandbox';
import SceneSelect from '#/components/select/scene.vue';
import dayjs from 'dayjs';
import { findLabelByValue } from '#/utils/data';
import { setRecord, setHandover } from '#/api/checklist';
import { useUserStore } from '#/store/user';
const username = useUserStore().username;
const userid = useUserStore().userid;
const supplier = useUserStore().supplier;
const tableTemplateRef = ref<any>(null);
const forbidStore = useForbidStore();
const { allForbidTags } = storeToRefs(forbidStore);
const searchOptions = {
  collapsed: false,
  showCollapseButton: false,
  submitOnChange: false,
  wrapperClass: 'grid-cols-5',
  schemas: {
    '1': [
      {
        component: 'Input',
        fieldName: 'fileinx',
        label: 'fileID',
        componentProps: {
          placeholder: '请输入fileID',
        },
      },
      {
        field: 'scene',
        fieldName: 'scene',
        component: shallowRef(SceneSelect),
        label: '场景',

        componentProps: {
          biz: 'drive_core',
        },
      },
      {
        component: 'Input',
        fieldName: 'extra_id',
        label: '附件id',
        componentProps: {
          placeholder: '请输入附件id',
        },
      },

      {
        component: 'Input',
        fieldName: 'third_preview_url',
        label: '预览链接',
        componentProps: {
          placeholder: '请输入预览链接',
        },
      },
      {
        component: 'Input',
        fieldName: 'userid',
        label: 'UserID',
        componentProps: {
          placeholder: '请输入UserID',
        },
      },
      {
        component: 'Input',
        fieldName: 'reviewer',
        label: '操作人',
        componentProps: {
          placeholder: '请输入操作人',
        },
      },
      {
        component: 'DatePicker',
        fieldName: 'date_time',
        label: '日期',
        defaultValue: dayjs().startOf('day').unix(),
        componentProps: {
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'X',
        },
      },
      {
        component: 'Select',
        fieldName: 'org_entry',
        label: '一审来源',
        defaultValue: 'all',
        componentProps: {
          options: mapToOptions(ORG_MAP),
        },
      },
    ],
  },
};

const columns: VxeGridProps['columns'] = [
  { type: 'seq', title: '序号', width: 50, align: 'center' },
  {
    field: 'status',
    title: '状态',
    width: 80,
    slots: { default: 'status' },

    visible: true,
  },
  { field: 'fileinx', title: 'filed', width: 140, align: 'center' },
  { field: 'scene', title: '场景', width: 120, align: 'center' },
  { field: 'extra_id', title: '附件id', width: 120, align: 'center' },
  {
    field: 'fname',
    title: '名称',
    width: 300,
    slots: { default: 'fname' },
  },
  {
    field: 'type',
    title: '类型',
    width: 120,
    formatter: ({ cellValue }) =>
      findLabelByValue(allForbidTags.value, cellValue, 'type', 'name'),
  },
  {
    field: 'userid',
    title: 'UserID',
    width: 110,
  },

  {
    field: 'org_entry',
    title: '一审来源',
    width: 120,
    formatter: ({ cellValue }) => ORG_MAP[cellValue] || cellValue,
  },
  {
    field: 'pv_count',
    title: 'PV',
    width: 80,

    slots: { default: ({ row }) => row.pv_count || 0 },
  },
  {
    field: 'uv_count',
    title: 'UV',
    width: 80,

    slots: { default: ({ row }) => row.uv_count || 0 },
  },
  {
    field: 'gpv_count',
    title: 'GPV',
    width: 80,

    slots: { default: ({ row }) => row.gpv_count || 0 },
  },

  {
    field: 'releasetime',
    title: '发布时间',
    width: 180,
    align: 'center',
    slots: {
      default: ({ row }) => formatTimestampToDate(row.link_ctime),
    },
  },
  {
    field: 'operator',
    title: '操作人',
    width: 130,

    slots: {
      default: ({ row }) => row.reviewer_name || row.review_name,
    },
  },
  {
    field: 'operatortime',
    title: '操作时间',
    width: 180,
    align: 'center',
    slots: {
      default: ({ row }) => formatTimestampToDate(row.modify_time),
    },
  },
  {
    field: 'actions',
    title: '操作',
    width: 180,
    slots: { default: 'actions' },
  },
];
const paginationOptions = {
  pageSize: 20, // 每页显示的记录数
  currentPage: 1, // 当前页码
};
const handleAction = async (row: any, value: string) => {
  // 构造基础请求参数
  const baseParams = {
    reviewer: username,
    reviewer_id: userid,
    currentIndex: '888',
    entry: 'ensureGloble',
    uuid: row.uuid,
    fileinx: row.fileinx,
    scene: row.scene,
    extra_id: row.extra_id,
    from: 'drive_core',
    supplier: supplier,
  };

  let response: any;
  try {
    console.log(value);
    if (value[0] === 'pass') {
      // 通过操作
      const req = {
        ...baseParams,
        fsha: row.fsha,
        operation: 'ok', // 通过状态，对应原始代码的 statusType.pass
        in_time: row.in_time,
      };
      response = await setRecord(req, userid);
    } else {
      // 拒绝或其他操作
      const req = {
        ...baseParams,
        type: value[1], // 禁止类型
        fsha: row.fsha,
        operation: 'forbid', // 拒绝状态，对应原始代码的 statusType.forbid
      };
      response = await setRecord(req, userid);
    }

    // 处理响应
    if (response.result) {
      message.success('操作成功');
      // 刷新表格数据
      tableTemplateRef.value?.refreshTable();
      // 清空选择
      row.selectedType = undefined;
    } else {
      message.warning('操作失败');
    }
  } catch (error) {
    console.error('操作失败:', error);
    message.warning('操作失败');
  }
};
const queryMethod = async ({
  page,
  limit,
  ...formValues
}: {
  currentPage: number;
  pageSize: number;
  [key: string]: any;
}) => {
  try {
    const { pageSize, ...remainValues } = formValues;

    let params = {
      ...transformTimeFields(remainValues, [
        ['time', ['start_time', 'end_time']],
      ]),
      page: page - 1,
      limit: 20,
    };

    const response = await getEnsureListService(params);

    const items = response.data.records;
    return {
      items,
      total: response.data.total || 0,
    };
  } catch (error) {
    console.error('查询失败:', error);
    return { items: [], total: 0 };
  }
};
</script>

<template>
  <table-template
    ref="tableTemplateRef"
    :columns="columns"
    :query-method="queryMethod"
    :pagination-options="paginationOptions"
    :search-options="searchOptions"
  >
    <template #status="{ row }">
      <status-tag
        :status="row.status"
        :tag-map="FILE_DELAY_STATUS_MAP"
        :color-map="EXAMINE_COLOR_MAP"
        default-tag="未操作"
        other-color="grey"
      />
    </template>
    <template #fname="{ row }">
      <template v-if="row.is_grey">
        <span>{{ row.fname }}</span>
      </template>
      <template v-else>
        <file-preview :item="row" />
      </template>
    </template>
    <template #actions="{ row }">
      <forbid-select
        v-model="row.selectedType"
        :isGLobalList="true"
        :pass="true"
        :filterType="[3, 5]"
        :isCascader="true"
        class="ml-5 w-[120px]"
        :isClear="true"
        :callback="(value: any) => handleAction(row, value)"
      />
    </template>
  </table-template>
</template>

<style></style>
