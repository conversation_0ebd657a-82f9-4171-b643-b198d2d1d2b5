<script lang="ts" setup>
import { computed, type Component } from 'vue';
import TableTemplate from "#/components/table/index.vue";
import { AI_MARK_SUPERVISION_MAP, IMG_TEXT_ENTRY_MAP } from '#/constants/maps/ai-mark';
import { formatTimestampToDate, getCurrentMomentRange, getLastNDaysRange, transformTimeFields } from '#/utils/time';
import { aiTextImgRecordList } from '#/api/ai-mark'
import { mapToOptions } from '#/utils/transform';
import type { VxeGridProps } from '@vben/plugins/vxe-table';
import ImageViewer from '#/components/image/index.vue';

// 定义 FormSchema 接口以确保类型安全
interface FormSchema<T extends string = string> {
  component: string | Component; // 组件名称，如 'Input', 'Button', 'RangePicker' 等
  fieldName: T; // 表单字段名称，用于绑定数据模型
  label?: string; // 表单字段的标签文本
  componentProps?: Record<string, any>; // 组件的属性配置
  defaultValue?: any; // 默认值
  class?: string;
}

// 分页配置
const paginationOptions = {
  pageSize: 20, // 每页显示的记录数
  currentPage: 1, // 当前页码
};

// 扁平化数据处理函数 - 参考老系统逻辑
const flattenData = (records: any[]) => {
  const flattenedItems: any[] = [];

  records.forEach((record, recordIndex) => {
    const itemId = recordIndex + 1; // 序号从1开始

    // 处理 input 文本数据
    if (record.input?.text) {
      flattenedItems.push({
        imgId: `inputtext${recordIndex}`,
        itemId: itemId,
        ioType: 'input-文本',
        ...record.input.text,
        originalRecord: record
      });
    }

    // 处理 input 图片数据
    if (record.input?.images && Array.isArray(record.input.images)) {
      record.input.images.forEach((image: any, imageIndex: number) => {
        flattenedItems.push({
          imgId: `inputimg${recordIndex}`,
          itemId: itemId,
          ioType: 'input-图片',
          ...image,
          originalRecord: record
        });
      });
    }

    // 处理 output 文本数据
    if (record.output?.text) {
      flattenedItems.push({
        imgId: `outtext${recordIndex}`,
        itemId: itemId,
        ioType: 'output-文本',
        ...record.output.text,
        originalRecord: record
      });
    } else {
      // 即使output.text为空，也显示一行
      flattenedItems.push({
        imgId: `outtext${recordIndex}`,
        itemId: itemId,
        ioType: 'output-文本',
        content: '0-0-0-0-0',
        status: '',
        label: '',
        type: '',
        fileid: record.fileid,
        reviewer_name: record.reviewer_name,
        entry: record.entry,
        modify_time: record.modify_time,
        originalRecord: record
      });
    }

    // 处理 output 图片数据
    if (record.output?.images && Array.isArray(record.output.images) && record.output.images.length > 0) {
      record.output.images.forEach((image: any, imageIndex: number) => {
        flattenedItems.push({
          imgId: `outputimg${recordIndex}`,
          itemId: itemId,
          ioType: 'output-图片',
          ...image,
          originalRecord: record
        });
      });
    }
  });

  return flattenedItems;
};

// 单元格合并方法 - 参考老系统逻辑
const spanMethod = ({ row, column, rowIndex, data }: any) => {
  // 序号列合并 (第0列)
  if (column.field === 'itemId') {
    if (rowIndex === 0 || row.itemId !== data[rowIndex - 1]?.itemId) {
      let rowspan = 0;
      data.forEach((element: any) => {
        if (element.itemId === row.itemId) {
          rowspan++;
        }
      });
      return { rowspan, colspan: 1 };
    } else {
      return { rowspan: 0, colspan: 0 };
    }
  }

  // 类型列合并 (第1列)
  if (column.field === 'ioType') {
    if (rowIndex === 0 || row.imgId !== data[rowIndex - 1]?.imgId) {
      let rowspan = 0;
      data.forEach((element: any) => {
        if (element.imgId === row.imgId) {
          rowspan++;
        }
      });
      return { rowspan, colspan: 1 };
    } else {
      return { rowspan: 0, colspan: 0 };
    }
  }

  // 其他列不合并
  return { rowspan: 1, colspan: 1 };
};

// 处理搜索的方法
const handleSearch = async ({
  page,
  pageSize,
  ...formValues
}: any) => {
  // 使用 transformTimeFields 转换时间字段
  const transformedValues = transformTimeFields( formValues, [["dateRange", ["start_time", "end_time"]]]);

  const params = {
    page: page - 1,
    limit: pageSize,
    ...transformedValues // 合并转换后的参数
  };

  try {
    const response = await aiTextImgRecordList(params);
    const records = response.data.records || [];
    const flattenedItems = flattenData(records);

    return {
      items: flattenedItems,
      total: response.data.total || 0
    };
  } catch (error) {
    console.error('请求失败:', error);
    return { items: [], total: 0 };
  }
};

const rangePickerConfig: FormSchema = {
  component: 'RangePicker', // 使用 RangePicker 组件
  fieldName: 'dateRange', // 字段名称为 'dateRange'
  label: '时间范围', // 标签文本为 '时间范围'
  defaultValue: getCurrentMomentRange(7, 'X'), // 当前时刻到当前时刻
  class: 'col-span-2',
  componentProps: {
    placeholder: ['开始时间', '结束时间'], // 占位符文本
    format: 'YYYY-MM-DD HH:mm:ss', // 日期格式
    showTime: { format: 'HH:mm:ss' }, // 启用时间选择，精确到秒
    valueFormat: 'X', // 值格式为日期字符串
  },
};

// 生成输入框配置的函数
const inputConfig = (label: string, placeholder: string, fieldName: string): FormSchema => ({
  component: 'Input', // 使用 Input 组件
  fieldName, // 字段名称为标签文本的小写形式
  label, // 标签文本
  componentProps: { placeholder }, // 占位符文本
});

// 生成选择框配置的函数
const selectConfig = (label: string, placeholder: string, options: any[], fieldName: string): FormSchema => ({
  component: 'Select', // 使用 Select 组件
  fieldName, // 字段名称为标签文本的小写形式
  label, // 标签文本
  defaultValue: 'ai_mark_text_image_online_refuse',
  componentProps: {
    placeholder, // 占位符文本
    options, // 选项列表
  },
});


// 表单配置
const schema = computed(() => [
  inputConfig('操作人', '请输入操作人', 'reviewer_name'), // 操作人输入框
  inputConfig('msg_id', '请输入msg_id', 'msg_id'), // msg_id 输入框
  selectConfig('入口', '请选择入口', [
    ...mapToOptions(IMG_TEXT_ENTRY_MAP),
    ...mapToOptions(AI_MARK_SUPERVISION_MAP)
  ], 'entry'),// 入口选择框
  rangePickerConfig, // 时间范围选择器
]);

// 表格列配置
const columns: VxeGridProps["columns"] = [
  { title: '序号', field: 'itemId', width: 80, align: 'center' },
  { title: '类型', field: 'ioType', width: 120 },
  { title: 'msg_id', field: 'fileid', width: 200 },
  { title: '状态', field: 'status', width: 100 },
  {
    title: '内容',
    field: 'content',
    width: 300,
    slots: { default: 'content' } // 自定义内容显示
  },
  { title: '操作人', field: 'reviewer_name', width: 120 },
  { title: '复审类型', field: 'type', width: 100 },
  { title: '入口', field: 'entry', width: 150 },
  {
    title: '操作时间',
    field: 'modify_time',
    width: 180,
    formatter: ({ cellValue }: { cellValue: number }) =>
      formatTimestampToDate(cellValue)
  },// 映射 modify_time 为操作时间
  {
    title: '操作',
    field: 'operation',
    width: 100,
    slots: { default: 'operation' }, // 自定义操作列
  },
];


// 动态生成 searchOptions
const searchOptions = computed(() => ({
  collapsed: false,
  schemas: {
    '1': schema.value,
  },
  showCollapseButton: false,
  submitOnChange: false,
  wrapperClass: 'grid-cols-8',
}));
</script>

<template>
  <table-template :columns="columns" :pagination-options="paginationOptions" :query-method="handleSearch"
    :search-options="searchOptions" :span-method="spanMethod" :extra-config="{ border: true }">
    <!-- 自定义内容显示插槽 -->
    <template #content="{ row }">
      <div v-if="row.ioType && row.ioType.includes('文本')" class="max-w-xs">
        <div class="truncate" :title="row.content">
          {{ row.content || '-' }}
        </div>
      </div>
      <div v-else-if="row.ioType && row.ioType.includes('图片')" class="flex items-center">
        <image-viewer
          v-if="row.content && row.content !== '-'"
          :src="row.content"
          class="w-20 h-20 rounded-md"
        />
        <span v-else>-</span>
      </div>
      <div v-else>
        {{ row.content || '-' }}
      </div>
    </template>

    <!-- 自定义操作列插槽 -->
    <template #operation="{ row }">
      <a-button type="link" size="small" @click="() => console.log('查看详情:', row.originalRecord)">
        查看详情
      </a-button>
    </template>
  </table-template>
</template>