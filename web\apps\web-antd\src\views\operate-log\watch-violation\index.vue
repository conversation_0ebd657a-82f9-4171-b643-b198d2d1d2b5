<script setup lang="ts">
import type { VxeGridProps } from "#/adapter/vxe-table";
import {
  getLastNDaysRange,
  transformTimeFields,
  formatTimestampToDate,
} from "#/utils/time";
import TableTemplate from "#/components/table/index.vue";
import { getDangerIpLogs } from "#/api/operateLog";
import FileLink from "#/components/file/index.vue";
import StatusTag from "#/components/tag/index.vue";

import { VIOLATION_BUSINESS_TYPE_MAP } from "#/constants/maps";

import { mapToOptions } from "#/utils/transform";
import { findLabelByValue } from "#/utils/data";
import useForbidStore from "#/store/forbid";
import { storeToRefs } from "pinia";
import { OPERATE_MANUAL_STAUTS, DOC_MANUAL_COLOR } from "#/constants/maps/status";
import { NO_LIMIT_NUM } from "#/constants/num";

const forbidStore = useForbidStore();
const { allForbidTags } = storeToRefs(forbidStore);
const queryMethod = async ({
  page,
  pageSize,
  ...formValues
}: {
  page: number;
  pageSize: number;
  [key: string]: any;
}) => {
  console.log(page, pageSize, "jahha");
  let params: any = {
    page: page - 1,
    size: pageSize,
    ...transformTimeFields(formValues, [["dateRange", ["start", "end"]]]),
  };
  const res = await getDangerIpLogs(params);
  return {
    items: res,
    total: res.length == pageSize ? NO_LIMIT_NUM : 0,
  };
};
const paginationOptions = {
  pageSize: 20,
  currentPage: 1,
  layouts: ["PrevPage", "NextPage"],
};
const searchOptions = {
  schemas: {
    "1": [
      {
        component: "RangePicker",
        fieldName: "dateRange",
        defaultValue: getLastNDaysRange(14, 0, "X"),
        label: "日期",
        componentProps: {
          valueFormat: "X",
        },
      },
      {
        component: "Input",
        fieldName: "fileid",
        label: "fileid",
        componentProps: {
          placeholder: "请输入",
        },
      },
      {
        component: "Select",
        fieldName: "biz",
        label: "违规业务",
        componentProps: {
          options: mapToOptions(VIOLATION_BUSINESS_TYPE_MAP),
        },
      },
    ],
  },
  showCollapseButton: true,
  submitOnChange: false,
  wrapperClass: "grid-cols-4",
};

const columns: VxeGridProps["columns"] = [
  {
    field: "date",
    title: "日期",
  },
  {
    field: "user_id",
    title: "userid",
  },
  {
    field: "biz",
    title: "违规业务",
  },
  {
    field: "current_location",
    title: "当前ip",
  },
  {
    field: "register_location",
    title: "注册IP",
  },
  {
    field: "fileid",
    title: "fileid",
  },
  {
    title: "名称",
    width: 300,
    slots: {
      default: "fname",
    },
  },
  {
    field: "forbid_type",
    title: "违规标签",
    formatter: ({ cellValue }) =>
      findLabelByValue(allForbidTags.value, cellValue, "type", "name"),
  },
  {
    title: "当前状态",
    slots: {
      default: "status",
    },
  },
  {
    field: "create_time",
    title: "发布时间",
    formatter: ({ cellValue }) => formatTimestampToDate(cellValue),
  },
];
</script>

<template>
  <table-template
    :columns="columns"
    :pagination-options="paginationOptions"
    :query-method="queryMethod"
    :search-options="searchOptions"
  >
    <template #fname="{ row }">
      <file-link :item="row" />
    </template>
    <template #status="{ row }">
      <status-tag
        :status="row.record_status"
        :tag-map="OPERATE_MANUAL_STAUTS"
        :color-map="DOC_MANUAL_COLOR"
      />
    </template>
  </table-template>
</template>
