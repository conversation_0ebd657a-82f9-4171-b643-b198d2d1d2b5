<script lang="ts" setup>
import { ref } from 'vue';

import { IconifyIcon } from '@vben/icons';

import float from '#/components/common/drag.vue';
import Field from '#/components/common/Field.vue';

interface Props {
  isLoading?: boolean;
  msgId?: string;
  scene?: string;
  content?: string;
  translatedContent?: string;
  showTranslation?: boolean;
}
withDefaults(defineProps<Props>(), {
  isLoading: false,
  msgId: '',
  scene: '-',
  content: '',
  translatedContent: '',
  showTranslation: false,
});
const emit = defineEmits<{
  (e: 'toggleTranslation'): void;
}>();
const containerRef = ref<HTMLElement | undefined>(undefined);
</script>

<template>
  <div
    ref="containerRef"
    class="relative h-full w-full overflow-y-auto border p-4 font-medium"
    v-loading="isLoading"
  >
    <!-- 标题部分 -->
    <div class="mb-4 border-b pb-3">
      <div class="flex items-center justify-between">
        <field :value="msgId" copyable label="msg_id" />
        <field :value="scene" label="场景" />
      </div>
    </div>

    <!-- 内容部分 -->
    <div
      v-if="!showTranslation"
      class="max-h-[calc(100%-100px)] gap-2 overflow-y-auto"
      v-html="content"
    ></div>
    <div
      v-else
      class="max-h-[calc(100%-64px)] gap-2 overflow-y-auto text-blue-500"
      v-html="translatedContent"
    ></div>

    <!-- 翻译按钮 -->
    <float
      v-if="containerRef && !isLoading"
      :bottom="20"
      :parent-element="containerRef"
      :right="20"
      position-base="bottom-right"
    >
      <a-button
        :class="{ 'bg-blue-500 text-white': showTranslation }"
        class="items flex items-center justify-center shadow-md transition-transform hover:scale-110"
        type="primary"
        @click="emit('toggleTranslation')"
      >
        <iconify-icon icon="ant-design:translation-outlined" />
        翻译
      </a-button>
    </float>
  </div>
</template>
