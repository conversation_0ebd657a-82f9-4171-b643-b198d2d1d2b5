<script setup lang="ts">
import type { VxeGridProps } from "#/adapter/vxe-table";
import { ref } from "vue";
import TableTemplate from "#/components/table/index.vue";
import { ACCOUNT_OPERATE_TYPE_MAP } from "#/constants/maps";
import { EMPTY_OPTION } from "#/constants/options";
import { NO_LIMIT_NUM } from "#/constants/num";

import { mapToOptions, generateUrlParams } from "#/utils/transform";
import { useUserStore } from "#/store";
const userStore = useUserStore();
import {
  getLastNDaysRange,
  transformTimeFields,
  formatTimestampToDate,
} from "#/utils/time";
// 通过传入不同查询方法实现组件复用
const props = defineProps({
  queryMethod: {
    type: Function,
    default: () => {},
  },
  type: {
    type: String,
  },
});

// 搜索配置
const searchOptions = {
  collapsed: false, // 默认展开
  schemas: {
    "1": [
      {
        component: "Input",
        fieldName: "userid",
        label: "userId",
      },
      {
        component: "Input",
        fieldName: "reviewer_name",
        label: "操作人",
      },
      {
        component: "Select",
        fieldName: "status",
        label: "操作类型",
        componentProps: {
          options: [
            ...EMPTY_OPTION,
            ...mapToOptions(ACCOUNT_OPERATE_TYPE_MAP[props.type]),
          ],
        },
      },

      {
        component: "RangePicker",
        fieldName: "dateRange",
        label: "时间",
        defaultValue: getLastNDaysRange(14, 0, "X"),
        componentProps: {
          valueFormat: "X",
          showTime: true,
        },
      },
    ],
  },
  showCollapseButton: true,
  submitOnChange: false,
  wrapperClass: "grid-cols-4",
};

// 表格列配置
const columns: VxeGridProps["columns"] = [
  {
    type: "seq",
    align: "center",
    title: "序号",
    fixed: "left",
    width: 100,
  },
  { field: "userid", title: "userId" },
  {
    field: "status",
    title: "操作类型",
    visible: props.type === "ban",
    formatter: ({ cellValue }) => ACCOUNT_OPERATE_TYPE_MAP[props.type][cellValue],
  },
  {
    field: "operation",
    title: "操作类型",
    visible: props.type === "share",
    formatter: ({ cellValue }) => ACCOUNT_OPERATE_TYPE_MAP[props.type][cellValue],
  },
  { field: "reason", title: "操作原因" },
  {
    title: "操作时间",
    formatter: ({ row }) =>
      props.type === "ban"
        ? formatTimestampToDate(row.nowtime)
        : formatTimestampToDate(row.operation_time),
  },
  {
    title: "生效时间",
    formatter: ({ row }) => formatTimestampToDate(row.effect_time || row.operation_time),
  },
  {
    field: "expire_time",
    title: "失效时间",
    formatter: ({ row }) =>
      formatTimestampToDate(
        row.expire_time,
        "YYYY-MM-DD HH:mm:ss",
        row.operation === "unban" || row.operation === "delete" ? "-" : "永久"
      ),
  },
  { field: "reviewer_name", title: "操作人" },
];

const getLogs = props.queryMethod;
const paginationOptions = {
  pageSize: 20,
  currentPage: 1,
  layouts: ["PrevPage", "NextPage"],
};
const solveParams = (page, pageSize, formValues: any) => {
  let params = {
    page: page - 1,
    limit: pageSize,
    ...transformTimeFields(formValues, [["dateRange", ["start_time", "end_time"]]]),
  };
  return params;
};
// 查询方法
const queryMethod = async ({
  page,
  pageSize,
  ...formValues
}: {
  page: number;
  pageSize: number;
  [key: string]: any;
}) => {
  let params = solveParams(page, pageSize, formValues);
  const res = await getLogs(params);
  return {
    items: res.data,
    total: res.more ? NO_LIMIT_NUM : 0,
  };
};
// 表格组件引用
const tableTemplateRef = ref<any>(null);
const handleExport = async () => {
  let searchData = await tableTemplateRef.value.getFormvalues();
  let searchParams = solveParams(1, 20, searchData);
  // 构造需要传递的参数对象
  const params = {
    user_id: userStore.userid,
    ...searchParams,
  };
  const url = `/manual/export/illegal_user_log${generateUrlParams(params)}`;

  window.location.href = url;
};
</script>

<template>
  <table-template
    ref="tableTemplateRef"
    :columns="columns"
    :pagination-options="paginationOptions"
    :query-method="queryMethod"
    :search-options="searchOptions"
    :has-export="true"
    @export="handleExport"
  >
  </table-template>
</template>
