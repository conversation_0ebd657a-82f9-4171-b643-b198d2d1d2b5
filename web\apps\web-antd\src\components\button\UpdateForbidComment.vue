<script setup lang="ts">
import { ref } from 'vue';
import { CheckOutlined, DownOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import type { PropType } from 'vue';
import { typeData } from '#/constants/maps/examine-log';

// 定义 statusType
const statusType = {
  pass: 'ok',
  verify: 'verifying',
  forbid: 'forbid',
  undo: 'undo',
};

// Props 定义
const props = defineProps({
  platform: {
    type: String,
    default: 'wps_account',
  },
  tabPlatform: {
    type: String,
    default: '',
  },
  isWatermark: {
    type: Boolean,
    default: false,
  },
  isV2: {
    type: Boolean,
    default: false,
  },
  scope: {
    type: Object as PropType<any>,
    required: true,
  },
  params: {
    type: Object,
    default: () => ({}),
  },
  callBack: {
    type: Function as PropType<() => void>,
    default: undefined,
  },
  entry: {
    type: String,
    default: 'taskGloble',
  },
  callback: {
    type: Function as PropType<(scope: any, status: number) => any>,
    required: true,
  },
});

// 控制整体加载状态
const loading = ref(false);

const alloperate = async (item: any, status: number) => {
  loading.value = true;
  try {
    await props.callback(item, status);
    loading.value = false;
  } catch (error) {
    message.error('操作失败');
    console.error(error);
  }
};
</script>

<template>
  <div class="inline-block">
    <!-- 加载状态控制 -->
    <a-spin :spinning="loading">
      <!-- 判断 `forbid` 状态 -->
      <template v-if="scope.status === 'forbid'">
        <a-button
          type="default"
          class="flex items-center justify-center space-x-1"
          @click="alloperate(scope, 0)"
        >
          <span>通过</span>
          <CheckOutlined />
        </a-button>
      </template>
      <!-- 其他状态 -->
      <template v-else>
        <a-dropdown :trigger="['click']">
          <a-button
            type="default"
            class="flex items-center justify-center space-x-1"
          >
            <span>操作</span>
            <template #icon>
              <DownOutlined class="order-last" />
            </template>
          </a-button>
          <template #overlay>
            <a-menu @click="({ key }: any) => alloperate(scope, key)">
              <a-menu-item v-for="item in typeData" :key="item.value">
                {{ item.name }}
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </template>
    </a-spin>
  </div>
</template>
