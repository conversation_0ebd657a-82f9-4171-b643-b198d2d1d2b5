/**
 * 该文件可自行根据业务逻辑进行调整
 */
import type { HttpResponse } from '@vben/request';

import { useAppConfig } from '@vben/hooks';
import { preferences } from '@vben/preferences';
import {
  authenticateResponseInterceptor,
  errorMessageResponseInterceptor,
  RequestClient,
} from '@vben/request';
import { useAccessStore } from '@vben/stores';

import { message } from 'ant-design-vue';

import { useAuthStore } from '#/store';
import { MessageShowMode, SuccessMessageType } from '#/types/api';

import { refreshTokenApi } from './core';

import {
  getXAccessToken,
  setAccessKey,
  randomKeyType,
} from '#/utils/rainbowEncrypt';
import { objectToQueryString } from '#/utils';
import { getRouterId } from '#/utils/common';

const { apiURL } = useAppConfig(import.meta.env, import.meta.env.PROD);

// 定义加密相关的常量
const SHA_LEN = [16, 8, 16];
const accessType = randomKeyType(); // 随机某种加密方式
/**
 * 获取加密的 key 和 accessType
 */
function getEncryptionConfig(password?: string) {
  const accessKey = password
    ? setAccessKey(password)
    : localStorage.getItem('accessKey');
  const key = accessKey ? accessKey.substring(0, SHA_LEN[accessType]) : '';
  return { key, accessType };
}

// 提取公共的header配置
function getCommonHeaders(accessType: number) {
  return {
    'X-Cipher': accessType + 1,
    'Content-Type': 'application/json',
    'X-User-Agent': window.isElectron ? 'Electron' : '',
  };
}

// 提取公共的请求头处理逻辑
function handleRequestHeaders(config: any) {
  const nowTime = Math.floor(Date.now() / 1000);
  const { url, data } = config;
  const isLoginUrl = url === '/manual/login';

  // 从data中提取前端控制参数
  let frontPayload = {};
  if (data && typeof data === 'object') {
    frontPayload = data._frontPayload || {};
    delete data._frontPayload;
  }

  // 添加时间戳和user_id逻辑
  if (config['method']?.toLowerCase() === 'get') {
    config['params'] = {
      ...config['data'],
      _t: nowTime,
      user_id: localStorage.getItem('user_id'),
    };
    delete config['data'];
  } else {
    if (config.url?.includes('?')) {
      config.url += '&_t=' + nowTime;
    } else {
      config.url += '?_t=' + nowTime;
    }
    if (!config.url?.includes('login'))
      config.url += '&user_id=' + localStorage.getItem('user_id');
  }

  const commonHeaders = getCommonHeaders(accessType);

  if (isLoginUrl) {
    const { key } = getEncryptionConfig(data.password);
    config.headers = {
      ...config.headers,
      ...commonHeaders,
      'X-Access-Token': getXAccessToken(key, accessType, data.password),
    } as any;
    data.password = key;
  } else {
    const key =
      localStorage.getItem('accessKey')?.substring(0, SHA_LEN[accessType]) ||
      '';
    config.headers = {
      ...config.headers,
      ...commonHeaders,
      'X-Access-Token': getXAccessToken(key, accessType),
      entranceid: getRouterId(), // 后台根据前端页面路由id判断接口权限
    } as any;
  }

  // 将前端控制参数保存到config中
  config._frontPayload = frontPayload;

  return config;
}

function createRequestClient(baseURL: string) {
  const client = new RequestClient({
    baseURL,
  });

  /**
   * 重新认证逻辑
   */
  async function doReAuthenticate() {
    console.warn('Access token or refresh token is invalid or expired. ');
    const accessStore = useAccessStore();
    const authStore = useAuthStore();
    accessStore.setAccessToken(null);
    if (
      preferences.app.loginExpiredMode === 'modal' &&
      accessStore.isAccessChecked
    ) {
      accessStore.setLoginExpired(true);
    } else {
      await authStore.logout();
    }
  }

  /**
   * 刷新token逻辑
   */
  async function doRefreshToken() {
    const accessStore = useAccessStore();
    const resp = await refreshTokenApi();
    const newToken = resp.data;
    accessStore.setAccessToken(newToken);
    return newToken;
  }

  function formatToken(token: null | string) {
    return token ? `Bearer ${token}` : null;
  }

  // 请求头处理
  client.addRequestInterceptor({
    fulfilled: async (config) => {
      const accessStore = useAccessStore();
      config = handleRequestHeaders(config);
      config.headers.Authorization = formatToken(accessStore.accessToken);
      config.headers['Accept-Language'] = preferences.app.locale;
      return config;
    },
  });

  // response数据解构
  client.addResponseInterceptor<HttpResponse>({
    fulfilled: (response) => {
      const { data: responseData, status } = response;

      const { code, data, result, message: errorMessage } = responseData;
      
      // 获取消息显示模式配置，默认为None
      const showMessage = (response.config as any)._frontPayload?.showMessage || MessageShowMode.None;
      // 获取成功消息类型配置，默认为Success
      const successMessageType = (response.config as any)._frontPayload?.successMessageType || SuccessMessageType.Success;
      
      // 根据showMessage配置决定是否显示消息
      if (errorMessage && showMessage !== MessageShowMode.None) {
        const shouldShow = 
          showMessage === MessageShowMode.All || 
          (showMessage === MessageShowMode.Success && result === true) ||
          (showMessage === MessageShowMode.Fail && result === false);
          
        if (shouldShow) {
          if (result === false) {
            message.error(errorMessage);
          } else {
            // 根据successMessageType配置决定使用success还是info
            if (successMessageType === SuccessMessageType.Info) {
              message.info(errorMessage);
            } else {
              message.success(errorMessage);
            }
          }
          // 如果显示了消息，直接返回，不继续后续处理
          return responseData;
        }
      }
      
      if (status >= 200 && status < 400 && code === 0) {
        return data;
      }
      // kcs预览失败时需要获取到这里的数据
      // if(responseData.result ){
      return responseData;
      // }

      throw Object.assign({}, response, { response });
    },
  });

  // token过期的处理
  client.addResponseInterceptor(
    authenticateResponseInterceptor({
      client,
      doReAuthenticate,
      doRefreshToken,
      enableRefreshToken: preferences.app.enableRefreshToken,
      formatToken,
    }),
  );

  // 通用的错误处理,如果没有进入上面的错误处理逻辑，就会进入这里
  client.addResponseInterceptor(
    errorMessageResponseInterceptor((msg: string, error) => {
      // 检查是否设置了silentError标记，如果设置了则不显示错误消息
      if (error?.config?._frontPayload?.silentError) {
        return;
      }
      // 检查是否已经显示过消息
      if (error?.config?._frontPayload?.messageShown) {
        return;
      }
      if (error?.config?._frontPayload?.customError) {
        // 获取响应数据中的错误信息
        const responseData = error?.response?.data ?? {};
        const errorMessage = responseData?.error ?? responseData?.message ?? '';
        const customError = error.config._frontPayload.customError;

        const responseDataStr = responseData;
        // 处理customError为数组的情况
        if (Array.isArray(customError)) {
          // 遍历所有规则，查找匹配的规则
          for (const rule of customError) {
            if (rule.matchText && responseDataStr.includes(rule.matchText)) {
              message.error(rule.message || errorMessage);
              return;
            }
          }
        }
        // 处理customError为单个对象的情况
        else if (typeof customError === 'object') {
          // 直接检查responseData中是否包含matchText
          if (
            customError.matchText &&
            responseDataStr.includes(customError.matchText)
          ) {
            message.error(customError.message || errorMessage);
            return;
          } else if (!customError.matchText) {
            // 如果customError不包含matchText属性，则直接显示customError
            message.error(
              typeof customError === 'string'
                ? customError
                : customError.message || errorMessage,
            );
            return;
          }
        }
        // 处理customError为字符串的情况
        else if (typeof customError === 'string') {
          message.error(customError);
          return;
        }
      }
      // 这里可以根据业务进行定制,你可以拿到 error 内的信息进行定制化处理，根据不同的 code 做不同的提示，而不是直接使用 message.error 提示 msg
      // 当前mock接口返回的错误字段是 error 或者 message
      const responseData = error?.response?.data ?? {};
      const errorMessage = responseData?.error ?? responseData?.message ?? '';
      // 如果没有错误信息，则会根据状态码进行提示
      message.error(errorMessage || msg);
    }),
  );

  return client;
}
export function requestPostQuery(url: string, params: any, config: any = {}) {
  if (params.method === 'GET') {
    url += '?' + objectToQueryString(params) + `&user_id=${params.user_id}`;
  } else {
    url += `?uri=${params.uri}&method=POST&user_id=${params.user_id}`;
    delete params.uri;
    delete params.method;
  }

  return requestClient.post(url, params, config);
}
export const requestClient = createRequestClient(apiURL);
export const baseRequestClient = new RequestClient({ baseURL: apiURL });
