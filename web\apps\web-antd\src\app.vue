<script lang="ts" setup>
import { computed, onMounted, onUnmounted } from 'vue';
import { useRoute } from 'vue-router';

import { useAntdDesignTokens } from '@vben/hooks';
import { preferences, usePreferences } from '@vben/preferences';

import { App, ConfigProvider, theme } from 'ant-design-vue';

import { antdLocale } from '#/locales';
import useWebSocket from '#/composables/net/use-websocket';
import { useAuthStore } from '#/store';
import { getItem } from '#/utils/save';
import { timerManager, updateLastActiveTime as updateActiveTime } from '#/utils/timer-manager';
defineOptions({ name: 'App' });

const { isDark } = usePreferences();
const { tokens } = useAntdDesignTokens();
import './assets/styles/global.scss'; // 引入全局自定义样式

const route = useRoute();
const authStore = useAuthStore();

const tokenTheme = computed(() => {
  const algorithm = isDark.value
    ? [theme.darkAlgorithm]
    : [theme.defaultAlgorithm];

  // antd 紧凑模式算法
  if (preferences.app.compact) {
    algorithm.push(theme.compactAlgorithm);
  }

  return {
    algorithm,
    token: tokens,
  };
});

useWebSocket();

// 更新最后活动时间
function updateLastActiveTime() {
  updateActiveTime();

  if (
    !getItem("user_id") &&
    route.name !== "Login" &&
    route.name !== "devSwitch" &&
    route.name !== "face_verify" &&
    route.name !== "mfacode" &&
    route.name !== "pageError"
  ) {
    removeLoginInfo();
  }
}

// 清除登录信息
function removeLoginInfo() {
  // 通知定时器管理器正在登出
  timerManager.setLoggingOut(true);

  // 停止所有定时器
  timerManager.stopTimers();

  // 执行登出
  authStore.logout().finally(() => {
    timerManager.setLoggingOut(false);
  });
}

// 组件挂载时的逻辑
onMounted(() => {
  // 添加事件监听器
  document.onmousemove =
    document.onmousedown =
    document.onclick =
    document.onkeydown =
    document.onkeyup =
      updateLastActiveTime;

  // 启动定时器管理器
  timerManager.startTimers();
});

// 组件卸载时清理
onUnmounted(() => {
  timerManager.stopTimers();
});
</script>

<template>
  <ConfigProvider :locale="antdLocale" :theme="tokenTheme">
    <App>
      <RouterView />
    </App>
  </ConfigProvider>
</template>
