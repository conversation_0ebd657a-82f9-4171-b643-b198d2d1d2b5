import { ref, reactive, computed, nextTick } from 'vue';

/**
 * 图像变换接口
 */
export interface Transform {
  deg: number;   // 当前图像的旋转角度（以度为单位）
  flipX: number; // 是否水平翻转，1表示正常，-1表示翻转
  flipY: number; // 是否垂直翻转，1表示正常，-1表示翻转
  offsetX: number; // 当前图像的X轴偏移量
  offsetY: number; // 当前图像的Y轴偏移量
}

/**
 * 变换更新接口
 */
export interface TransformPayload {
  curWidth: number;  // 当前大图的宽度
  curHeight: number; // 当前大图的高度
  deg: number;       // 当前大图的旋转角度
  scaleX: number;    // 当前大图的水平缩放比例
  scaleY: number;    // 当前大图的垂直缩放比例
  offsetX: number;   // 当前大图的X轴偏移
  offsetY: number;   // 当前大图的Y轴偏移
  imgWidth?: number; // 原始图像宽度
  imgHeight?: number; // 原始图像高度
}

/**
 * 变换事件接口
 */
export interface TransformEvent {
  offsetX: number;
  offsetY: number;
  deg: number;
}

/**
 * 容器尺寸接口
 */
export interface ContainerSize {
  width: number;
  height: number;
}

/**
 * 使用图像变换逻辑的Composable
 */
export function useImageTransformer(emit?: (e: 'afterTransform', payload: TransformEvent) => void) {
  /** 是否显示大图 */
  const showViewer = ref(false);
  /** 是否显示鸟瞰图 */
  const showSmallBox = ref(false);
  /** 用户点击 × 关闭鸟瞰图后，避免再次显示 */
  const closedByUser = ref(false);

  /** 从子组件回传的变换数据 */
  const transform = reactive<Transform>({
    deg: 0,  // 默认不旋转
    flipX: 1,  // 默认不水平翻转
    flipY: 1,  // 默认不垂直翻转
    offsetX: 0,  // 默认不偏移
    offsetY: 0,  // 默认不偏移
  });

  /** 原始图像尺寸 - 加载后获取 */
  const imageWidth = ref(0);
  const imageHeight = ref(0);

  /** 子组件回传的当前大图实际渲染宽高 */
  const currentWidth = ref(0);  // 当前大图实际显示的宽度
  const currentHeight = ref(0); // 当前大图实际显示的高度

  /** 子组件回传的容器（可视区域）大小 */
  const containerWidth = ref(0);  // 大图查看器的容器宽度
  const containerHeight = ref(0); // 大图查看器的容器高度

  /** 鸟瞰图中原图宽高(加载后得到) */
  const bWidth = ref(0);  // 鸟瞰图中显示的原图宽度
  const bHeight = ref(0); // 鸟瞰图中显示的原图高度

  /** 小窗容器大小(父组件中固定) */
  const imgContainerWidth = ref(0);  // 小窗容器的宽度
  const imgContainerHeight = ref(0); // 小窗容器的高度

  /** 拖拽相关 */
  const isDragging = ref(false);

  // refs
  const imgContainerRef = ref<HTMLElement | null>(null);
  const floatBoxRef = ref<HTMLDivElement | null>(null);
  const imgElementRef = ref<HTMLImageElement | null>(null);
  const innerBoxRef = ref<HTMLDivElement | null>(null);

  /** 打开大图 */
  function onPreviewClick() {
    console.log('useImageTransformer - onPreviewClick 被调用，设置 showViewer = true');
    showViewer.value = true;   // 打开大图查看器
    closedByUser.value = false;  // 重置关闭标志
    checkNeedShowBirdView();  // 检查是否需要显示鸟瞰图
    console.log('useImageTransformer - onPreviewClick 完成，showViewer =', showViewer.value);
  }

  /** 关闭大图 */
  function onCloseViewer() {
    showViewer.value = false;   // 关闭大图查看器
    closedByUser.value = false; // 重置关闭标志
  }

  /** 由子组件 afterTransform 回传 */
  function updateTransform(payload: TransformPayload) {
    currentWidth.value = payload.curWidth;
    currentHeight.value = payload.curHeight;
    transform.deg = payload.deg;
    transform.flipX = payload.scaleX;
    transform.flipY = payload.scaleY;
    transform.offsetX = payload.offsetX;
    transform.offsetY = payload.offsetY;

    // 如果提供了原始图像尺寸，更新本地存储
    if (payload.imgWidth && payload.imgHeight) {
      imageWidth.value = payload.imgWidth;
      imageHeight.value = payload.imgHeight;
    }

    checkNeedShowBirdView();  // 根据大图的尺寸判断是否显示鸟瞰图

    // 当鸟瞰图显示时，确保内部元素正确更新
    if (showSmallBox.value) {
      nextTick(() => {
        getInnerSize();
      });
    }
  }

  /** 由子组件 containerRectChange 回传 */
  function updateContainerSize(size: ContainerSize) {
    containerWidth.value = size.width;  // 更新大图查看器容器的宽度
    containerHeight.value = size.height; // 更新大图查看器容器的高度
    checkNeedShowBirdView();  // 根据新容器尺寸判断是否显示鸟瞰图
  }

  /** 判断是否需要显示鸟瞰图 */
  function checkNeedShowBirdView() {
    if (closedByUser.value) return;  // 如果用户关闭了鸟瞰图，则不再显示

    const viewportW = containerWidth.value;  // 获取大图查看器容器的宽度
    const viewportH = containerHeight.value; // 获取大图查看器容器的高度

    // 判断当前大图是否超出了可视区域，如果超出则显示鸟瞰图
    const shouldShow = currentWidth.value > viewportW || currentHeight.value > viewportH;

    // 如果显示状态有变化，更新并在显示后更新内部裁剪
    if (shouldShow !== showSmallBox.value) {
      showSmallBox.value = shouldShow;
      if (shouldShow) {
        nextTick(() => {
          // 确保容器尺寸已更新
          if (imgContainerRef.value) {
            imgContainerWidth.value = imgContainerRef.value.clientWidth;
            imgContainerHeight.value = imgContainerRef.value.clientHeight;
          }
          // 确保位置正确
          applyFloatBoxStyle();
          // 计算裁剪区域
          getInnerSize();
        });
      }
    }
  }

  /** 关闭鸟瞰图 */
  function closeSmallBox() {
    showSmallBox.value = false;  // 关闭鸟瞰图
    closedByUser.value = true;   // 标记为用户手动关闭鸟瞰图
  }

  /** 鸟瞰图加载后，获取原图在小窗中的宽高 */
  function onBirdImageLoad() {
    nextTick(() => {
      const imgElement = imgElementRef.value;
      if (!imgElement) return;
      const rect = imgElement.getBoundingClientRect();
      bWidth.value = rect.width;  // 获取鸟瞰图图片的实际宽度
      bHeight.value = rect.height; // 获取鸟瞰图图片的实际高度

      // 获取原始图像尺寸
      imageWidth.value = imgElement.naturalWidth;
      imageHeight.value = imgElement.naturalHeight;


      // 更新小窗容器尺寸
      if (imgContainerRef.value) {
        imgContainerWidth.value = imgContainerRef.value.clientWidth;
        imgContainerHeight.value = imgContainerRef.value.clientHeight;
      }

      // 确保初始位置正确
      applyFloatBoxStyle();
      getInnerSize();  // 计算鸟瞰图的可视区域
    });
  }

  /** 计算鸟瞰图中缩略图的旋转/翻转 */
  const birdStyle = computed(() => {
    if (!showSmallBox.value && closedByUser.value) return {};  // 如果不显示鸟瞰图，返回空对象

    const deg = transform.deg;  // 获取当前旋转角度
    let flipX = transform.flipX; // 获取当前水平翻转状态
    let flipY = transform.flipY; // 获取当前垂直翻转状态

    // 如果旋转角度为90或270度，则交换水平和垂直翻转
    if (deg % 180 !== 0) {
      flipX = transform.flipY;
      flipY = transform.flipX;
    }

    return {
      transform: `rotate(${deg}deg) scaleX(${flipX}) scaleY(${flipY})`,  // 应用旋转和翻转
    };
  });

  /** 计算floatBox样式（表示可视区域） */
  const floatStyle = computed(() => {
    // 条件判断：当不显示小盒子且用户主动关闭时，不应用样式
    if (!showSmallBox.value && closedByUser.value) return null;

    // 防御性检查：当关键尺寸未加载时返回默认居中样式
    if (!bWidth.value || !bHeight.value || !currentWidth.value || !currentHeight.value || !imageWidth.value || !imageHeight.value) {
      console.log('尺寸参数不全，使用默认居中样式');
      return {
        width: `${bWidth.value}px`,
        height: `${bHeight.value}px`,
        top: "50%",
        left: "50%",
        transform: "translate(-50%, -50%)"
      };
    }

    // ======== 使用与主视图完全对应的缩放和位置计算逻辑 ========


    // 2. 计算视口比例 - 容器大小与图片显示大小的比值
    const viewportRatioW = Math.min(1, containerWidth.value / currentWidth.value);
    const viewportRatioH = Math.min(1, containerHeight.value / currentHeight.value);

    // 3. 计算鸟瞰图中的框大小 - 这表示可见区域
    const fWidth = bWidth.value * viewportRatioW;
    const fHeight = bHeight.value * viewportRatioH;

    // 4. 计算鸟瞰图中心点
    const birdCenterX = imgContainerWidth.value / 2;
    const birdCenterY = imgContainerHeight.value / 2;

    // 5. 计算偏移比例 - 注意方向关系
    // 在主视图中，正偏移表示图像向右/下移动
    // 在鸟瞰图中，我们需要相反的方向移动框，所以用负号
    const offsetRatioX = -transform.offsetX / currentWidth.value;
    const offsetRatioY = -transform.offsetY / currentHeight.value;

    // 6. 将比例转换为像素偏移
    const birdOffsetX = offsetRatioX * bWidth.value;
    const birdOffsetY = offsetRatioY * bHeight.value;

    // 7. 最终位置计算 - 框从图像中心开始，然后根据偏移调整
    const floatLeft = birdCenterX - fWidth / 2 + birdOffsetX -300;
    const floatTop = birdCenterY - fHeight / 2 + birdOffsetY -300;

    // 返回样式对象
    return {
      width: `${fWidth}px`,
      height: `${fHeight}px`,
      left: `${floatLeft}px`,
      top: `${floatTop}px`,
      opacity: 0.7,
      border: '300px solid rgba(128, 128, 128, 0.5)'
    };
  });


  /** 开始拖动 */
  function startDrag(event: MouseEvent | TouchEvent) {
    event.preventDefault();

    if (!floatBoxRef.value) return;

    // 获取浮动框当前位置
    const box = floatBoxRef.value;
    const boxRect = box.getBoundingClientRect();
    const initialLeft = parseFloat(box.style.left) || boxRect.left;
    const initialTop = parseFloat(box.style.top) || boxRect.top;

    let startX: number, startY: number;
    if (event instanceof MouseEvent) {
      startX = event.clientX;
      startY = event.clientY;
    } else {
      // 确保有触摸点
      if (!event.touches || event.touches.length === 0) return;
      startX = event.touches[0]!.clientX;
      startY = event.touches[0]!.clientY;
    }

    isDragging.value = true;

    // 移动处理函数
    const moveHandler = (e: MouseEvent | TouchEvent) => {
      e.preventDefault();

      if (!isDragging.value || !floatBoxRef.value) return;

      let moveX: number, moveY: number;
      if (e instanceof MouseEvent) {
        moveX = e.clientX;
        moveY = e.clientY;
      } else {
        if (!e.touches || e.touches.length === 0) return;
        moveX = e.touches[0]!.clientX;
        moveY = e.touches[0]!.clientY;
      }

      // 计算移动距离
      const deltaX = moveX - startX;
      const deltaY = moveY - startY;

      // 直接更新浮动框位置
      let newLeft = initialLeft + deltaX;
      let newTop = initialTop + deltaY;

      // 获取容器边界
      const containerRect = imgContainerRef.value?.getBoundingClientRect();
      if (containerRect) {
        // 考虑到300px边框，计算实际的内容区域
        const boxWidth = parseFloat(box.style.width) || boxRect.width;
        const boxHeight = parseFloat(box.style.height) || boxRect.height;

        // 计算边界限制 - 考虑边框的宽度
        const minLeft = -300-boxWidth;
        const minTop = -300-boxHeight;
        // 修正右边和下边界，需要考虑300px的右边框和下边框
        const maxLeft = containerRect.width  - 300;
        const maxTop = containerRect.height  - 300;

        // 应用边界限制
        newLeft = Math.max(minLeft, Math.min(newLeft, maxLeft));
        newTop = Math.max(minTop, Math.min(newTop, maxTop));
      }

      // 更新DOM位置
      box.style.left = `${newLeft}px`;
      box.style.top = `${newTop}px`;

      // 计算这个移动相对于鸟瞰图的比例
      if (!bWidth.value || !bHeight.value || !currentWidth.value || !currentHeight.value) return;

      // 从DOM位置计算到相对于鸟瞰图中心的偏移
      const boxWidth = parseFloat(box.style.width) || boxRect.width;
      const boxHeight = parseFloat(box.style.height) || boxRect.height;

      // 计算鸟瞰图中心点
      const birdCenterX = imgContainerWidth.value / 2;
      const birdCenterY = imgContainerHeight.value / 2;

      // 框的中心点 - 考虑300px边框偏移
      const boxCenterX = newLeft + boxWidth / 2 + 300;
      const boxCenterY = newTop + boxHeight / 2 + 300;

      // 框中心与鸟瞰图中心的距离
      const offsetX = boxCenterX - birdCenterX;
      const offsetY = boxCenterY - birdCenterY;

      // 计算偏移比例
      const offsetRatioX = offsetX / bWidth.value;
      const offsetRatioY = offsetY / bHeight.value;

      // 转换为大图偏移
      const imageOffsetX = -offsetRatioX * currentWidth.value;
      const imageOffsetY = -offsetRatioY * currentHeight.value;


      // 直接更新本地transform对象，确保鸟瞰图和大图同步
      transform.offsetX = imageOffsetX;
      transform.offsetY = imageOffsetY;

      // 更新大图位置
      if (emit) {
        emit('afterTransform', {
          offsetX: imageOffsetX,
          offsetY: imageOffsetY,
          deg: transform.deg,
        });
      }
    };

    // 结束处理函数
    const endHandler = () => {
      isDragging.value = false;

      // 移除事件监听
      document.removeEventListener('mousemove', moveHandler);
      document.removeEventListener('touchmove', moveHandler);
      document.removeEventListener('mouseup', endHandler);
      document.removeEventListener('touchend', endHandler);
      document.removeEventListener('touchcancel', endHandler);

      // 触发一次内部元素裁剪重新计算
      nextTick(() => {
        getInnerSize();
      });
    };

    // 添加事件监听
    document.addEventListener('mousemove', moveHandler);
    document.addEventListener('touchmove', moveHandler, { passive: false });
    document.addEventListener('mouseup', endHandler);
    document.addEventListener('touchend', endHandler);
    document.addEventListener('touchcancel', endHandler);
  }

  /** 计算innerBox的clipPath */
  function getInnerSize() {
    // 如果鸟瞰图未显示，不需要计算裁剪
    if (!showSmallBox.value || !showViewer.value) return;

    // 使用Vue的nextTick确保在DOM更新后执行测量
    nextTick(() => {
      // 获取关键元素的DOM引用
      const imgElement = imgElementRef.value;    // 鸟瞰图中的图片元素
      const floatBox = floatBoxRef.value;        // 表示可视区域的浮动框
      const innerBox = innerBoxRef.value;        // 浮动框内部需要裁剪的元素

      // 安全校验：确保所有元素都已挂载
      if (!imgElement || !floatBox || !innerBox) {
        console.log('获取DOM元素失败，无法计算裁剪区域');
        return;
      }

      // 确保floatBox位置正确应用
      applyFloatBoxStyle();

      // 获取元素位置和尺寸
      const imgRect = imgElement.getBoundingClientRect();
      const floatRect = floatBox.getBoundingClientRect();

      /**
       * 检查图片和浮动框的相对位置关系
       * 只有当浮动框超出图片区域时，才需要裁剪
       */

      // 计算图片的边界
      const imgLeft = imgRect.left;
      const imgTop = imgRect.top;
      const imgRight = imgRect.right;
      const imgBottom = imgRect.bottom;

      // 计算浮动框的边界
      const boxLeft = floatRect.left;
      const boxTop = floatRect.top;
      const boxRight = floatRect.right;
      const boxBottom = floatRect.bottom;

      // 计算裁剪值：当浮动框超出图片时，需要裁剪
      // 浮动框左侧超出图片左侧
      const clipLeft = Math.max(0, imgLeft - boxLeft);

      // 浮动框上侧超出图片上侧
      const clipTop = Math.max(0, imgTop - boxTop);

      // 浮动框右侧超出图片右侧
      const clipRight = Math.max(0, boxRight - imgRight);

      // 浮动框下侧超出图片下侧
      const clipBottom = Math.max(0, boxBottom - imgBottom);

      // 应用裁剪，使用CSS的inset()函数
      innerBox.style.clipPath = `inset(${clipTop}px ${clipRight}px ${clipBottom}px ${clipLeft}px)`;

    });
  }

  /** 应用浮动框样式，确保正确的初始位置 */
  function applyFloatBoxStyle() {
    if (!floatBoxRef.value) return;

    // 获取计算后的样式
    const style = floatStyle.value;
    if (!style) return;

    // 应用到DOM元素
    const box = floatBoxRef.value;

    // 直接应用计算的样式到DOM元素
    if ('width' in style && style.width) box.style.width = style.width;
    if ('height' in style && style.height) box.style.height = style.height;
    if ('left' in style && style.left) box.style.left = style.left;
    if ('top' in style && style.top) box.style.top = style.top;
    if ('transform' in style && style.transform) box.style.transform = style.transform;
    if ('opacity' in style && style.opacity !== undefined) box.style.opacity = String(style.opacity);
    if ('border' in style && style.border) box.style.border = style.border;
  }

  // 初始化和监听方法供组件调用
  function initialize() {
    // 在组件挂载时调用
    if (imgContainerRef.value) {
      imgContainerWidth.value = imgContainerRef.value.clientWidth;
      imgContainerHeight.value = imgContainerRef.value.clientHeight;
    }
  }

  return {
    // 状态
    showViewer,
    showSmallBox,
    transform,
    isDragging,

    // 尺寸和位置
    currentWidth,
    currentHeight,
    containerWidth,
    containerHeight,
    imageWidth,
    imageHeight,
    bWidth,
    bHeight,

    // 计算属性
    birdStyle,
    floatStyle,

    // DOM引用
    imgContainerRef,
    floatBoxRef,
    imgElementRef,
    innerBoxRef,

    // 方法
    onPreviewClick,
    onCloseViewer,
    updateTransform,
    updateContainerSize,
    closeSmallBox,
    onBirdImageLoad,
    startDrag,
    initialize,
    getInnerSize,
    applyFloatBoxStyle
  };
}
