<script lang="ts" setup>
import { ref, onMounted, onUnmounted, watch, shallowRef, computed } from 'vue';
import { getRemoteTasks, applyRemoteTask } from '#/api/download-cloud';
import { useClipboard, useThrottleFn } from '@vueuse/core';
import { message } from 'ant-design-vue';
import { IconifyIcon } from '@vben/icons';
import { useTaskStore } from '#/store';
import { storeToRefs } from 'pinia';
import { useVbenModal } from '@vben/common-ui';
import StandardFormModal from '#/components/modal/form-modal.vue';
import { useApiHandler } from '#/composables/common/use-api-handler';
import BizSelect from '#/components/select/biz.vue';
import SceneSelect from '#/components/select/scene.vue';
import { formatTimestampToDate } from '#/utils/time';
import { useRoute } from 'vue-router';

interface DownloadItem {
  id: string;
  fileid: string;
  extra_id: string;
  scene: string;
  fname: string;
  status: number;
  pc_ok: boolean;
  create_time: string;
  biz: string;
  is_read: number;
  fsize: number;
}

const taskStore = useTaskStore()
const { biz } = storeToRefs(taskStore)
const route = useRoute()

/**
 * 获取业务标识，优先从store获取，若为空则尝试从路由query获取
 */
const currentBiz = computed(() => {
  // 当store中的biz为空时，尝试从route.query获取
  return biz.value || (route.query.biz as string) || '';
})

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  }
})
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
}>()

// API 处理
const { handleApiCall } = useApiHandler();

// 响应式状态
const drawerVisible = ref(props.visible);
const downloadList = ref<DownloadItem[]>([]);
const downListLoading = ref(false);
const { copy } = useClipboard();
// 定时器引用
const timer = ref<number | null>(null);

// 表单弹框配置
const formConfig = {
  commonConfig: {
    labelWidth: 100,
  },
  schema: [
    {
      component: shallowRef(BizSelect),
      componentProps: {
        placeholder: '请选择业务',
        isAI: false,
        class: 'w-full',
        disabled: true,
        // modelPropName: 'value',
      },
      fieldName: 'biz',
      label: '业务',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入文件ID',
        class: 'w-full',
      },
      fieldName: 'fileid',
      label: '文件ID',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入附件ID',
        class: 'w-full',
      },
      fieldName: 'extra_id',
      label: '附件ID',
    },
    {
      component: shallowRef(SceneSelect),
      componentProps: {
        class: 'w-full',
        placeholder: '请选择场景',
      },
      dependencies: {
        componentProps(values: any) {
          return {
            biz: values.biz,
          };
        },
        show(values: any) {
          return !!values.biz;
        },
        triggerFields: ['biz'],
      },
      fieldName: 'scene',
      label: '场景',
    },
  ],
  showDefaultActions: false,
};

// 定义弹窗配置
const modalConfig = {
  fullscreenButton: false,
  title: '自定义下载',
  zIndex: 1050,
};

// 使用 form-modal 组件
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: StandardFormModal,
  confirmText: '下载',
});

// 计算属性
watch(() => props.visible, (val) => {
  drawerVisible.value = val;
});

const tableRowClassName = (record: DownloadItem) => {
  if (record.status === 3) return 'bg-red-100';
  if (record.is_read === 1) return 'opacity-75';
  return '';
};

const handleCopy = async (text: string) => {
  try {
    await copy(text);
    message.success('复制成功');
  } catch {
    message.error('复制失败');
  }
};


const fileClickRow = (row: DownloadItem) => {
  if (row.status === 3) {
    message.error('文件异常，请重新下载！');
    return;
  }
  if ([0, 1].includes(row.status)) {
    message.warn('下载中，请稍后~');
    return;
  }
  if (!row.pc_ok) {
    message.warn('主机繁忙，请稍后~');
    return;
  }
  let fileTipInfo = "";
  if (row.scene) {
    fileTipInfo = `[${row.fileid}@${row.extra_id}@${row.scene}] ${row.fname}`;
  } else {
    fileTipInfo = `[${row.fileid}] ${row.fname}`;
  }
  window.open(`#/cloud-view?task_id=${row.id}&biz=${row.biz}&fsize=${row.fsize}&fileTipInfo=${fileTipInfo}`, '_blank');
};

// API 相关
const fetchDownloadList = async () => {
  downListLoading.value = true;
  try {
    const res = await getRemoteTasks({
      page: 0,
      limit: 100,
      biz: currentBiz.value
    });
    downloadList.value = res.data?.tasks || [];
    // const unreadCount = downloadList.value.filter(item => !item.is_read).length;
    // emit('update:noReadNum', unreadCount);
  } finally {
    downListLoading.value = false;
  }
};

// 打开自定义下载弹窗
const openCustomDownload = () => {
  formModalApi.setData({
    values: {
      biz: currentBiz.value,
      fileid: '',
      extra_id: '',
      scene: '',
    },
  });
  formModalApi.open();
};

// 确认提交表单
const onConfirm = async (values: Record<string, any>) => {
  await handleApiCall(
    applyRemoteTask,
    {
      fileid: values.fileid,
      extra_id: values.extra_id,
      scene: values.scene,
      biz: values.biz || currentBiz.value,
    },
    {
      loadingKey: 'download',
      onSuccess: () => {
        message.success('下载任务已提交');
        formModalApi.close();
        fetchDownloadList();
      },
      onError: () => {
        message.warning('下载任务失败');
      },
    }
  );
};

const throttledFetch = useThrottleFn(fetchDownloadList, 2000);

// 生命周期
onMounted(() => {
  // 组件挂载时不再调用fetchDownloadList和设置定时器
});

// 方法
/**
 * 关闭抽屉并清除定时器
 */
const handleClose = () => {
  drawerVisible.value = false;
  emit('update:visible', false);
  // 清除定时器
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
};

/**
 * 打开抽屉，获取下载列表并设置定时刷新
 */
const open = () => {
  drawerVisible.value = true;
  emit('update:visible', true);
  
  // 获取下载列表
  fetchDownloadList();
  
  // 设置定时刷新
  timer.value = setInterval(fetchDownloadList, 30000) as unknown as number;
}

// 组件卸载时清除定时器
onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
});

defineExpose({
  open,
})
</script>
<template>
  <div class="download-drawer">
    <form-modal
      :form-config="formConfig"
      :modal-config="modalConfig"
      @onConfirm="onConfirm"
    />
    <a-drawer v-model:open="drawerVisible" placement="right" width="50%" @close="handleClose">
      <template #title>
        <div class="flex justify-start items-center gap-2">
          <span>文件下载列表({{ currentBiz }})</span>
          <iconify-icon 
            class="cursor-pointer" 
            :class="{ 'animate-spin': downListLoading }" 
            @click="throttledFetch" 
            title="刷新列表" 
            icon="ant-design:sync-outlined" />
          <iconify-icon class="cursor-pointer" @click="openCustomDownload" title="自定义下载" icon="ant-design:plus-outlined" />
        </div>
      </template>
      <a-table class="h-full" :dataSource="downloadList" :rowClassName="tableRowClassName" size="small" :loading="downListLoading" :scroll="{ x: 900 }">
        <a-table-column key="status" title="状态" width="120px" align="center">
          <template #default="{ record }">
            <div class="flex items-center justify-center">
              <template v-if="record.status === 0 || record.status === 1">
                <a-tooltip title="下载中">
                  <iconify-icon icon="ant-design:loading-outlined" class="animate-spin mr-1 text-blue-500" />
                </a-tooltip>
              </template>
              <template v-else-if="record.status === 2">
                <a-tooltip :title="record.pc_ok ? '准备就绪' : '主机繁忙，请稍后'">
                  <iconify-icon v-if="record.pc_ok" icon="ant-design:check-circle-outlined" class="text-green-500 mr-1" />
                  <iconify-icon v-else icon="ant-design:loading-outlined" class="animate-spin mr-1 text-blue-500" />
                </a-tooltip>
              </template>
              <template v-else-if="record.status === 3">
                <a-tooltip title="下载失败，请重新下载">
                  <iconify-icon icon="ant-design:close-circle-outlined" class="mr-1 text-red-500" />
                </a-tooltip>
              </template>
              <span :class="['cursor-pointer', { 'opacity-50': record.status !== 2 }]"
                @click="handleCopy(record.fileid)">
                {{ record.fileid }}
              </span>
            </div>
          </template>
        </a-table-column>

        <a-table-column key="extra_id" title="附件ID" width="60px" align="center" dataIndex="extra_id" />
        <a-table-column key="scene" title="场景" width="60px" align="center" dataIndex="scene" />

        <a-table-column key="fname" title="文件名" width="200px" align="center">
          <template #default="{ record }">
            <div class="flex items-center justify-center">
              <span :class="['text-blue-500 truncate max-w-[200px]',
                { 'cursor-pointer': record.status === 2, 'cursor-not-allowed': [0, 1].includes(record.status), 'opacity-50': record.status !== 2 }]"
                @click="fileClickRow(record)">
                {{ record.fname }}
              </span>
              <a-button type="link" :disabled="record.status !== 2" @click="fileClickRow(record)">
                <template #icon>
                  <iconify-icon class="mt-1 ml-1" icon="ant-design:cloud-download-outlined"/>
                </template>
              </a-button>
            </div>
          </template>
        </a-table-column>

        <a-table-column key="create_time" title="创建时间" width="120px" align="center" fixed="right">
          <template #default="{ record }">
            <span :class="{ 'opacity-50': record.status !== 2 }">
              {{ formatTimestampToDate(record.create_time) }}
            </span> 
          </template>
        </a-table-column>
      </a-table>
    </a-drawer>
  </div>
</template>
