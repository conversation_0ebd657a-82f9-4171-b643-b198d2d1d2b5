<script lang="ts" setup>
import { message } from 'ant-design-vue';

import TableTemplate from '#/components/table/index.vue';

import { reactive, ref } from 'vue';
import type { VxeGridProps } from '#/adapter/vxe-table';

import Chart from '#/components/chart/index.vue';
import { historyScore } from '#/api/exam.js';
import statusTag from '../components/StatusTag.vue';

const searchOptions = {
  collapsed: false,
  schemas: {
    '1': [
      {
        component: 'Input',
        componentProps: {
          placeholder: '请输入',
          // style: 'width:200px',
        },
        fieldName: 'examineeId',
        label: '账号：',
        labelClass: 'font-semibold w-auto',
      },
    ],
  },
  showCollapseButton: false,
  submitOnChange: false,
  wrapperClass: 'grid grid-cols-[1fr_3fr] ',
  actionWrapperClass: 'col-start-2 text-left',
};

const columns: VxeGridProps['columns'] = [
  {
    field: 'title',
    title: '考试',
    align: 'center',
  },
  {
    field: 'score',
    title: '分数',
    align: 'center',
    slots: { default: 'score' },
  },
  {
    field: 'full_mark',
    title: '满分',
    align: 'center',
  },
  {
    field: 'rank',
    title: '排名',
    align: 'center',
  },
  {
    field: 'duration',
    title: '考试总时长',
    align: 'center',
    slots: {
      default: ({ row }) => {
        return `${row.duration / 60}分钟`;
      },
    },
  },
  {
    field: 'time_used',
    title: '考试用时',
    align: 'center',
    slots: {
      default: ({ row }) => {
        return `${((row.end_time - row.start_time) / 60).toFixed(1)}分钟`;
      },
    },
  },
];
const paginationOptions = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1,
});
const titleOptions = {
  help: '',
  title: '',
};

const dataList = ref([]);

const xData = ref<any[]>([]);
const yData = ref<any[]>([]);
const chartDataLine = reactive<any>({
  legend: ['历史得分率'],
  xAxis: [],
  yAxis: [{ name: '历史得分率' }],
  series: [
    {
      name: '历史得分率',
      type: 'line',
      data: [],
    },
  ],
});

async function queryHistoryScore({
  page,
  pageSize,
  ...formValues
}: {
  page: number;
  pageSize: number;
  [key: string]: any;
}) {
  // 构建基础参数
  const baseParams: any = {
    page,
    limit: pageSize,
    ...formValues,
  };
  if (!baseParams.examineeId) {
    dataList.value = [];
    return { items: [], total: 0 };
  }
  const res = await historyScore({
    examinee_name: baseParams.examineeId,
    limit: baseParams.limit,
    page: baseParams.page - 1,
  });
  if (res && res.result) {
    dataList.value = res.data.records;

    updateChartData();
    return {
      items: res.data.records,
      total: res.data.count,
    };
  } else {
    message.error(res.message);
    dataList.value = [];
    updateChartData();
    return { items: [], total: 0 };
  }
}
function updateChartData() {
  xData.value = dataList.value.slice(0, 30).map((item: any) => item.title);
  yData.value = dataList.value.slice(0, 30).map((item: any) => {
    const rate = (item.score / item.full_mark) * 100;
    return rate.toFixed(2);
  });
  chartDataLine.xAxis = xData.value;
  chartDataLine.series[0].data = yData.value;
}
const toolbarOptions = {
  export: true,
};
</script>
<template>
  <div class="history-container">
    <table-template
      :columns="columns"
      :title-options="titleOptions"
      :pagination-options="paginationOptions"
      :query-method="queryHistoryScore"
      :search-options="searchOptions"
      :toolbar-options="toolbarOptions"
    >
      <!-- Chart 放置在工具栏左侧区域，这样会在搜索框和表格之间 -->
      <template #toolbar-left>
        <div class="w-full mb-4">
          <Chart
            :data="chartDataLine"
            height="400px"
            title="得分率"
            type="line"
            class="bg-card p-4 rounded shadow-sm"
          />
        </div>
      </template>
      <template #score="{ row }">
        <span>{{ row.score }}</span>
        <status-tag
          situation="history"
          :correct-status="row.correct_status"
        ></status-tag>
      </template>
    </table-template>
  </div>
</template>
