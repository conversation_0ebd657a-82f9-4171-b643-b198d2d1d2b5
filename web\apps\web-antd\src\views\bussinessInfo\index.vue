<script lang="ts" setup>
import { ref, computed } from "vue";
import StandardForm from "#/components/form/index.vue";
import { mapToOptions } from "#/utils/transform";
import { ENTRANCE_MAP, TYPE_MAP } from "#/constants/maps/bussiness-info";
import { ID_TYPE_MAP } from "#/constants/maps/operate-log";
import {
  getRecordBizData,
  avatarReset,
  nicknameReset,
  homeuserReset,
} from "#/api/forbidinfo";
import ImageView from "#/components/image/index.vue";
import UpdateForbidCommentBtn from "#/components/button/UpdateForbidComment.vue";
import { convertFieldsToNumbers } from "#/utils/transform";
import ForbidSelect from "#/components/select/ForbidType.vue";
import { useApiHandler } from "#/composables/common/use-api-handler";

const { handleApiCall } = useApiHandler();
const tabOptions = [
  { key: "wps_account", tab: "wps账号" },
  { key: "home_topic", tab: "家园帖子" },
];
const activeTab = ref<"wps_account" | "home_topic">("wps_account");

// 只有 wps_account 时显示“查询”按钮
const isWpsAccount = computed(() => activeTab.value === "wps_account");

// --- 表单公共配置 ---
const layout = "horizontal";
const formContainerClass = "flex flex-wrap";
const wrapperClass = "grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-5";
const commonConfig = {
  colon: true,
  componentProps: { class: "w-full" },
  labelWidth: 80,
};

// --- 动态 Schema ---
const formSchema = computed(
  () =>
    ({
      wps_account: [
        {
          component: "Input",
          fieldName: "userid",
          label: "userid",
        },
        {
          component: "Select",
          fieldName: "entry",
          label: "入口选择",
          componentProps: { options: mapToOptions(ENTRANCE_MAP) },
        },
      ],
      home_topic: [
        {
          component: "Input",
          fieldName: "user_id",
          label: "userid",
        },
        {
          component: "Select",
          fieldName: "user_id_type",
          label: "账号类别",
          componentProps: { options: mapToOptions(ID_TYPE_MAP) },
        },
        {
          component: "Select",
          fieldName: "scope",
          defaultValue: "all",
          label: "类型",
          componentProps: { options: mapToOptions(TYPE_MAP) },
        },
      ],
    }[activeTab.value])
);

// --- 查询结果 ---
const avatarUrl = ref<string>("");
const nickname = ref<string>("");
const currentParams = ref({});
const formRef = ref();
const forbidStatus = ref();

// --- 处理提交 ---
async function handleSearch(values: Record<string, any>) {
  currentParams.value = {
    ...values,
    biz: activeTab.value,
  };
  const res = await getRecordBizData(currentParams.value);
  if (res.result) {
    avatarUrl.value = res.data.avatar_url || "";
    nickname.value = res.data.nickname || "";
  }
}
const updateItem = async (item: any) => {
  let isAvatar = currentParams.value.entry === "avatar_check";
  const baseParams = {
    status: "forbid",
    type: item,
    ...currentParams.value,
    ...(isAvatar ? { avatar_url: avatarUrl.value } : { nickname: nickname.value }),
  };

  const params = convertFieldsToNumbers(baseParams, ["userid"]);
  let api = isAvatar ? avatarReset : nicknameReset;
  await handleApiCall(api, params);
};
const handleAction = async (item: any) => {
  const values = await formRef.value.formApi.getValues();
  let baseParams = {
    scope: "all",
    type: item[1],
    ...values,
  };
  const params = convertFieldsToNumbers(baseParams, ["user_id"]);
  await handleApiCall(homeuserReset, params);
};
</script>

<template>
  <div>
    <!-- 标签页 -->
    <a-tabs v-model:activeKey="activeTab" class="h-[40px] px-5">
      <a-tab-pane v-for="tab in tabOptions" :key="tab.key" :tab="tab.tab" />
    </a-tabs>
    <standard-form
      ref="formRef"
      :key="activeTab"
      :schema="formSchema"
      :layout="layout"
      :form-container-class="formContainerClass"
      :wrapper-class="wrapperClass"
      :common-config="commonConfig"
      :showDefaultActions="isWpsAccount"
      @submit="handleSearch"
      class="my-4 p-5"
      action-wrapper-class="text-left col-auto"
    >
      <template #right v-if="!isWpsAccount">
        <forbid-select
          v-model="forbidStatus"
          :isGLobalList="true"
          :pass="true"
          :filterType="[3, 5]"
          :isCascader="true"
          class="ml-5 w-[120px]"
          :isClear="true"
          :tabPlatform="activeTab.value"
          @change="handleAction"
        />
      </template>
    </standard-form>
    <a-card v-if="avatarUrl || nickname">
      <div class="mt-4 flex items-center space-x-4">
        <image-view v-if="avatarUrl" :src="avatarUrl" width="100" height="100" preview />
        <span v-else class="text-lg font-medium">{{ nickname }}</span>
        <update-forbid-comment-btn :callback="updateItem" scope='{status:"pass"}' />
      </div>
    </a-card>
  </div>
</template>
