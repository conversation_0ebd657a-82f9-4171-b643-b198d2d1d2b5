<script setup lang="ts">
import { computed } from 'vue';

import { aiForbidUserPull } from '#/api/ai-mark';

import MarkTemplate from './mark-template.vue';
import TextCard from './text-card.vue';

withDefaults(
  defineProps<{
    enableKeywords?: boolean;
    showLevel?: boolean;
  }>(),
  {
    enableKeywords: true,
    showLevel: false,
  },
);
const entry = defineModel<string>('entry', { required: true });
const api = computed(() => {
  return entry.value.includes('ai_forbid_user') ? aiForbidUserPull : undefined;
});
</script>

<template>
  <mark-template
    v-model:entry="entry"
    :api="api"
    :enable-keywords="enableKeywords"
    auto-commit
    :show-level="showLevel"
  >
    <template #default="slotProps">
      <text-card
        :content="slotProps.content"
        :is-loading="slotProps.isLoading"
        :msg-id="slotProps.msgId"
        :scene="slotProps.scene"
        :show-translation="slotProps.showTranslation"
        :translated-content="slotProps.translatedContent"
        @toggle-translation="slotProps.toggleTranslation"
      />
    </template>
  </mark-template>
</template>
