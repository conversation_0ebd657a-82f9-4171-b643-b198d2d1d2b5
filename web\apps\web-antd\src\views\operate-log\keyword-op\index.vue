<script setup lang="ts">
import type { VxeGridProps } from "#/adapter/vxe-table";
import { ref, shallowRef, onMounted } from "vue";
import { getLastNDaysRange } from "#/utils/time";
import TableTemplate from "#/components/table/index.vue";
import CategorySelect from "#/components/select/category.vue";
import LibrarySelect from "#/components/select/library.vue";
import { getKeywordsWordConfigLogs } from "#/api/keywords";
import { mapToOptions } from "#/utils/transform";
import { OPERATE_TYPE_MAP } from "#/constants/maps/operate-log";
import { KEYWORD_KIND_MAP } from "#/constants/maps/keywords";
import { formatTimestampToDate, transformTimeFields } from "#/utils/time";
import { useKeywordCategory } from "#/composables/keyword/use-category";
import { EMPTY_OPTION } from "#/constants/options";
import { $t } from "#/locales";

const {
  categoryTreeData,
  fetchData: loadCategoryData,
  getKeywordLabelString,
} = useKeywordCategory();
import { COMPANY_TYPE_MAP,PAID_STATUS_MAP,VIDEO_STREAM_TASK_TYPE_MAP,VIDEO_STREAM_STATUS_MAP,VIDEO_STREAM_TASK_STATUS_MAP } from '#/constants/maps';

const searchOptions = {
  collapsed: false,
  schemas: {
    "1": [
      {
        component: shallowRef(LibrarySelect),
        fieldName: "base_id",
        defaultValue: 100000, // 默认云文档词库
        label: "词库",
        componentProps: {
          placeholder: "请选择",
          modelPropName: "value",
          immediate: true,
        },
      },
      {
        component: "Select",
        fieldName: "kind",
        label: "级别",
        defaultValue: "",
        componentProps: {
          placeholder: "请选择",
          options: [...EMPTY_OPTION, ...mapToOptions(KEYWORD_KIND_MAP)],
        },
      },
      {
        component: shallowRef(CategorySelect),
        fieldName: "category",
        label: "类别",
        componentProps: {
          placeholder: "请选择",
          treeData: categoryTreeData,
        },
      },
      {
        component: "Input",
        fieldName: "username",
        label: "操作账号",
        componentProps: {
          placeholder: "请输入",
        },
      },
      {
        component: "Select",
        fieldName: "word",
        label: "关键词",
        help: $t("page.keywords.task.keyword_search_help"),
        componentProps: {
          mode: "tags",
          maxTagCount: "responsive",
        },
      },
      {
        component: "Select",
        fieldName: "operate",
        label: "操作类型",
        defaultValue: "",
        componentProps: {
          placeholder: "请选择",
          options: [...EMPTY_OPTION, ...mapToOptions(OPERATE_TYPE_MAP)],
        },
      },
      {
        component: "RangePicker",
        fieldName: "dateRange",
        rules: "required",
        label: "日期范围",
        defaultValue: getLastNDaysRange(14, 0, "X"),
        class: "col-span-2",
        componentProps: {
          // format: "YYYY-MM-DD HH:mm:ss",
          valueFormat: "X",
        },
      },
    ],
  },
  showCollapseButton: true,
  submitOnChange: false,
  wrapperClass: "grid-cols-6",
};

const exportOptions = {
  filename: "表格数据",
};

const columns: VxeGridProps["columns"] = [
  {
    type: "seq",
    align: "center",
    title: "序号",
    fixed: "left",
    width: 100,
  },
  {
    title: "操作详情",
    children: [
      {
        field: "word",
        title: "关键词",
      },
      {
        field: "kind",
        title: "级别",
        formatter: ({ cellValue }) => {
          return KEYWORD_KIND_MAP[cellValue] || "";
        },
      },
      {
        field: "class",
        title: "类别",
        formatter: ({ row }) => getKeywordLabelString(row.level, row.category, row.class),
      },
      {
        field: "effect_time",
        title: "生效时间",
        formatter: ({ cellValue }) => formatTimestampToDate(cellValue),
      },
    ],
  },
  {
    field: "operate",
    title: "操作类型",
    formatter: ({ cellValue }) => {
      return OPERATE_TYPE_MAP[cellValue] || "";
    },
  },
  {
    title: "原始详情",
    children: [
      {
        field: "origin_word",
        title: "关键词",
      },
      {
        field: "origin_category",
        title: "级别",
      },
      {
        field: "origin_class",
        title: "类别",
        formatter: ({ row }) =>
          getKeywordLabelString(row.origin_level, row.origin_category, row.origin_class),
      },
      {
        field: "origin_effect_time",
        title: "生效时间",
        formatter: ({ cellValue }) => formatTimestampToDate(cellValue),
      },
    ],
  },
  {
    field: "username",
    title: "操作账号",
  },
  {
    field: "modify_time",
    title: "操作时间",
    width: 180,
    formatter: ({ cellValue }) => formatTimestampToDate(cellValue),
  },
  {
    field: "remark",
    title: "备注",
  },
];

const getKeywordOpLogs = async ({
  page,
  pageSize,
  ...formValues
}: {
  page: number;
  pageSize: number;
  [key: string]: any;
}) => {
  try {
    // 构建基础参数
    const baseParams = {
      page: page - 1,
      limit: pageSize,
      ...transformTimeFields(formValues, [["dateRange", ["start_time", "end_time"]]]),
    };

    // 使用validateParams处理参数

    let res = await getKeywordsWordConfigLogs(baseParams);

    const records = res.data.records;
    return {
      items: records,
      total: res.data.count,
    };
  } catch (error) {
    return {
      items: [],
      total: 0,
    };
  }
};

const paginationOptions = {
  pageSize: 20,
  currentPage: 1,
};
onMounted(() => {
  loadCategoryData(); // 类别
});
</script>

<template>
  <table-template
    :columns="columns"
    :export-options="exportOptions"
    :pagination-options="paginationOptions"
    :query-method="getKeywordOpLogs"
    :search-options="searchOptions"
  >
  </table-template>
</template>
