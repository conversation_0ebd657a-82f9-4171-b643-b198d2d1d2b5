<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import TableTemplate from "#/components/table/index.vue";
import { useRouter } from "vue-router";
import ActionDropdown from "../components/ActionDropdown.vue";
import { Cascader } from "ant-design-vue";
import { formatTimestampToDate, getLastNDaysRange } from "#/utils/time";
import { success } from "#/utils/toast";
import dayjs from "dayjs";
import type { VxeGridProps } from "#/adapter/vxe-table";
import { z } from "#/adapter/form";
import { useForbidList } from "./violation";
import FormModalComponent from "#/components/modal/form-modal.vue";
import { useVbenModal } from "@vben/common-ui";
import { updateStatus } from "#/api/black-list";
import { findLabelByValue } from "#/utils/data";
import { storeToRefs } from "pinia";
import useForbidStore from "#/store/forbid";
const forbidStore = useForbidStore();
const { allForbidTags } = storeToRefs(forbidStore);

const props = withDefaults(
  defineProps<{
    tabPlatform: string;
    queryMethod: (params: any) => Promise<any>;
    // platName: string;
  }>(),
  {
    tabPlatform: "",
    // platName: '',
    queryMethod: () => Promise.resolve({}),
  }
);
const router = useRouter();
const docTable = ref<any>(null);

const currentRow = ref<Record<string, any>>({});

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: FormModalComponent,
});
const { forbidList, loadData } = useForbidList();
onMounted(() => {
  loadData();
});
const paginationOptions = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1,
  layouts: ["PrevPage", "NextPage"],
});

const listQuery = ref<Record<string, any>>({
  // 搜索条件
  illegal_section: "",
  userid: null,
  type: null,
  status: "",
  start_time: getLastNDaysRange(14, 0, "x")[0], // 接口的定义这里的start_time 要比 end_time 大
  end_time: getLastNDaysRange(14, 0, "x")[1],
});

const illegalOptions = [
  {
    label: "全部",
    value: "",
  },
  {
    label: "10次以下",
    value: "1",
  },
  {
    label: "10次~50次",
    value: "2",
  },
  {
    label: "50次以上",
    value: "3",
  },
];
const searchOptions = {
  collapsed: false,
  schemas: {
    "1": [
      {
        component: "Input",
        componentProps: {
          placeholder: "userid",
          clearable: true,
          class: "w-50",
        },
        fieldName: "userid",
        label: "UserID:",
        rules: z
          .string()
          .regex(/^\d*$/, { message: "UserID格式不对，请重新输入" }) // 允许空字符串
          .optional(),
      },
      {
        component: "Select",
        componentProps: {
          options: illegalOptions,
        },
        fieldName: "illegal_section",
        label: "违规次数:",
      },
      {
        component: "Cascader",
        componentProps: {
          options: forbidList,
          showCheckedStrategy: Cascader.SHOW_CHILD,
          showSearch: true,
          clearable: true,
          expandTrigger: "hover",
          fieldNames: {
            label: "name",
            value: "type",
            children: "children",
          },
        },
        fieldName: "type",
        label: "最新违规类型:",
      },
      {
        component: "DatePicker",
        componentProps: {
          showTime: { format: "HH:mm:ss" },
          format: "YYYY-MM-DD HH:mm:ss",
          valueFormat: "x", // 使用时间戳格式
          placeholder: "开始时间",
          disabledDate: disabledStartDate,
        },
        defaultValue: listQuery.value.start_time.toString(),
        fieldName: "start_time",
        label: "11最新违规时间:",
      },
      {
        component: "DatePicker",
        componentProps: {
          showTime: { format: "HH:mm:ss" },
          format: "YYYY-MM-DD HH:mm:ss",
          valueFormat: "x", // 使用时间戳格式
          placeholder: "结束时间",
          disabledDate: disabledEndDate,
        },
        defaultValue: listQuery.value.end_time.toString(),
        fieldName: "end_time",
        label: "至",
      },
    ],
  },
  showCollapseButton: false,
  submitOnChange: false,
  wrapperClass: "grid grid-cols-5 ",
  handleValuesChange: handleValuesChange,
};
const forbidType = [
  { label: "开启", value: "ok" },
  { label: "封禁", value: "forbid" },
];
const columns = ref<VxeGridProps["columns"]>([
  {
    type: "seq",
    title: "序号",
    width: 80,
    align: "center",
  },
  {
    field: "userid",
    title: "userID",
    align: "center",
    slots: { default: "userid" },
  },
  {
    field: "count",
    title: "违规次数",
    align: "center",
    slots: { default: "count" },
  },
  {
    field: "type",
    title: "最新违规类型",
    align: "center",
    formatter: ({ cellValue }) =>
      findLabelByValue(allForbidTags.value, cellValue, "type", "name"),
  },
  {
    field: "forbid_time",
    title: "最新违规时间",
    align: "center",
    formatter: ({ cellValue }) => formatTimestampToDate(cellValue),
  },
  {
    field: "status",
    title: "状态",
    visible: false,
    formatter: ({ cellValue }) => {
      const item = forbidType.find((item) => item.value === cellValue);
      return item ? item.label : cellValue;
    },
  },
  {
    field: "pv_num",
    title: "违规记录",
    align: "center",
    slots: { default: "forbidRecord" },
  },
  {
    title: "操作",
    align: "center",
    slots: { default: "action" },
  },
]);
const toolbarOptions = {
  export: true,
};
const formConfig = {
  commonConfig: {
    labelWidth: 100,
    wrapperClass: "pr-6",
  },
  schema: computed(() => {
    const schema = [
      {
        component: "Input",
        componentProps: {
          disabled: true,
        },
        defaultValue: currentRow.value.userid,
        fieldName: "userid",
        label: "用户ID",
      },
      {
        component: "Input",
        defaultValue: "",
        fieldName: "reason",
        label: "原因",
        dependencies: {
          triggerFields: ["reason", "ctype"],
          trigger(values: any) {
            console.log("values", values);
            if (!values.reason || (currentRow.value.status == "ok" && !values.ctype))
              formModalApi.setState({ confirmDisabled: true });
            else formModalApi.setState({ confirmDisabled: false });
          },
        },
      },
      {
        component: "Cascader",
        componentProps: {
          options: forbidList,
          showCheckedStrategy: Cascader.SHOW_CHILD,
          showSearch: true,
          clearable: true,
          expandTrigger: "hover",
          fieldNames: {
            label: "name",
            value: "type",
            children: "children",
          },
          class: "w-full",
        },
        fieldName: "ctype",
        label: "最新违规类型",
        dependencies: {
          // 根据 currentRow.value.status 动态控制显示/隐藏
          show() {
            return currentRow.value.status === "ok";
          },
          // 仅当 status 变化时触发
          triggerFields: ["status"],
        },
      },
    ];
    return schema;
  }),
};
const modalConfig = {
  fullscreenButton: false,
};

async function handleConfirm(values: any) {
  console.log("confirm", values);
  let { userid, status } = currentRow.value;
  status = status === "ok" ? "forbid" : "ok";
  let data: Record<string, any> = {
    userid,
    reason: values.reason,
    status,
  };
  if (currentRow.value.status === "ok") {
    const len = values.ctype?.length;
    data.ctype = values.ctype[len - 1];
  }
  const res = await updateStatus(data);
  if (res && res.result) {
    success("操作成功");
    docTable.value.refreshTable();
  }
  formModalApi.close();
}

function handleValuesChange(values: any) {
  console.log("values", values);

  listQuery.value.start_time = values.start_time;
  listQuery.value.end_time = values.end_time;
}

function disabledStartDate(time: dayjs.Dayjs) {
  const { end_time } = listQuery.value;
  const currentTime = time.valueOf();
  if (end_time) {
    return currentTime > end_time;
  }
  return currentTime > Date.now();
}
function disabledEndDate(time: dayjs.Dayjs) {
  const { start_time } = listQuery.value;
  const currentTime = time.valueOf();
  if (start_time) {
    return currentTime <= start_time || currentTime > Date.now();
  }
}

function toDetail(id: number) {
  router.push({
    path: "/users/grey/illegal_detail",
    query: { id: id, from: props.tabPlatform },
  });
}

function handleCommand(command: number, rowData: any) {
  console.log("rowdata", rowData);
  currentRow.value = rowData;
  formModalApi.setState({
    title: `账号操作-${currentRow.value.status === "ok" ? "封禁用户" : "解封用户"}`,
  });
  formModalApi.open();
}
</script>
<template>
  <div>
    <table-template
      ref="docTable"
      :search-options="searchOptions"
      :columns="columns"
      :pagination-options="paginationOptions"
      :query-method="props.queryMethod"
      :toolbar-options="toolbarOptions"
    >
      <template #userid="{ row }">
        <span :class="{ 'text-gray-400': row.pvless === 2 }">{{ row.userid }}</span>
      </template>
      <template #count="{ row }">
        <span class="text-[#606266]">
          {{ row.count }}
        </span>
      </template>
      <template #forbidRecord="{ row }">
        <a-button type="link" @click="toDetail(row.userid)">查看详情</a-button>
      </template>
      <template #action="{ row }">
        <action-dropdown :row-data="row" @command="handleCommand"></action-dropdown>
      </template>
    </table-template>

    <form-modal
      :form-config="formConfig"
      :modal-config="modalConfig"
      @on-confirm="handleConfirm"
    >
    </form-modal>
  </div>
</template>
