import { requestClient } from "./request";
import { ApiCommonResponse} from "#/types/api";
// 获取文件预览NEW
export function createFileView(query: GetFileViewRequest) :Promise<ApiCommonResponse<FileViewData>>{
  return requestClient.get('/manual/view/create_view',{
    data: query
  })
}

export function createOcrView(query:GetFileViewRequest) {
  return requestClient.get('/manual/view/create_ocr_view',{
    data: query
  })
}
// 获取文件预览NEW
export function getExpireUrl(query: {key:string,url_bucket:string}):Promise<ApiCommonResponse<string>> {
  return requestClient.get('/manual/view/expire_url',{
    data: query
  }).then(res=>ApiCommonResponse.create(res))
}

// 获取文件数据
export function getUrlData(url:string,mode:string = 'json'):Promise<any> {
  let newUrl = url
  if (!url.includes('https')){
    newUrl = url.replace('http', 'https')
  }
  return fetch(newUrl).then(res=>{
    if (mode === 'blob'){
      return res.blob()
    }else if (mode === 'json'){
      return res.json()
    }else {
      return res.text()
    }
  })
}

// 批量获取图 
export function getExpireUrlBatch(data:{key:string,bucket:string}[]):Promise<ApiCommonResponse<any>> {
  return requestClient.post('/manual/view/expire_url/batch',{
    items: data
  }).then(res=>ApiCommonResponse.create(res))
}

// 视频
export function videoDetail(query: {
  fileinx: string;
  from?: string;
  focus?: boolean;
  force?: boolean;
  id_new?: boolean;
  extra?: string;
  scene?: string;
  nowtime?: number;
}):Promise<VideoDetailResponse> {
  return requestClient.get('/manual/view/video_detail',{
    data: query
  })
}

// 获取webview
export function getWebview(query: GetFileViewRequest):Promise<any> {
  return requestClient.get('/manual/preview/create_url',{
    data: query
  })
}

// 稻壳预览
export function getThirdView(query: GetFileViewRequest) {
  return requestClient.get('/manual/view/create_third',{
    data: query
  })
}

// 无痕预览链接转化
export function linkToPrivate(data:any) {
  return requestClient.post('/manual/preview/in_private',{
    data
  })
}

export function mediaFormat():Promise<MediaFormatResponse> {
  return requestClient.get('/manual/view/media_format')
}


// 音频
export function audioDetail(query: {
  fileinx: string;
  from?: string;
  focus?: boolean;
  force?: boolean;
  id_new?: boolean;
  scene?: string;
  nowtime?: number;
}):Promise<AudioDetailResponse> {
  return requestClient.get('/manual/view/audio_detail',{
    data: query
  })
}
