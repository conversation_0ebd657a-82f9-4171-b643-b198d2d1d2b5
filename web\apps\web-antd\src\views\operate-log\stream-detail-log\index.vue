<script setup lang="ts">
import type { VxeGridProps } from '#/adapter/vxe-table';
import TableTemplate from '#/components/table/index.vue';
import { ref , computed} from 'vue';
import { useRoute } from "vue-router";
import ImageViewer from '#/components/image/index.vue';
import { mapToOptions } from '#/utils/transform';
import { VIDEO_STREAM_TASK_TYPE_MAP,VIDEO_STREAM_STATUS_MAP } from '#/constants/maps';
import { getLastNDaysRange,getTodayTimeRange,formatTimestampToDate } from "#/utils/time";
import { listStreamDetailLog } from "#/api/operateLog";
import {NO_LIMIT_NUM} from "#/constants/num";

const route = useRoute();

const paginationOptions = ref({
  pageSize: 20,
  currentPage: 1,
        // 不显示 “共 N 条”
  layouts: ['PrevPage', 'NextPage', 'Sizes','Jump'], // 显示按钮组成
});
const parsedRow = computed(() => {
  const raw = route.query.row as string;
  
  try {
    return JSON.parse(decodeURIComponent(route.query.row as string));
  } catch {
    return {};
  }
});

const queryMethod = async ({
  page,
  pageSize,
  ...formValues
}: {
  page: number;
  pageSize: number;
  [key: string]: any;
}) => {
  const row = parsedRow.value;
  const QueryParams = {
    biz:row.biz,
    scene:row.scene,
    stream_id: row.stream_id,
    task_type: row.task_type,
    task_id: row.task_id,
    start_time: row.create_time,
    page: page - 1,
    limit: pageSize,
    ...formValues
  };
  const res = await listStreamDetailLog(QueryParams);
  const records = res.data.logs;

  return {
    items: records,
    total: res.data.count ?? NO_LIMIT_NUM
    
  };
};

const searchOptions = {
  collapsed: false,
  schemas: {
    '1': [
      // 流ID 企业ID 认证企业等级 认证类型 付费状态 
      // 创建者ID 任务类型 任务ID 状态 运行状态 开始时间 结束时间
      
      {
        component: "Select",
        fieldName: 'ai_label',
        label: '状态:',
        componentProps: {
          placeholder: '请选择',
          options:mapToOptions(VIDEO_STREAM_STATUS_MAP)
        },
      }
    ],
  },
  showCollapseButton: true,
  submitOnChange: false,
  wrapperClass: 'grid-cols-5',
};


const baseColumns1: VxeGridProps['columns'] = [
  {
    type: 'seq',
    // field: 'order_number',
    title: '序号',
    fixed: 'left',
    width: 100,
  },
  {
    field: 'ai_label',
    title: '状态',
    formatter: ({ cellValue,row }) => {
          return VIDEO_STREAM_STATUS_MAP[row.verify_result.ai_label] || "-";
        },
  },
  {
    field: 'stream_id',
    title: '流ID',
  },
  {
    field: 'task_type',
    title: '任务类型',
    formatter: ({ cellValue }) => {
          return VIDEO_STREAM_TASK_TYPE_MAP[cellValue] || "";
        },
  },
  {
    field: 'task_id',
    title: '任务ID',
  },
];

const baseColumns2: VxeGridProps['columns'] = [
  {
    field: 'err_msg',
    title: '违规原因',
    formatter: ({ cellValue,row }) => {
          return row.verify_result.hit_info;
        },
  },
  
  {
    field: 'nowtime',
    title: '操作时间',
    formatter: ({ cellValue }) => formatTimestampToDate(cellValue),
  },
];
const taskType = computed(() => parsedRow.value.task_type);

const getDynamicColumns = (): VxeGridProps['columns'] => {
  if (taskType.value === 'video') {
    return [
      ...baseColumns1,
      {
        field: 'image_urls',
        title: '图片',
        slots:{
          default:'cell_image_urls'
        }
      },
      ...baseColumns2,
    ];
  } else if (taskType.value === 'audio') {
    return [
      ...baseColumns1,
      {
        field: 'audio_urls',
        title: '音频片段',
        minWidth: 100,
        slots:{
          default:'cell_audio_urls'
        }
      },
      {
        field: 'asr_text',
        title: 'ASR文本',
        formatter: ({ cellValue }) => cellValue || '',
      },
      ...baseColumns2,
    ];
  }
  return [...baseColumns1, ...baseColumns2];
};

const columns = ref<VxeGridProps['columns']>(getDynamicColumns());



const toolbarOptions = {
  export: true,
};

const exportOptions = {
  filename: '表格数据', // 导出的文件名
  type: 'csv', // 导出格式
  isHeader: true, // 包含表头
  original: true, // 导出源数据
  useLabel: true, // 使用列标签作为字段名
  message: true, // 显示导出消息提示
  encoding: 'utf8', // 文件编码
};
// const playAudio = (url: string) => {
//   if (!url) return;
//   const audio = new Audio(url);
//   audio.play();
// };


</script>

<template>

  <table-template
    :columns="columns"
    :query-method="queryMethod"
    :pagination-options="paginationOptions"
    :toolbar-options="toolbarOptions"
    :export-options="exportOptions"
    :search-options="searchOptions"
  >
  <template #cell_image_urls="{ row }">
    <image-viewer v-if="row.image_urls && row.image_urls.length" :src="row.image_urls[0]" />
    <span v-else>-</span>
  </template>
  <template #cell_audio_urls="{ row }">
  <div style="width: 100%;">
    <audio v-if="row.audio_urls && row.audio_urls.length" 
    :src="row.audio_urls[0]" 
    controls
    style="width: 100%; max-width: 100%; height: 32px;"
    ></audio>
    <!-- <audio-player v-else></audio-player> -->
  </div>
</template>

</table-template>
  
</template>
