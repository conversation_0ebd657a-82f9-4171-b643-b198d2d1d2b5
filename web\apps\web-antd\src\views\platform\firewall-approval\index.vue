<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { SearchOutlined, CheckOutlined } from '@ant-design/icons-vue';
import { getApplyList, applyUpdate } from '#/api/platform';
import { formatTimestampToDate } from '#/utils/time';

interface AppItem {
  id: string;
  app: string;
  check?: 'pass' | 'reject' | '';
}

interface ApprovalItem {
  userid: string;
  uname: string;
  reason: string;
  modify_time: number;
  list: AppItem[];
}

interface ApplyRequest {
  pass_list: string[];
  reject_list: string[];
}

const route = useRoute();
const loading = ref(false);
const searchAccount = ref<string>('');
const approvalData = ref<ApprovalItem[]>([]);
const applyReq = ref<ApplyRequest[]>([]);
const selectedUserId = ref<string>('');

// 获取申请列表
async function fetchApplyList() {
  try {
    loading.value = true;
    const resp = await getApplyList(searchAccount.value);
    if (resp.result) {
      approvalData.value = resp.data.records.map((item: ApprovalItem) => ({
        ...item,
        list: item.list.map((it: AppItem) => ({
          ...it,
          check: '' as const,
        })),
      }));

      // 初始化申请请求数组
      applyReq.value = resp.data.records.map(() => ({
        pass_list: [],
        reject_list: [],
      }));

      // 如果有指定的用户ID，高亮显示
      if (route.query.id) {
        selectedUserId.value = route.query.id as string;
      }
    } else {
      message.error('获取申请列表失败');
    }
  } catch (error) {
    console.error('获取申请列表失败:', error);
    message.error('获取申请列表失败');
  } finally {
    loading.value = false;
  }
}

// 处理选项变化
function handleItemChange(item: AppItem, cardIndex: number) {
  if (item.check === 'pass') {
    addPassItem(item, cardIndex);
  } else if (item.check === 'reject') {
    addRejectItem(item, cardIndex);
  }
}

// 添加通过项
function addPassItem(item: AppItem, index: number) {
  if (!applyReq.value[index]) return;

  // 检查reject_list是否有，有的话删除
  const rejectIndex = applyReq.value[index].reject_list.indexOf(item.id);
  if (rejectIndex > -1) {
    applyReq.value[index].reject_list.splice(rejectIndex, 1);
  }

  // 检查pass_list是否已存在，避免重复添加
  if (!applyReq.value[index].pass_list.includes(item.id)) {
    applyReq.value[index].pass_list.push(item.id);
  }
}

// 添加拒绝项
function addRejectItem(item: AppItem, index: number) {
  if (!applyReq.value[index]) return;

  // 检查pass_list是否有，有的话删除
  const passIndex = applyReq.value[index].pass_list.indexOf(item.id);
  if (passIndex > -1) {
    applyReq.value[index].pass_list.splice(passIndex, 1);
  }

  // 检查reject_list是否已存在，避免重复添加
  if (!applyReq.value[index].reject_list.includes(item.id)) {
    applyReq.value[index].reject_list.push(item.id);
  }
}

// 检查是否通过
function isPass(id: string, index: number): boolean {
  return applyReq.value[index]?.pass_list.includes(id) || false;
}

// 检查是否拒绝
function isReject(id: string, index: number): boolean {
  return applyReq.value[index]?.reject_list.includes(id) || false;
}

// 提交审批
async function handleSubmit(index: number) {
  const data = applyReq.value[index];
  const approvalItem = approvalData.value[index];

  if (!data || !approvalItem) {
    message.error('数据异常，请刷新页面重试');
    return;
  }

  const totalItems = approvalItem.list.length;
  const reviewedItems = data.pass_list.length + data.reject_list.length;

  if (reviewedItems < totalItems) {
    message.warning('请审核完毕再提交');
    return;
  }

  try {
    const resp = await applyUpdate(data);
    if (resp.result) {
      message.success('提交成功');
      await fetchApplyList();
    } else {
      message.error(resp.message || '提交失败');
    }
  } catch (error) {
    console.error('提交失败:', error);
    message.error('提交失败');
  }
}

// 获取卡片样式
function getCardClass(item: ApprovalItem): string {
  return item.userid === selectedUserId.value ? 'highlight-card' : '';
}

onMounted(() => {
  fetchApplyList();
});
</script>

<template>
  <div class="p-5">
    <!-- 页面标题 -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-800 mb-2">防火墙应用审批</h1>
      <p class="text-gray-600">管理和审批用户的防火墙应用申请</p>
    </div>

    <!-- 搜索区域 -->
    <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
      <div class="flex items-center gap-4">
        <label class="text-gray-700 font-medium">账号：</label>
        <a-input
          v-model:value="searchAccount"
          placeholder="请输入账号"
          class="w-64"
          allow-clear
        />
        <a-button
          type="primary"
          :loading="loading"
          @click="fetchApplyList"
        >
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
      </div>
    </div>

    <!-- 申请列表 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-5">
      <a-spin :spinning="loading">
        <!-- 无申请提示 -->
        <div v-if="approvalData.length === 0" class="text-center py-12 text-gray-500">
          <div class="text-lg">暂无申请</div>
        </div>

        <!-- 申请卡片列表 -->
        <div v-else class="space-y-6">
          <a-card
            v-for="(item, index) in approvalData"
            :key="index"
            :class="['approval-card', getCardClass(item)]"
            size="default"
          >
            <!-- 卡片头部 -->
            <template #title>
              <div class="flex justify-between items-center w-full">
                <div class="flex items-center gap-3">
                  <span class="text-lg font-semibold text-gray-800">信息确认</span>
                  <span class="text-sm text-gray-500">
                    {{ item.uname }}({{ item.userid }})
                  </span>
                </div>
                <span class="text-sm text-gray-400">
                  申请时间：{{ formatTimestampToDate(item.modify_time, 'YYYY-MM-DD HH:mm:ss') }}
                </span>
              </div>
            </template>

            <!-- 卡片内容 -->
            <div class="space-y-4">
              <!-- 应用路径列表 -->
              <div class="space-y-3">
                <div
                  v-for="(appItem, appIndex) in item.list"
                  :key="appIndex"
                  class="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0"
                >
                  <div class="flex items-center gap-4 flex-1">
                    <span class="text-gray-600 min-w-20">应用路径：</span>
                    <span class="text-gray-800 flex-1">{{ appItem.app }}</span>
                  </div>

                  <a-radio-group
                    v-model:value="appItem.check"
                    @change="handleItemChange(appItem, index)"
                    class="ml-4"
                  >
                    <a-radio
                      value="pass"
                      @click="addPassItem(appItem, index)"
                    >
                      <span :class="{ 'text-green-600 font-medium': isPass(appItem.id, index) }">
                        通过
                      </span>
                    </a-radio>
                    <a-radio
                      value="reject"
                      @click="addRejectItem(appItem, index)"
                    >
                      <span :class="{ 'text-red-600 font-medium': isReject(appItem.id, index) }">
                        拒绝
                      </span>
                    </a-radio>
                  </a-radio-group>
                </div>
              </div>

              <!-- 申请理由 -->
              <div class="flex gap-4 pt-2">
                <span class="text-gray-600 min-w-20">申请理由：</span>
                <span class="text-orange-600 flex-1">{{ item.reason }}</span>
              </div>
            </div>

            <!-- 操作按钮 -->
            <template #actions>
              <div class="flex justify-end">
                <a-button
                  type="primary"
                  @click="handleSubmit(index)"
                  :loading="loading"
                >
                  <template #icon>
                    <CheckOutlined />
                  </template>
                  确认
                </a-button>
              </div>
            </template>
          </a-card>
        </div>
      </a-spin>
    </div>

    <!-- 高亮提示 -->
    <a-alert
      v-if="selectedUserId"
      :message="`当前查看用户ID: ${selectedUserId} 的申请`"
      type="info"
      show-icon
      class="mt-4"
    />
  </div>
</template>

<style scoped>
.approval-card {
  width: 100%;
  max-width: 800px;
  margin: 0 auto 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.approval-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.highlight-card {
  border: 2px solid #1890ff;
  background-color: #f0f8ff;
}

.highlight-card:hover {
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3);
}

/* 自定义单选按钮样式 */
:deep(.ant-radio-wrapper) {
  margin-right: 16px;
}

:deep(.ant-radio-wrapper:hover .ant-radio) {
  border-color: #1890ff;
}

/* 卡片头部样式 */
:deep(.ant-card-head) {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

:deep(.ant-card-head-title) {
  padding: 0;
  font-size: 16px;
  font-weight: 600;
}

/* 卡片内容样式 */
:deep(.ant-card-body) {
  padding: 20px 24px;
}

/* 操作按钮区域 */
:deep(.ant-card-actions) {
  background-color: #fafafa;
  border-top: 1px solid #f0f0f0;
  padding: 12px 24px;
}

:deep(.ant-card-actions > li) {
  margin: 0;
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .approval-card {
    margin: 0 0 16px;
  }

  :deep(.ant-card-head),
  :deep(.ant-card-body),
  :deep(.ant-card-actions) {
    padding-left: 16px;
    padding-right: 16px;
  }
}
</style>
