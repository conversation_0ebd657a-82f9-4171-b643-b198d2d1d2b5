import { mediaFormat } from '#/api/preview';
import { computed, ref, onBeforeMount } from 'vue';
import { useTaskStore } from '#/store';
import { storeToRefs } from 'pinia';
import { useRoute } from 'vue-router';
export enum MediaType {
  Audio = 'audio',
  Video = 'video',
  NotSupport = 'not_support',
  NotInit = 'not_init',
}

/**
 * 获取任务媒体类型的组合式函数
 * @returns 计算属性，返回当前任务的媒体类型（音频/视频/不支持/未初始化）
 * @example
 * const mediaType = useTaskMediaType();
 * if(mediaType.value === MediaType.Audio) {
 *   // 处理音频逻辑
 * }
 */
export const useTaskMediaType = () => {
  const supportAudio = ref<string[]>([]);
  const supportVideo = ref<string[]>([]);
  const route = useRoute();
  const taskStore = useTaskStore();
  const { taskFileExtension } = storeToRefs(taskStore);
  
  // 获取文件扩展名，优先使用taskStore中的值，如果不存在则尝试从路由参数获取
  const getFileExtension = computed(() => {
    return taskFileExtension.value || (route.query.ext as string) || '';
  });

  onBeforeMount(async () => {
    const resp = await mediaFormat();
    if(resp.result) {
      supportAudio.value = resp.sm_support_audio;
      supportVideo.value = resp.sm_support_video;
    }
  })
  
  return computed(() => {
    // 如果没有获取到文件扩展名或支持的格式列表尚未初始化
    if(getFileExtension.value === '' || supportAudio.value.length === 0 || supportVideo.value.length === 0) {
      return MediaType.NotInit;
    }
    
    // 判断是否为支持的音频格式
    if(supportAudio.value.includes(getFileExtension.value)) {
      return MediaType.Audio;
    }
    
    // 判断是否为支持的视频格式
    if(supportVideo.value.includes(getFileExtension.value)) {
      return MediaType.Video;
    }
    
    // 不支持的格式
    return MediaType.NotSupport;
  })
}
