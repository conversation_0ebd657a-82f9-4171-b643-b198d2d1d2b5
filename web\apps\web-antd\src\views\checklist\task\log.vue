<script setup lang="ts">
import { shallowRef, ref } from "vue";
import { storeToRefs } from "pinia";
import { useRoute } from "vue-router";
import {
  getLastNDaysRange,
  transformTimeFields,
  formatTimestampToDate,
} from "#/utils/time";
import TableTemplate from "#/components/table/index.vue";
import SceneSelect from "#/components/select/scene.vue";
import FileLink from "#/components/file/index.vue";
import StatusTag from "#/components/tag/index.vue";
import ForbidTypeSelect from "#/components/select/ForbidType.vue";
import { findLabelByValue } from "#/utils/data";
import { useBizInfo } from "#/composables/common/use-biz";
import useForbidStore from "#/store/forbid";
const forbidStore = useForbidStore();
const { allForbidTags } = storeToRefs(forbidStore);
import { useUserStore } from "#/store/user";
const userStore = useUserStore();
import { getHistoryLogs } from "#/api/operateLog";
import { OPERATE_MANUAL_STAUTS, DOC_MANUAL_COLOR } from "#/constants/maps/status";
import { mapToOptions } from "#/utils/transform";
import { EMPTY_OPTION } from "#/constants/options";
import { NO_LIMIT_NUM } from "#/constants/num";

const route = useRoute();
const { isDocer } = useBizInfo(route.query.from);

const searchOptions = {
  schemas: {
    "1": [
      {
        component: "Input",
        fieldName: "fileinx",
        label: "fileid",
        componentProps: {
          placeholder: "请输入",
        },
      },
      {
        component: shallowRef(SceneSelect),
        fieldName: "scene",
        label: "场景",
        componentProps: {
          placeholder: "请选择",
          biz: route.query.from,
        },
      },
      {
        component: "Input",
        fieldName: "extra_id",
        label: "附件id",
        componentProps: {
          placeholder: "请输入",
        },
      },
      {
        component: "Input",
        fieldName: "fname",
        label: "标题搜索",
        componentProps: {
          placeholder: "请输入",
        },
      },
      {
        component: "RadioGroup",
        fieldName: "fuzzy",
        label: "匹配类型",
        defaultValue: false,
        componentProps: {
          placeholder: "请输入",
          options: [
            {
              label: "全名匹配",
              value: false,
            },
            {
              label: "模糊匹配",
              value: true,
            },
          ],
        },
      },
      {
        component: "Select",
        fieldName: "status",
        defaultValue: "",
        label: "状态",
        componentProps: {
          placeholder: "请输入",
          options: [...EMPTY_OPTION, ...mapToOptions(OPERATE_MANUAL_STAUTS)],
        },
      },
      {
        fieldName: "types",
        component: shallowRef(ForbidTypeSelect),
        componentProps: { version: "v1", isCascader: true },
        label: "标签1.0",
      },
      {
        fieldName: "types2",
        component: shallowRef(ForbidTypeSelect),
        componentProps: { version: "v2", isCascader: true },
        label: "标签2.0",
      },
      // 日期范围
      {
        component: "RangePicker",
        fieldName: "dateRange",
        label: "日期范围",
        defaultValue: getLastNDaysRange(0, 0, "X"),
        class: "col-span-2",
        componentProps: {
          placeholder: ["开始日期", "结束日期"],
          format: "YYYY-MM-DD HH:mm:ss",
          valueFormat: "X",
        },
      },
    ],
  },
  showCollapseButton: true,
  submitOnChange: false,
  wrapperClass: "grid-cols-6",
};
const columns: VxeGridProps["columns"] = [
  {
    type: "seq",
    align: "center",
    title: "序号",
    fixed: "left",
    width: 100,
  },
  {
    title: "状态",
    slots: { default: "status" },
  },
  {
    field: "fileinx",
    title: "fileid",
  },
  {
    field: "scene",
    title: "场景",
  },
  {
    field: "extra_id",
    title: "附件id",
  },
  {
    title: "名称",
    slots: { default: "fname" },
    width: 180,
  },
  {
    field: "type",
    title: "类型",
    formatter: ({ cellValue }) =>
      findLabelByValue(allForbidTags.value, cellValue, "type", "name"),
  },

  {
    field: "userid",
    title: "userID",
    formatter: ({ row }) => (row.task_tag === 4 ? "-" : row.userid),
  },

  {
    title: "发布时间",
    formatter: ({ row }) => {
      return isDocer
        ? formatTimestampToDate(row.create_time)
        : formatTimestampToDate(row.link_ctime);
    },
  },
];
const paginationOptions = ref({
  pageSize: 20,
  currentPage: 1,
  layouts: ["PrevPage", "NextPage"],
});
const getData = async ({
  page,
  pageSize,
  ...searchValues
}: {
  page: number;
  pageSize: number;
  [key: string]: any;
}) => {
  try {
    let params: any = {
      page: page - 1,
      size: pageSize,
      from: route.query.from,
      reviewer: userStore.username, // 等权限的改好
      reviewer_role: "manual",
      ...transformTimeFields(searchValues, [["dateRange", ["start_time", "end_time"]]]),
    };
    // 处理types字段，如果存在则取最后一项作为type字段
    if (searchValues.types && searchValues.types.length > 0) {
      params.type = searchValues.types[searchValues.types.length - 1];
      delete params.types;
    }
    const res = await getHistoryLogs(params);
    let items = res.data.logs || [];
    return { items, total: items.length == pageSize ? NO_LIMIT_NUM : 0 };
  } catch (error) {
    return { items: [], total: 0 };
  }
};
</script>
<template>
  <div>
    <table-template
      ref="tableTemplateRef"
      :columns="columns"
      :search-options="searchOptions"
      :query-method="getData"
      :pagination-options="paginationOptions"
    >
      <template #status="{ row }">
        <status-tag
          :status="row.status"
          default-tag="未操作"
          :tag-map="OPERATE_MANUAL_STAUTS"
          :color-map="DOC_MANUAL_COLOR"
        />
      </template>
      <template #fname="{ row }">
        <file-link :item="row" maxWidth="130px" />
      </template>
    </table-template>
  </div>
</template>
