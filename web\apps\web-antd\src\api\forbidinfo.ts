import { requestClient } from '#/api/request';

export const getForbidInfo = (data: any) => {
    return requestClient.get('/manual/preview/forbidinfo', { data });
};

export const getRecordBizData = (data: any) => {
    return requestClient.get('/manual/record/search_biz_data', { data });
};
export const avatarReset = (data: any) => {
    return requestClient.post('/manual/avatar/reset', data);
};
export const nicknameReset = (data: any) => {
    return requestClient.post('/manual/nickname/reset', data);
};
export const homeuserReset = (data: any) => {
    return requestClient.post('/manual/record/reset_home_user', data);
};