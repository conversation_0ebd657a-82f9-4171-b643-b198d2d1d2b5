<script lang="ts" setup>
import { computed, ref, watch } from 'vue';

import { storeToRefs } from 'pinia';

import { getExpireUrlBatch } from '#/api/preview';
import { getPreviewImageInfo } from '#/api/task';
import Field from '#/components/common/Field.vue';
import ImgView from '#/components/image/index.vue';
import { MACHINE_IMG_LABEL_MAP } from '#/constants/maps/task';
import { useTaskStore } from '#/store';
import useForbidStore from '#/store/forbid';
import usePlatformStore from '#/store/platform';

import WordLevelBox from './WordLevelBox.vue';

const platformStore = usePlatformStore();
// 控制显示全部图片的Modal
const showAllImagesModal = ref(false);
// 最大显示图片数
const MAX_DISPLAY_IMAGES = 9;

const taskStore = useTaskStore();
const { taskItem } = storeToRefs(taskStore);
const ai_label_desc = ref<string>('');
const forbidStore = useForbidStore();
const imgInfos = ref<any[]>([]);
// 新增：记录加载失败的图片数量
const failedImagesCount = ref(0);
// 新增：当前预览的图片索引（用于大图查看时的左右切换）
const currentPreviewIndex = ref<number>(0);
// 新增：全局大图查看器显示状态
const showGlobalViewer = ref<boolean>(false);
/**
 * 处理标签字符串，将其转换为对应的中文标签数组
 * @param {string} label - 原始标签字符串，以逗号分隔
 * @returns {string[]} - 返回处理后的中文标签数组（已去重和过滤空值），如果没有标签则返回空数组
 * @example
 * // 返回 ["涉政", "色情"]
 * processLabel("polity,porn")
 * // 返回 ["涉政"] (去重)
 * processLabel("polity,polity")
 * // 返回 [] (无标签)
 * processLabel("")
 */
const processLabel = (label: string): string[] => {
  console.log('labels', label);
  if (!label) {
    return [];
  }

  // 将标签字符串分割成数组
  let labels = label.split(',');

  // 过滤空字符串、"pass"和"ignore"标签
  labels = labels.filter((item) => {
    const trimmedItem = item.trim();
    return (
      trimmedItem !== '' && trimmedItem !== 'pass' && trimmedItem !== 'ignore'
    );
  });

  // 如果没有标签，返回空数组
  if (labels.length === 0) {
    return [];
  }

  // 将每个标签映射为对应的中文标签
  const result = labels.map((item) => MACHINE_IMG_LABEL_MAP[item] || '未知');

  // 对结果数组进行去重
  const uniqueResult = [...new Set(result)];

  console.log('labels', labels, uniqueResult);
  return uniqueResult;
};
const processedImgInfos = computed(() => {
  return imgInfos.value.map((imgInfo) => {
    const labels = processLabel(imgInfo.label);

    // 复制标签数组以便修改
    const finalLabels = [...labels];

    // 确定图片边框颜色
    let borderColor = '';
    if (imgInfo.is_reco_hit) {
      borderColor = 'red';
    } else if (imgInfo.is_ocr_hit) {
      borderColor = 'blue';
    } else if (imgInfo.is_qr_code_hit) {
      borderColor = 'yellow';
      // 为二维码添加特殊标签
      finalLabels.push('二维码');
    }

    return {
      ...imgInfo,
      processedLabels: finalLabels,
      hasLabels: finalLabels.length > 0,
      borderColor,
    };
  });
});
const getPreviewImageInfoData = async () => {
  if (!taskItem.value) {
    return;
  }
  const res = await getPreviewImageInfo({
    from: taskItem.value?.biz,
    fsha: taskItem.value?.fsha,
    scene: taskItem.value?.scene,
    extra_id: taskItem.value?.extra_id,
    fileinx: taskItem.value?.fileinx,
  });
  if (!res.result || !res.data || !res.data.images) {
    imgInfos.value = [];
    return;
  }
  // 获取图片缩放配置
  const imageScaleConfig =
    platformStore.currentQueueConfig?.image_scale_config || {};
  const scale_width = imageScaleConfig.scale_width;
  const scale_height = imageScaleConfig.scale_height;

  // 准备批量获取过期URL的参数
  const items = res.data.images.map((item: any) => ({
    key: item.key,
    bucket: item.bucket || '',
    mode: 'image_scale',
    image_scale_width: scale_width,
    image_scale_height: scale_height,
  }));
  // 如果有图片，则请求刷新过期时间
  if (items.length > 0) {
    try {
      const expireUrlBatchRes = await getExpireUrlBatch(items);

      // 如果成功获取了新的URL，更新图片信息
      if (
        expireUrlBatchRes.result &&
        expireUrlBatchRes.data &&
        expireUrlBatchRes.data.items
      ) {
        // 创建URL映射表
        const urlMap = expireUrlBatchRes.data.items.reduce(
          (map: Record<string, string>, item: any) => {
            if (item.key && item.url) {
              map[item.key] = item.url;
            }
            return map;
          },
          {},
        );

        // 更新图片信息中的URL
        res.data.images = res.data.images.map((item: any) => {
          if (item.url && urlMap[item.url]) {
            return { ...item, url: urlMap[item.url] };
          }
          return item;
        });
      }
    } catch (error) {
      console.error('获取图片过期URL失败:', error);
    }
  }
  console.log('机审图片', res.data);
  imgInfos.value = res.data.images;
  ai_label_desc.value = res.data.ai_label_desc;
};
const hasOcrKeywords = computed(() => {
  return (
    taskItem.value?.external_json?.ocr?.keywords &&
    taskItem?.value?.external_json?.ocr?.keywords?.length > 0
  );
});
const hasKeywords = computed(() => {
  return (
    taskItem.value?.external_json?.keywords &&
    taskItem?.value?.external_json?.keywords?.length > 0
  );
});

// 计算文档类别
const docCategory = computed(() => {
  console.log('taskItem', taskItem.value, taskItem.value?.doc_type);
  let type = taskItem.value?.doc_type || Number.parseInt(ai_label_desc.value);
  if (type === 12) {
    type = 10;
  }
  taskStore.setSelectedForbidType(type || 0);
  const forbidTypes = forbidStore.enabledForbidTags;
  const forbidType = forbidTypes?.find((item: any) => item.type === type);
  // 这里可以根据实际逻辑计算文档类别
  return forbidType?.name || '未知';
});
// 计算是否有图片识别结果
const hasImageResults = computed(() => {
  return imgInfos.value.length > 0;
});

// 计算是否显示"显示全部"按钮
const shouldShowViewAllButton = computed(() => {
  return processedImgInfos.value.length > MAX_DISPLAY_IMAGES;
});

// 显示的图片列表
const displayedImages = computed(() => {
  if (shouldShowViewAllButton.value) {
    return processedImgInfos.value.slice(0, MAX_DISPLAY_IMAGES);
  }
  return processedImgInfos.value;
});

/**
 * 打开查看全部图片的Modal
 */
const openAllImagesModal = () => {
  showAllImagesModal.value = true;
};

/**
 * 处理图片点击事件，设置当前预览索引
 * @param index 图片索引
 */
const handleImageClick = (index: number) => {
  currentPreviewIndex.value = index;
  showGlobalViewer.value = true;
  console.log('CardTaskMachine - handleImageClick:', { index, showGlobalViewer: showGlobalViewer.value });
};

/**
 * 处理切换到上一张图片
 */
const handlePrevImage = () => {
  console.log('CardTaskMachine - handlePrevImage, 当前索引:', currentPreviewIndex.value);
  if (currentPreviewIndex.value > 0) {
    currentPreviewIndex.value--;
  }
};

/**
 * 处理切换到下一张图片
 */
const handleNextImage = () => {
  console.log('CardTaskMachine - handleNextImage, 当前索引:', currentPreviewIndex.value);
  if (currentPreviewIndex.value < processedImgInfos.value.length - 1) {
    currentPreviewIndex.value++;
  }
};

/**
 * 关闭全局大图查看器
 */
const handleCloseViewer = () => {
  showGlobalViewer.value = false;
  console.log('CardTaskMachine - handleCloseViewer');
};

/**
 * 计算属性：获取当前显示图片的URL列表（用于大图切换）
 * @returns {string[]} 图片URL列表
 */
const imageUrls = computed(() => {
  return processedImgInfos.value.map(img => img.url).filter(Boolean) as string[];
});

watch(
  () => taskItem.value,
  (newVal) => {
    // 检查newVal是否存在且不是空对象
    if (!!newVal && Object.keys(newVal).length > 0) {
      ai_label_desc.value = '';
      getPreviewImageInfoData();
    }
  },
);
</script>
<template>
  <div>
    <a-card size="small">
      <template #title>
        <div class="flex items-center justify-between" size="small">
          <span class="font-semibold">机器检测结果</span>
        </div>
      </template>
      <div class="max-h-[200px] space-y-0.5 overflow-y-auto">
        <!-- 文档类别 -->
        <field :value="docCategory" label="文档类别" />

        <!-- 文本识别结果 -->
        <field
          :direction="hasKeywords || hasOcrKeywords ? 'vertical' : 'horizontal'"
          label="文本识别结果"
          label-item-position="start"
        >
          <word-level-box
            v-if="hasKeywords"
            :wordlist="taskItem?.external_json.keywords || []"
          />
          <word-level-box
            v-if="hasOcrKeywords"
            :is-ocr="true"
            :wordlist="taskItem?.external_json.ocr.keywords || []"
          />
          <span v-if="!hasKeywords && !hasOcrKeywords">无</span>
        </field>

        <!-- 图片识别结果 -->
        <field
          :direction="hasImageResults ? 'vertical' : 'horizontal'"
          class="relative"
          label="图片识别结果"
        >
          <!-- 显示全部按钮 -->
          <a-button
            v-if="shouldShowViewAllButton"
            class="absolute right-2 top-0 z-10"
            size="small"
            type="link"
            @click="openAllImagesModal"
          >
            展示全部图片
          </a-button>
          <div
            v-if="hasImageResults"
            class="relative flex max-h-24 flex-wrap gap-2 overflow-auto overflow-y-auto border border-gray-300 p-2"
          >
            <img-view
              v-for="(imgInfo, index) in displayedImages"
              :key="imgInfo.url"
              :class="[
                imgInfo.borderColor === 'red' ? 'border-2 border-red-500' : '',
                imgInfo.borderColor === 'blue'
                  ? 'border-2 border-blue-500'
                  : '',
                imgInfo.borderColor === 'yellow'
                  ? 'border-2 border-yellow-500'
                  : '',
              ]"
              :file-item="taskItem"
              :image-item="imgInfo"
              :show-hover-image="true"
              :src="imgInfo.url"
              :tag-color="imgInfo.hasLabels ? 'red' : ''"
              :tag-text="imgInfo.processedLabels"
              :tooltip-height="300"
              :tooltip-width="300"
              :current-index="currentPreviewIndex"
              :total-images="processedImgInfos.length"
              :image-list="imageUrls"
              :is-current="index === currentPreviewIndex"
              :show-global-viewer="showGlobalViewer"
              :enable-image-navigation="true"
              alt="图片识别结果"
              class="h-20 w-20 overflow-hidden rounded"
              @prev-image="handlePrevImage"
              @next-image="handleNextImage"
              @close-viewer="handleCloseViewer"
              @click="() => handleImageClick(index)"
            />
          </div>
          <span v-else>无</span>
        </field>
      </div>
    </a-card>

    <!-- 显示全部图片的Modal -->
    <a-modal
      v-model:open="showAllImagesModal"
      :destroy-on-close="true"
      :footer="null"
      title="全部图片"
      width="80%"
    >
      <div class="flex max-h-[70vh] flex-wrap gap-2 overflow-auto p-2">
        <img-view
          v-for="(imgInfo, index) in processedImgInfos"
          :key="imgInfo.url"
          :class="[
            imgInfo.borderColor === 'red' ? 'border-2 border-red-500' : '',
            imgInfo.borderColor === 'blue' ? 'border-2 border-blue-500' : '',
            imgInfo.borderColor === 'yellow'
              ? 'border-2 border-yellow-500'
              : '',
          ]"
          :src="imgInfo.url"
          :current-index="currentPreviewIndex"
          :total-images="processedImgInfos.length"
          :image-list="imageUrls"
          :is-current="index === currentPreviewIndex"
          :show-global-viewer="showGlobalViewer"
          :enable-image-navigation="true"
          alt="图片识别结果"
          class="h-24 w-24 overflow-hidden rounded"
          @prev-image="handlePrevImage"
          @next-image="handleNextImage"
          @close-viewer="handleCloseViewer"
          @click="() => handleImageClick(index)"
        />
      </div>
    </a-modal>
  </div>
</template>
<style scoped>
:deep(.ant-card-head) {
  background-color: #006be6;
  color: #fff;
  min-height: 30px;
}
</style>
