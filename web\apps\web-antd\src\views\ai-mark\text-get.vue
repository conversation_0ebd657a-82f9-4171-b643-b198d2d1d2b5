<script setup lang="ts">
import { computed, onBeforeMount, onUnmounted, ref } from 'vue';
import { useRoute } from 'vue-router';

import MarkLabel from '#/components/ai-mark/label-mark.vue';
import Field from '#/components/common/Field.vue';
import CheckTabList from '#/components/task/CheckTabList.vue';
import ViewBoard from '#/components/task/ViewBoard.vue';
import { useDocumentManager } from '#/composables/ai-mark/use-document-manager';
import { useMarkedResults } from '#/composables/ai-mark/use-marked-results';
import { INFORM_REASON_MAP } from '#/constants/maps';

import MarkedItem from './components/marked-item.vue';
// 导入抽离的hooks
import { useTextSelection } from '#/composables/ai-mark/use-text-selection';
import { useTaskStore } from '#/store';

const contentCard = ref<any>(null);
const entry = ref<string>('task_ai_mark_extract'); // 改成根据路由匹配
const taskStore = useTaskStore();
const route = useRoute();

const isReport = computed(() => {
  return entry.value === 'task_ai_mark_report';
});
// 使用文档管理hook
const {
  documents: data,
  currentIndex: index,
  content,
  isLoading,
  loadDocuments: loadData,
  focusDocument: focusIndex,
  getCurrentDocument,
  updateDocumentStatus,
  nextUnprocessedDocument: nextFocus,
  clear: clearDocuments,
} = useDocumentManager(entry);

// 使用文本选择hook
const {
  hasSelectedText,
  selectedText,
  handleTextSelection: handleTextSelectionBase,
  clearSelection,
} = useTextSelection();

// 使用标记结果管理hook
const {
  markedTexts,
  addMarkedText,
  removeMarkedItem,
  confirmAndSubmit,
  clearMarkedTexts,
} = useMarkedResults(entry);

// 文档选择变更处理
const handleChange = async (taskItem: any) => {
  clearMarkedTexts();
  await focusIndex(taskItem.index);
};

// 自定义文本选择处理，传入容器元素
const handleTextSelection = () => {
  if (!contentCard.value) return;
  const container = contentCard.value.$el || contentCard.value;
  handleTextSelectionBase(container);
};

// 标签点击处理
const handleLabelMark = (label: any) => {
  if (!selectedText.value) return;
  addMarkedText(selectedText.value, label);
  hasSelectedText.value = false;
};

// 处理提交
const handleSubmit = () => {
  const doc = getCurrentDocument();
  if (!doc) return;

  confirmAndSubmit(doc, (document: any) => {
    // 更新文档状态
    updateDocumentStatus(document.index, {
      buttonType: 'warning',
      forbidText: '已提取',
      result: true,
    });
    taskStore.incrementFinished();
    // 检查是否所有项目都已处理
    const hasAllResults = data.value.every((item) => item.result !== undefined);
    if (hasAllResults) {
      // 全部处理完成，清空列表
      clearDocuments();
    } else {
      // 自动跳转到下一个未处理的项目
      nextFocus();
    }
  });
};

// 组件卸载时清理资源
onUnmounted(() => {
  clearSelection();
  clearMarkedTexts();
  clearDocuments();
  taskStore.resetCounter();
});
onBeforeMount(() => {
  const path = route.path;
  if (path.includes('aiComplaintMarkTask')) {
    entry.value = 'task_ai_mark_report';
  }
});
</script>
<template>
  <div class="h-full max-h-[calc(100vh-50px)] w-full">
    <view-board
      :disabled-fields="['forbid', 'preview', 'date', 'checkLog', 'supplier']"
    />
    <div class="flex h-[calc(100%-45px)] w-full items-center justify-center">
      <a-button v-if="data.length === 0" type="link" @click="loadData">
        加载复审~
      </a-button>

      <div v-else class="flex h-full w-full">
        <div
          class="flex w-3/4 flex-col items-center border-r p-4 lg:w-3/5 xl:w-4/5"
        >
          <field
            v-if="isReport"
            :value="INFORM_REASON_MAP[data[index]?.label]"
            class="mb-4 w-full"
            label="违规原因"
          />
          <a-card
            ref="contentCard"
            class="h-full w-full overflow-y-auto p-4 font-medium"
            v-loading="isLoading"
            @mouseup="handleTextSelection"
          >
            <div
              v-if="
                isReport &&
                typeof content === 'object' &&
                content.input &&
                content.output
              "
            >
              <field :value="content.input" label="Input" label-width="4rem" />
              <field
                label="Output"
                label-item-position="start"
                label-width="4rem"
              >
                <div
                  class="overflow-auto text-wrap"
                  v-html="content.output"
                ></div>
              </field>
            </div>
            <div v-else v-html="content"></div>
          </a-card>
          <div class="mb-4 w-full">
            <check-tab-list
              v-model:index="index"
              :options="data"
              @change="handleChange"
            />
          </div>
        </div>
        <!-- 文本内容提取区域 -->
        <div class="flex w-1/4 flex-col items-center p-4 lg:w-2/5 xl:w-1/5">
          <a-card class="h-full w-full overflow-auto">
            <div class="mb-8 text-xl font-medium">提取样本内容</div>
            <div class="space-y-4">
              <template v-if="markedTexts.length === 0">
                <div class="text-center text-gray-400">
                  暂无提取内容，请从左侧文本中选择并标记
                </div>
              </template>
              <template v-for="(item, idx) in markedTexts" :key="idx">
                <marked-item
                  :index="idx"
                  :item="item"
                  @remove="removeMarkedItem"
                />
              </template>
            </div>
          </a-card>
          <div class="my-4 flex w-full justify-end">
            <a-button class="mx-2 w-24" type="primary" @click="handleSubmit">
              提交
            </a-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 选中文本对话框 -->
    <a-modal
      v-model:open="hasSelectedText"
      :footer="null"
      :width="500"
      title="复审"
    >
      <div class="py-4">
        <div class="mb-4">{{ selectedText }}</div>
        <mark-label
          :disabled="!hasSelectedText"
          :entry="entry"
          @mark="handleLabelMark"
        />
      </div>
    </a-modal>
  </div>
</template>
