<script setup lang="ts">
import { ref } from "vue";
import { useRoute } from "vue-router";
import {
  getLastNDaysRange,
  transformTimeFields,
  formatTimestampToDate,
} from "#/utils/time";
import TableTemplate from "#/components/table/index.vue";
import StatusTag from "#/components/tag/index.vue";
import { getTextLogs } from "#/api/operateLog";
import { OPERATE_MANUAL_STAUTS, DOC_MANUAL_COLOR } from "#/constants/maps/status";
const route = useRoute();
import { TEXT_FORBID_LABEL_MAP } from "#/constants/maps/task";
import { NO_LIMIT_NUM } from "#/constants/num";
const searchOptions = {
  schemas: {
    "1": [
      {
        component: "Input",
        fieldName: "fname",
        label: "队列",
        componentProps: {
          // placeholder: "请输入",
        },
      },
      {
        component: "Input",
        fieldName: "userid",
        label: "userid",
      },
      {
        component: "Input",
        fieldName: "nickname",
        label: "文本",
      },
      {
        component: "Input",
        fieldName: "company",
        label: "企业id",
      },
      {
        component: "Input",
        fieldName: "scene",
        label: "场景",
      },
      {
        component: "Input",
        fieldName: "extra_id",
        label: "附件id",
      },
      {
        component: "RangePicker",
        fieldName: "dateRange",
        label: "时间",
        defaultValue: getLastNDaysRange(0, 0, "X"),
        class: "col-span-2",
        componentProps: {
          format: "YYYY-MM-DD HH:mm:ss",
          valueFormat: "X",
        },
      },
    ],
  },
  showCollapseButton: true,
  submitOnChange: false,
  wrapperClass: "grid-cols-6",
};
const columns: VxeGridProps["columns"] = [
  {
    type: "seq",
    align: "center",
    title: "序号",
    fixed: "left",
    width: 100,
  },
  {
    field: "uuid",
    title: "uuid",
  },
  {
    field: "scene",
    title: "场景",
  },
  {
    field: "extra_id",
    title: "附件id",
  },
  {
    field: "company",
    title: "企业id",
  },
  {
    field: "userid",
    title: "userid",
  },
  {
    field: "task_name",
    title: "队列",
  },
  {
    field: "fileid",
    title: "文本id",
  },
  {
    field: "fileinx",
    title: "fileID",
  },
  {
    field: "content",
    title: "文本内容",
  },
  {
    title: "状态",
    slots: { default: "status" },
  },
  {
    title: "类型",
    formatter: ({ row }) => {
      return row.reviewer_name == "ai" ? "-" : TEXT_FORBID_LABEL_MAP[row.type];
    },
  },
  {
    field: "hits",
    title: "召回环节",
  },
  {
    title: "机审时间",
    formatter: ({ row }) => {
      return formatTimestampToDate(row.create_time);
    },
  },
  {
    title: "拉单时间",
    formatter: ({ row }) => {
      return formatTimestampToDate(row.create_time + parseInt(row.delay_time || "0"));
    },
  },
  {
    title: "操作时间",
    formatter: ({ row }) => {
      return formatTimestampToDate(row.modify_time);
    },
  },
];
// 分页配置
const paginationOptions = ref({
  pageSize: 20,
  currentPage: 1,
  layouts: ["PrevPage", "NextPage"],
});
const getData = async ({
  page,
  pageSize,
  ...searchValues
}: {
  page: number;
  pageSize: number;
  [key: string]: any;
}) => {
  try {
    let params: any = {
      page: page - 1,
      size: pageSize,
      from: route.query.biz,
      ...transformTimeFields(searchValues, [["dateRange", ["start_time", "end_time"]]]),
    };
    const res = await getTextLogs(params);
    let items = res.data.logs || [];
    return { items, total: items.length == pageSize ? NO_LIMIT_NUM : 0 };
  } catch (error) {
    return { items: [], total: 0 };
  }
};
</script>
<template>
  <div>
    <table-template
      ref="tableTemplateRef"
      :columns="columns"
      :search-options="searchOptions"
      :pagination-options="paginationOptions"
      :query-method="getData"
    >
      <template #status="{ row }">
        <status-tag
          :status="row.status"
          default-tag="未操作"
          :tag-map="OPERATE_MANUAL_STAUTS"
          :color-map="DOC_MANUAL_COLOR"
        />
      </template>
    </table-template>
  </div>
</template>
