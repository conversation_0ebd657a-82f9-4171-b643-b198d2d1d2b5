<script setup lang="ts">
import { shallowRef, ref, computed } from "vue";
import type { VxeGridProps } from "#/adapter/vxe-table";
import { getSentimentHitRecord } from "#/api/operateLog";
import {
  getLastNDaysRange,
  transformTimeFields,
  formatTimestampToDate,
} from "#/utils/time";
import TableTemplate from "#/components/table/index.vue";
import SceneSelect from "#/components/select/scene.vue";
import BizSelect from "#/components/select/biz.vue";
import FileLink from "#/components/file/index.vue";
import StatusTag from "#/components/tag/index.vue";
import ContentDisplay from "./content-display.vue";

import { IMAGE_LABEL_MAP, DOC_MANUAL_COLOR } from "#/constants/maps/status";
import {
  LIBRARY_TYPE_MAP,
  IS_HIT_MAP,
  FILE_AI_LABEL_MAP,
  SIMILARITY_SORT_MAP,
  PURPOSE_TYPE,
} from "#/constants/maps";

import { mapToOptions } from "#/utils/transform";
const props = defineProps({
  type: {
    type: String,
    default: "",
  },
});
const getData = async ({ page, pageSize, scene, ...formValues }: any) => {
  let params: any = {
    page: page - 1,
    size: pageSize,
    ...transformTimeFields(formValues, [
      ["dateRange", ["start_time_stamp", "end_time_stamp"]],
    ]),
  };
  if (props.type == "black") {
    params.hit_black_similarity = 1;
  }
  if (props.type == "white") {
    params.hit_white_similarity = 1;
  }
  const res = await getSentimentHitRecord(params);
  const items = res?.data?.data.map((item: any) => {
    item.hitStatus = item.ai_label == "manual" ? 3 : item.is_hit ? 1 : 2; // 命中状态
    return item;
  });
  return { items: items, total: res.data.total_count };
};
const isBlack = computed(() => props.type == "black");

const searchOptions = {
  collapsed: false, // 默认展开
  schemas: {
    "1": [
      {
        component: shallowRef(BizSelect),
        fieldName: "biz",
        label: "业务",
        componentProps: {
          placeholder: "请选择",
        },
      },
      {
        component: "Select",
        fieldName: "purpose",
        label: props.type == "black" ? "黑库" : "白库",
        componentProps: {
          options: mapToOptions(LIBRARY_TYPE_MAP[props.type]),
        },
      },
      {
        component: "Select",
        fieldName: "category",
        label: "类型",
        dependencies: {
          if(values: any) {
            return isBlack.value;
          },
          triggerFields: [""],
        },
      },
      {
        component: "Select",
        fieldName: "is_hit",
        label: "是否命中",
        componentProps: {
          options: mapToOptions(IS_HIT_MAP),
        },
      },
      {
        component: "Select",
        fieldName: "ai_label",
        label: "状态",
        componentProps: {
          options: mapToOptions(FILE_AI_LABEL_MAP),
        },
      },

      {
        component: "Input",
        fieldName: "sample_id",
        label: "样本ID",
        dependencies: {
          if(values: any) {
            return isBlack.value;
          },
          triggerFields: [""],
        },
      },
      {
        component: "Input",
        fieldName: "fileid",
        label: "fileID",
        componentProps: {
          placeholder: "请输入",
        },
      },
      {
        component: shallowRef(SceneSelect),
        fieldName: "scene",
        label: "场景",
        componentProps: {
          placeholder: "请选择场景",
          class: "max-w-64 w-full",
          mode: "tags",
          maxTagCount: "responsive",
        },
        dependencies: {
          componentProps(values: any) {
            return {
              biz: values.biz,
            };
          },
          triggerFields: ["biz"],
        },
      },
      {
        component: "Input",
        fieldName: "extra_id",
        label: "附件ID",
        componentProps: {
          placeholder: "请输入",
        },
      },
      {
        component: "Select",
        fieldName: "similarity_order",
        label: "相似度排序",
        componentProps: {
          placeholder: "请选择",
          options: mapToOptions(SIMILARITY_SORT_MAP),
        },
      },
      {
        component: "RangePicker",
        fieldName: "dateRange",
        label: "时间",
        class: "col-span-2",
        defaultValue: getLastNDaysRange(0, 0, "X"),
        componentProps: {
          valueFormat: "X",
          placeholder: ["开始时间", "结束时间"],
        },
      },
    ],
  },
  showCollapseButton: true,
  submitOnChange: false,
  wrapperClass: "grid-cols-6",
};

const columns: VxeGridProps["columns"] = [
  // { type: "expand", width: 80, slots: { content: "expand_content" } },
  {
    type: "seq",
    align: "center",
    title: "序号",
    width: 100,
  },
  {
    field: "purpose",
    title: "命中类型",
    visible: isBlack.value,
    formatter: ({ cellValue }) => PURPOSE_TYPE[cellValue],
  },
  {
    field: "biz",
    title: "业务编号",
  },
  {
    field: "fileid",
    title: "fileID",
  },
  {
    field: "extra_id",
    title: "附件id",
  },
  {
    field: "scene",
    title: "场景",
  },
  {
    title: "名称",
    width: 200,
    slots: {
      default: "fname",
    },
  },
  {
    title: "状态",
    slots: {
      default: "status",
    },
  },
  {
    title: "原始数据",
    slots: {
      default: "file_content",
    },
  },
  // 前置数据
  {
    title: "前置数据",
    slots: {
      default: "union_sample_content",
    },
  },
  {
    title: "后置数据",
    slots: {
      default: "sample_content",
    },
  },
  {
    field: "sample_id",
    title: "后置ID",
    visible: isBlack.value,

    formatter: ({ cellValue }) => (cellValue <= 0 ? "无" : cellValue),
  },
  {
    field: "union_sample_id",
    title: "前置ID",
    visible: isBlack.value,

    formatter: ({ cellValue }) => (cellValue <= 0 ? "无" : cellValue),
  },
  {
    field: "similarity",
    title: "样本类型",
    visible: isBlack.value,
  },
  {
    field: "similarity",
    title: "相似度",
    visible: isBlack.value,
  },
  {
    title: "命中状态",
    slots: {
      default: "hitStatus",
    },
  },
  // {
  //   field: "cost",
  //   title: "耗时",
  // },
  {
    field: "now_time",
    title: "创建时间",
    formatter: ({ cellValue }) => formatTimestampToDate(cellValue),
  },
];
</script>

<template>
  <table-template
    :columns="columns"
    :query-method="getData"
    :search-options="searchOptions"
  >
    <!-- <template #expand_content="{ row }">
      <div>zxqzxq：</div>
    </template> -->
    <template #fname="{ row }">
      <file-link :item="{ ...row, fname: row.f_name }" />
    </template>
    <template #file_content="{ row }">
      <content-display :row="row" field="file_content" />
    </template>
    <template #union_sample_content="{ row }">
      <content-display :row="row" field="union_sample_content" />
    </template>
    <template #sample_content="{ row }">
      <content-display :row="row" field="sample_content" />
    </template>
    <template #status="{ row }">
      <status-tag
        :status="row.ai_label"
        :tag-map="IMAGE_LABEL_MAP"
        :color-map="DOC_MANUAL_COLOR"
      />
    </template>
    <template #hitStatus="{ row }">
      <status-tag
        :status="row.hitStatus"
        :tag-map="IS_HIT_MAP"
        :color-map="{
          1: 'error',
          2: 'success',
          3: 'error',
        }"
      />
    </template>
  </table-template>
</template>
