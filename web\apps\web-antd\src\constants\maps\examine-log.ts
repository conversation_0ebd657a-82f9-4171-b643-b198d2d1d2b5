/* 此文件下存放全部数据列表页面的常量 */

// 审批状态映射
export const EXAMINE_STATUS_MAP = {
  ' ': '全部',
  ok: '通过',
  forbid: '未通过',
  manual_verifying: '未操作',
};
export const OFFLINE_STATUS_MAP = {
  ok: '通过',
  forbid: '拒绝',
  manual_verifying: '未操作',
};
// 审批角色映射
export const EXAMINE_ROLE_MAP = {
  ' ': '全部',
  ai: '机器',
  manual: '人工',
};

// 文档类型映射
export const RECORD_FLAG_MAP = {
  '0': '全部文档',
  '4096': '文档-1',
  '16': '文档-2',
  '4194304': '文档-3',
  '33554432': '所有人',
  '16777216': '指定人',
  '268435456': '翻译文档',
};
export const FLAG_MAP = [
  { mask: 0x1000, label: '文档-1' },
  { mask: 0x10, label: '文档-2' },
  { mask: 0x02000000, label: '所有人' },
  { mask: 0x01000000, label: '指定人' },
  { mask: 0x00400000, label: '文档-3' },
  { mask: 0x10000000, label: '翻译文档' },
];
// 预览类型映射
export const URL_MODE_MAP = {
  ' ': '智能识别',
  '0': '云文档',
  '1': '个人云',
  '2': '新发布',
  '3': '发布链接',
};
export const EXAMINE_TYPE_MAP = {
  '10': '政治',
  '20': '色情低俗',
  '30': '广告',
  '50': '非法',
};
export const EXAMINE_TAG_MAP = {
  ok_normal: '通过',
  ok_pvless_1: '通过',
  ok_pvless_2: '通过',
  forbid: '拒绝',
  verifying: '机审中',
  ' ': '未操作',
};

export const EXAMINE_COLOR_MAP = {
  ok_normal: 'green',
  ok_pvless_1: 'yellow',
  ok_pvless_2: 'grey',
  forbid: 'red',
  verifying: 'grey',
  ok: 'green',
  '': 'grey',
};
export const EXAMINE_SENT_MAP: Record<string, string> = {
  '1': '正面',
  '2': '负面',
  '3': '中性',
};
export const typeData = [
  { name: '通过', value: 0 },
  { name: '政治', value: 10 },
  { name: '色情低俗', value: 20 },
  { name: '广告', value: 30 },
  { name: '非法', value: 50 },
];
