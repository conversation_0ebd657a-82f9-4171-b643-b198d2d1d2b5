<script lang="ts" setup>
import { ref, watch } from 'vue';

import { storeToRefs } from 'pinia';

import usePlatformStore from '#/store/platform';
import { useTaskStore } from '#/store/task';
import useWebSocketStore from '#/store/websocket';

import AiTextMark from './ai-text-mark.vue';
import SupervisionMark from './supervision-mark.vue';
import TextImageMark from './text-image-mark.vue';

const props = defineProps<{
  entryOptions: any[];
}>();
const taskStore = useTaskStore();
const websocketStore = useWebSocketStore();
const { queueCountDelayData } = storeToRefs(websocketStore);
const { queue } = storeToRefs(usePlatformStore());
const entry = ref(props.entryOptions[0].value);
watch(
  entry,
  (val: string) => {
    taskStore.resetCounter();
    queue.value = val;
  },
  { immediate: true },
);

const getBadgeCount = (value: string) => {
  return queueCountDelayData.value.Marking?.[value]?.num || 0;
};
</script>
<template>
  <div class="h-full">
    <div class="flex w-full items-center">
      <a-tabs v-model:active-key="entry" class="ml-2 w-full">
        <a-tab-pane v-for="item in entryOptions" :key="item.value">
          <template #tab>
            <a-badge :count="getBadgeCount(item.value)" :offset="[15, -6]">
              {{ item.label }}
            </a-badge>
          </template>
        </a-tab-pane>
      </a-tabs>
    </div>
    <supervision-mark v-if="entry.includes('supervision')" :entry="entry" />
    <text-image-mark
      v-else-if="
        entry.includes('ai_mark_text_image') ||
        ['task_ai_forbid_user_text_image'].includes(entry)
      "
      :entry="entry"
    />
    <ai-text-mark
      v-else-if="
        entry.includes('ai_mark_online') ||
        entry.includes('remarking') ||
        [`task_ai_forbid_user`].includes(entry)
      "
      :enable-keywords="![`task_ai_forbid_user`].includes(entry)"
      :show-level="true"
      :entry="entry"
    />
    <div v-else class="flex h-full items-center justify-center">
      <span class="text-gray-500">暂不支持该类型的标注任务</span>
    </div>
  </div>
</template>
<style scoped>
:deep(.ant-tabs-nav) {
  margin-bottom: 0px;
}
</style>
