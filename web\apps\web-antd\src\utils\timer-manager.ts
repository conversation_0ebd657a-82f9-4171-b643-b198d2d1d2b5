import { refreshSign } from '#/api/account';
import { useUserStore } from '#/store/user';
import { useAuthStore } from '#/store';
import { setItem, getItem } from '#/utils/save';
import { getCookie } from '#/utils/auth';

// 定时器管理器
class TimerManager {
  private refreshSignIntervalId: NodeJS.Timeout | null = null;
  private checkActiveTimeIntervalId: NodeJS.Timeout | null = null;
  private isLoggingOut = false;
  
  // 刷新签名的时间间隔（30分钟）
  private readonly REFRESH_SIGN_INTERVAL = 30 * 60 * 1000;
  // 检查活动时间的间隔（1分钟）
  private readonly CHECK_ACTIVE_TIME_INTERVAL = 60 * 1000;
  // 用户活动超时时间（40分钟）
  private readonly USER_ACTIVITY_TIMEOUT = 40 * 60 * 1000;

  /**
   * 启动所有定时器
   */
  startTimers() {
    this.startRefreshSignTimer();
    this.startCheckActiveTimeTimer();
  }

  /**
   * 停止所有定时器
   */
  stopTimers() {
    this.stopRefreshSignTimer();
    this.stopCheckActiveTimeTimer();
  }

  /**
   * 设置登出状态
   */
  setLoggingOut(status: boolean) {
    this.isLoggingOut = status;
  }

  /**
   * 启动刷新签名定时器
   */
  private startRefreshSignTimer() {
    if (this.refreshSignIntervalId) {
      clearInterval(this.refreshSignIntervalId);
    }

    this.refreshSignIntervalId = setInterval(async () => {
      // 如果正在登出，停止执行
      if (this.isLoggingOut) {
        return;
      }

      const userStore = useUserStore();
      
      // 检查用户是否已登录
      if (userStore.isLoginStatus) {
        try {
          await refreshSign({ user_id: userStore.userid });
          if (getCookie("signed_payload")) {
            setItem("SIGNED-PAYLOAD", JSON.parse(getCookie("signed_payload")!));
          }
          setItem("SIGNATURE", getCookie("signature"));
        } catch (error) {
          // 如果刷新签名失败（比如401），停止继续刷新
          console.warn("刷新签名失败，可能用户已登出:", error);
          this.stopRefreshSignTimer();
        }
      } else {
        // 用户未登录，停止刷新签名
        this.stopRefreshSignTimer();
      }
    }, this.REFRESH_SIGN_INTERVAL);
  }

  /**
   * 启动检查活动时间定时器
   */
  private startCheckActiveTimeTimer() {
    if (this.checkActiveTimeIntervalId) {
      clearInterval(this.checkActiveTimeIntervalId);
    }

    this.checkActiveTimeIntervalId = setInterval(() => {
      // 如果正在登出，停止执行
      if (this.isLoggingOut) {
        return;
      }

      const userStore = useUserStore();
      
      // 检查用户是否已登录
      if (userStore.isLoginStatus) {
        const lastActiveTime = localStorage.getItem("lastActiveTime");
        if (lastActiveTime) {
          const now = Date.now();
          // 如果距离最后一次操作时间超过 40 分钟，清除登录信息
          if (now - Number(lastActiveTime) >= this.USER_ACTIVITY_TIMEOUT) {
            this.handleUserTimeout();
          }
        }
      } else {
        // 用户未登录，停止检查活动时间
        this.stopCheckActiveTimeTimer();
      }
    }, this.CHECK_ACTIVE_TIME_INTERVAL);
  }

  /**
   * 处理用户活动超时
   */
  private handleUserTimeout() {
    if (this.isLoggingOut) {
      return;
    }

    this.isLoggingOut = true;
    
    // 立即停止所有定时器
    this.stopTimers();
    
    // 执行登出
    const authStore = useAuthStore();
    authStore.logout().finally(() => {
      this.isLoggingOut = false;
    });
  }

  /**
   * 停止刷新签名定时器
   */
  private stopRefreshSignTimer() {
    if (this.refreshSignIntervalId) {
      clearInterval(this.refreshSignIntervalId);
      this.refreshSignIntervalId = null;
    }
  }

  /**
   * 停止检查活动时间定时器
   */
  private stopCheckActiveTimeTimer() {
    if (this.checkActiveTimeIntervalId) {
      clearInterval(this.checkActiveTimeIntervalId);
      this.checkActiveTimeIntervalId = null;
    }
  }
}

// 创建单例实例
export const timerManager = new TimerManager();

/**
 * 更新最后活动时间
 */
export function updateLastActiveTime() {
  setItem("lastActiveTime", Date.now());
}
