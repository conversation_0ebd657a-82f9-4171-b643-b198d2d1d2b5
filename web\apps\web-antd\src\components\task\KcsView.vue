<script lang="ts" setup>
import type { ImageItem, Keyword } from '#/types/task';

import { computed, onUnmounted, ref, useTemplateRef, watch } from 'vue';
import { useRoute } from 'vue-router';

import { IconifyIcon } from '@vben/icons';
import { withDefault } from '@vben/utils';

import { getKeywordsWordLabels } from '#/api/operateLog';
import {
  getExpireUrl,
  getExpireUrlBatch,
  getUrlData,
  linkToPrivate,
} from '#/api/preview';
import ImageMasonry from '#/components/common/ImageMasonry.vue';
import KeywordsJump from '#/components/task/KeywordsJump.vue';
import { MediaType, useTaskMediaType } from '#/composables/task/use-task-media-type';
import {
  useLoadingState,
  ViewStatus,
} from '#/composables/ui/use-loading-state';
import { removeFileExtension } from '#/utils/transform';

import MediaView from './MediaView.vue';
import MonacoEditor, { type MonacoEditorInstance } from './MonacoEditor.vue';
/**
 * 属性
 */
const props = defineProps<{
  data: FileViewData;
}>();
const emit = defineEmits(['loaded']);
const route = useRoute();
/**
 * 响应式变量
 */
// monaco编辑器引用
const editorRef = useTemplateRef<MonacoEditorInstance>('editorRef');
const ocrEditors = useTemplateRef<MonacoEditorInstance[]>('ocrEditors');
const previewBox = useTemplateRef<HTMLElement>('previewBox');
const toggle = ref(true);
const text = ref('');
const keywords = ref<Keyword[]>([]);
const mediaType = useTaskMediaType();
const ocrKeywords = ref<Keyword[]>([]);
const ocrTexts = ref<
  {
    bucket?: string;
    key?: string;
    keyword: Keyword[];
    text: string;
    url?: string;
  }[]
>([]);
const images = ref<ImageItem[]>([]);
// const imageParams = ref<{ bucket: string; key: string; user_id: string }[]>([]);
const urls = ref<{ clicked: boolean; value: string }[]>([]);
const videoUrls = ref<{ ism3u8: boolean; src: string }[]>([]);
const audioUrls = ref<string[]>([]);
const keywordsLabels = ref([]);
const imageTimer = ref<NodeJS.Timeout | null>(null);
const { isLoading, setLoading, setLoaded } = useLoadingState(ViewStatus.UNLOAD);

const ism3u8 = (url: string) => {
  // 空字符串，直接返回
  if (!url) return false;
  const part = url.split('?');
  return part[0]?.endsWith('.m3u8');
};
/**
 * 关键词点击事件处理函数，点击关键词触发关键词位置定位
 * @param keyword 关键词对象
 */
const clickKeyword = (keyword: Keyword) => {
  if (!editorRef.value || !keyword.ranges) return;
  // 获取编辑器的HTMLElement
  const topOffset = editorRef.value.editorContainer?.offsetTop || 0;
  previewBox.value?.scrollTo({
    top: topOffset,
    behavior: 'smooth',
  });
  const range = keyword.ranges[0];
  if (!range) return;
  // 编辑器画面切换到关键词所在位置
  editorRef.value.focus({
    lineNumber: range.start.row,
    column: range.start.column,
  });
};
/**
 * 点击OCR关键词事件处理函数，点击关键词触发关键词位置定位
 * @param keyword 关键词对象
 */
const clickOcrKeyword = (keyword: Keyword) => {
  if (!ocrEditors.value) return;
  const index = ocrEditors.value.findIndex((item) => {
    return item.keywords.find((item: Keyword) => item.word === keyword.word);
  });
  if (index === -1) return;
  ocrEditors.value[index]?.editorContainer?.scrollIntoView({
    behavior: 'smooth',
  });
  const range = keyword.ranges[0];
  if (!range) return;
  // 编辑器画面切换到关键词所在位置
  ocrEditors.value[index]?.focus({
    lineNumber: range.start.row,
    column: range.start.column,
  });
};
const getViewtext = async () => {
  if (!props.data.url) return;
  const resp = await getExpireUrl({
    key: props.data.url,
    url_bucket: props.data.url_bucket,
  });
  resp.inspect(async (data: string) => {
    text.value = await getUrlData(data, 'text');
  });
};
/**
 * 获取附加数据，如url,图片，关键词列表等
 */
const getExtraData = async () => {
  images.value = [];
  // 猜测getExpierUrl请求的目的可能是为了刷新url的过期时间
  if (!props.data.extra_url) return;
  const resp = await getExpireUrl({
    key: props.data.extra_url,
    url_bucket: props.data.url_bucket,
  });
  if (!resp || !resp.result) {
    return;
  }
  const extra = await getUrlData(resp.data);
  keywords.value = withDefault(extra.keywords, []);

  // 处理图片数据
  if (extra.images && extra.images.length > 0) {
    images.value = extra.images;
  }

  // url列表，给每个url添加一个clicked字段，实现点击链接后变灰的效果
  urls.value = withDefault(extra.urls, [])
    .filter((item: string) => !item.includes('kdocs.cn'))
    .map((item: string) => ({
      value: item,
      clicked: false,
    }));
  console.log('extra', extra);
  if (extra.videos) {
    videoUrls.value = extra.videos.map((item: string) => {
      return {
        src: item,
        ism3u8: ism3u8(item),
      };
    });
  }
  audioUrls.value = extra.audios || [];
};

/**
 * 获取OCR关键词数据
 */
const getOcrKeywordData = async () => {
  if (!props.data.ocr_url) {
    return;
  }
  const resp = await getExpireUrl({
    key: props.data.ocr_url,
    url_bucket: props.data.url_bucket,
  });
  resp.inspect(async (data) => {
    const resp = await getUrlData(data);
    resp.ocr_texts.forEach((item: any) => {
      ocrKeywords.value.push(...item.keyword);
    });

    ocrTexts.value = resp.ocr_texts;
  });
};

const getTargetURL = async (href: string) => {
  const dom = href.split('//');
  if (dom.length < 2) {
    return href;
  }
  const domain = dom[1]?.split('/')[0];
  if (
    domain !== 'docs.wps.cn' &&
    domain !== 'www.kdocs.cn' &&
    domain !== 'kdocs.cn'
  ) {
    return href;
  }
  const params = { url: href };
  const res = await linkToPrivate(params);
  if (res && res.data && !res.data.message) {
    return res.data.data.url ?? href;
  } else {
    let cb = encodeURIComponent(href);
    const t = Date.now();
    cb =
      href.split('?').length > 1
        ? encodeURIComponent(`${href}&disablePlugins=true`)
        : encodeURIComponent(`${href}?disablePlugins=true`);
    const url = `https://${domain}/logout?t=${t}&cb=${cb}`;
    return url;
  }
};
const clickURL = async (url: { clicked: boolean; value: string }) => {
  url.clicked = true;
  window.open(await getTargetURL(url.value));
};

const openAllOrTop20URL = () => {
  urls.value.slice(0, 20).forEach(async (url) => {
    url.clicked = true;
    window.open(await getTargetURL(url.value));
  });
};
const getKeywordsLabelData = async () => {
  const labelsResponseData = await getKeywordsWordLabels();
  keywordsLabels.value = labelsResponseData.data.labels;
};

/**
 * 计算属性
 */
const fileNameWithoutExtension = computed(() => {
  return removeFileExtension(props.data.fname);
});

const resetup = () => {
  images.value = [];
  text.value = '';
  ocrTexts.value = [];
  ocrKeywords.value = [];
  keywords.value = [];
  urls.value = [];
  videoUrls.value = [];
};

const fetchData = async () => {
  resetup();
  await getViewtext();
  await getExtraData();
  await getOcrKeywordData();
  if (!keywordsLabels.value || keywordsLabels.value.length === 0) {
    await getKeywordsLabelData();
  }
  if (images.value.length <= 1) {
    emit('loaded');
    setLoaded();
  } else if (images.value.length > 1) {
    // 图片数量大于一张时，延迟2秒发射loaded事件
    imageTimer.value = setTimeout(() => {
      emit('loaded');
      setLoaded();
    }, 2000);
  }
};
watch(
  props.data,
  () => {
    if (props.data.url) {
      fetchData();
    }
  },
  { immediate: true },
);
onUnmounted(() => {
  if (imageTimer.value) {
    clearTimeout(imageTimer.value);
  }
});
watch(
  () => props.data,
  () => {
    setLoading();
    fetchData();
  },
);
/**
 * 按需加载图片URL
 * @param params 图片参数数组
 * @returns 图片URL数组
 */
const loadImageUrls = async (params: ImageItem[]) => {
  const imageResponse = await getExpireUrlBatch(params);

  // 直接处理响应数据，不使用inspect
  if (imageResponse.result && imageResponse.data) {
    const urls = imageResponse.data.items
      .map((item: any) => item.url)
      .filter(Boolean);
    return urls;
  }
  return [];
};
</script>

<template>
  <div
    :class="
      route.name === 'Preview' ? 'max-w-[100vw]' : 'max-w-[calc(100vw-400px)]'
    "
    class="flex h-full flex-1 flex-col overflow-hidden"
  >
    <div
      class="flex h-[50px] items-center justify-between pl-2 text-2xl font-bold"
    >
      <div
        class="max-w-full overflow-hidden truncate text-ellipsis whitespace-nowrap"
        v-html="fileNameWithoutExtension"
      ></div>
    </div>
    <div class="relative flex h-[calc(100%-50px)] w-full overflow-auto">
      <div
        v-if="toggle && mediaType===MediaType.NotSupport"
        class="absolute left-0 top-0 z-10 max-h-[100%] w-[155px] overflow-auto border-r border-t"
      >
        <iconify-icon
          class="absolute right-1 top-3 cursor-pointer"
          icon="ant-design:left-outlined"
          @click="toggle = !toggle"
        />
        <keywords-jump
          :is-loading="isLoading"
          :keywords="keywords"
          :labels="keywordsLabels"
          title="关键词列表"
          @click="clickKeyword"
        />
        <keywords-jump
          :is-loading="isLoading"
          :keywords="ocrKeywords"
          :labels="keywordsLabels"
          title="OCR关键词列表"
          @click="clickOcrKeyword"
        />
      </div>
      <a-button
        v-else-if="toggle && mediaType===MediaType.NotSupport"
        class="absolute left-0 top-3 z-10 rounded-l-none border-l-0 p-0 px-1"
        @click="toggle = !toggle"
      >
        ≡
      </a-button>
      <div
        ref="previewBox"
        :class="{ 'ml-[155px]': toggle && mediaType===MediaType.NotSupport }"
        class="flex flex-1 flex-col overflow-auto border-l border-t px-20"
        v-loading="{ active: isLoading, text: '加载中...' }"
      >
        <div class="mb-4">
          <a-button
            v-if="urls.length > 0"
            type="link"
            @click="openAllOrTop20URL"
          >
            {{
              urls.length > 0 && urls.length > 20
                ? '打开前20个链接'
                : '打开所有'
            }}
          </a-button>
          <div class="ml-6 flex flex-col items-start">
            <a-button
              v-for="(url, index) in urls"
              :key="index"
              :class="{ 'text-gray-400': url.clicked }"
              class="max-w-[calc(100%-10px)] overflow-hidden overflow-ellipsis"
              type="link"
              @click="clickURL(url)"
            >
              {{ url.value }}
            </a-button>
          </div>
        </div>

        <monaco-editor
          v-if="text"
          ref="editorRef"
          :keywords="keywords"
          :value="text"
          class="min-h-[300px] w-full flex-shrink-0"
        />
        <div>
          <template v-if="ocrTexts.length > 0">
            <monaco-editor
              v-for="(item, index) in ocrTexts"
              :key="index"
              ref="ocrEditors"
              :image-bucket="item.bucket"
              :image-key="item.key"
              :image-url="item.url"
              :keywords="item.keyword"
              :value="item.text"
              class="mt-4 min-h-[300px] w-full flex-shrink-0"
              tag="OCR"
            />
          </template>
        </div>
        <media-view
          v-if="videoUrls.length + audioUrls.length > 0"
          :audio-urls="audioUrls"
          :media-type="mediaType"
          :video-urls="videoUrls"
          class="mt-4"
        />

        <div v-if="images.length > 0" class="mt-12 p-2">
          <image-masonry
            :batch-size="10"
            :column-width="300"
            :gap="40"
            :images="images"
            :on-load-images="loadImageUrls"
            :root-ref="previewBox"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<style>
:deep(.ant-image) {
  display: flex;
}

</style>
